import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import {
  GameState,
  GameComponent,
  ComponentType,
  Position,
  Direction,
  ResourceType,
  PerformanceMetrics,
  COMPONENT_DEFINITIONS,
  RECIPES,
} from '@/types/game';
import { generateId } from '@/utils/helpers';
import { SimulationEngine } from '@/engine/simulation';

interface GameStore extends GameState {
  // Actions
  addComponent: (type: ComponentType, position: Position, direction?: Direction) => string | null;
  removeComponent: (id: string) => void;
  moveComponent: (id: string, position: Position) => void;
  rotateComponent: (id: string) => void;
  connectComponents: (fromId: string, toId: string) => boolean;
  disconnectComponents: (fromId: string, toId: string) => void;
  setComponentRecipe: (id: string, recipeId: string) => void;
  toggleSimulation: () => void;
  updateSimulation: () => void;
  getPerformanceMetrics: () => PerformanceMetrics;
  exportGameState: () => string;
  importGameState: (jsonState: string) => boolean;
  resetGame: () => void;
}

const GRID_SIZE = { width: 50, height: 50 };
const simulationEngine = new SimulationEngine();

const initialState: GameState = {
  components: new Map(),
  gridSize: GRID_SIZE,
  isRunning: false,
  gameTime: 0,
  resources: new Map([
    [ResourceType.IRON_ORE, 1000],
    [ResourceType.COPPER_ORE, 1000],
    [ResourceType.COAL, 1000],
    [ResourceType.IRON_PLATE, 100],
    [ResourceType.COPPER_PLATE, 100],
    [ResourceType.GEAR, 50],
    [ResourceType.CIRCUIT, 50],
  ]),
  statistics: {
    totalProduction: new Map(),
    totalConsumption: new Map(),
    efficiency: 0,
    bottlenecks: [],
  },
};

export const useGameStore = create<GameStore>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    addComponent: (type: ComponentType, position: Position, direction = Direction.NORTH) => {
      const state = get();
      const definition = COMPONENT_DEFINITIONS[type];
      
      // Check if position is valid and not occupied
      if (!isPositionValid(position, definition.size, state)) {
        return null;
      }

      const id = generateId();
      const component: GameComponent = {
        id,
        type,
        position,
        direction,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: false,
        lastProcessTime: 0,
      };

      set((state) => ({
        components: new Map(state.components).set(id, component),
      }));

      return id;
    },

    removeComponent: (id: string) => {
      set((state) => {
        const newComponents = new Map(state.components);
        const component = newComponents.get(id);
        
        if (component) {
          // Remove all connections to this component
          newComponents.forEach((comp) => {
            comp.connections.inputs = comp.connections.inputs.filter(connId => connId !== id);
            comp.connections.outputs = comp.connections.outputs.filter(connId => connId !== id);
          });
          
          newComponents.delete(id);
        }

        return { components: newComponents };
      });
    },

    moveComponent: (id: string, position: Position) => {
      set((state) => {
        const component = state.components.get(id);
        if (!component) return state;

        const definition = COMPONENT_DEFINITIONS[component.type];
        if (!isPositionValid(position, definition.size, state, id)) {
          return state;
        }

        const newComponents = new Map(state.components);
        newComponents.set(id, { ...component, position });
        return { components: newComponents };
      });
    },

    rotateComponent: (id: string) => {
      set((state) => {
        const component = state.components.get(id);
        if (!component) return state;

        const newDirection = (component.direction + 1) % 4;
        const newComponents = new Map(state.components);
        newComponents.set(id, { ...component, direction: newDirection });
        return { components: newComponents };
      });
    },

    connectComponents: (fromId: string, toId: string) => {
      const state = get();
      const fromComponent = state.components.get(fromId);
      const toComponent = state.components.get(toId);

      if (!fromComponent || !toComponent || fromId === toId) {
        return false;
      }

      const fromDef = COMPONENT_DEFINITIONS[fromComponent.type];
      const toDef = COMPONENT_DEFINITIONS[toComponent.type];

      // Check connection limits
      if (fromComponent.connections.outputs.length >= fromDef.maxOutputs ||
          toComponent.connections.inputs.length >= toDef.maxInputs) {
        return false;
      }

      // Check if already connected
      if (fromComponent.connections.outputs.includes(toId)) {
        return false;
      }

      set((state) => {
        const newComponents = new Map(state.components);
        const newFromComponent = { ...fromComponent };
        const newToComponent = { ...toComponent };

        newFromComponent.connections.outputs.push(toId);
        newToComponent.connections.inputs.push(fromId);

        newComponents.set(fromId, newFromComponent);
        newComponents.set(toId, newToComponent);

        return { components: newComponents };
      });

      return true;
    },

    disconnectComponents: (fromId: string, toId: string) => {
      set((state) => {
        const newComponents = new Map(state.components);
        const fromComponent = newComponents.get(fromId);
        const toComponent = newComponents.get(toId);

        if (fromComponent && toComponent) {
          fromComponent.connections.outputs = fromComponent.connections.outputs.filter(id => id !== toId);
          toComponent.connections.inputs = toComponent.connections.inputs.filter(id => id !== fromId);
        }

        return { components: newComponents };
      });
    },

    setComponentRecipe: (id: string, recipeId: string) => {
      set((state) => {
        const component = state.components.get(id);
        if (!component) return state;

        const recipe = RECIPES[recipeId];
        if (!recipe) return state;

        const newComponents = new Map(state.components);
        newComponents.set(id, { ...component, recipe });
        return { components: newComponents };
      });
    },

    toggleSimulation: () => {
      set((state) => ({ isRunning: !state.isRunning }));
    },

    updateSimulation: () => {
      const state = get();
      if (!state.isRunning) return;

      const updates = simulationEngine.updateSimulation(state);
      if (Object.keys(updates).length > 0) {
        set((currentState) => ({ ...currentState, ...updates }));
      }
    },

    getPerformanceMetrics: (): PerformanceMetrics => {
      const state = get();
      return {
        throughput: state.statistics.totalProduction,
        utilization: new Map(),
        bottlenecks: state.statistics.bottlenecks,
        efficiency: state.statistics.efficiency,
      };
    },

    exportGameState: () => {
      const state = get();
      const exportData = {
        components: Array.from(state.components.entries()),
        gridSize: state.gridSize,
        gameTime: state.gameTime,
        resources: Array.from(state.resources.entries()),
      };
      return JSON.stringify(exportData, null, 2);
    },

    importGameState: (jsonState: string) => {
      try {
        const data = JSON.parse(jsonState);
        set({
          components: new Map(data.components),
          gridSize: data.gridSize || GRID_SIZE,
          gameTime: data.gameTime || 0,
          resources: new Map(data.resources),
          isRunning: false,
        });
        return true;
      } catch (error) {
        console.error('Failed to import game state:', error);
        return false;
      }
    },

    resetGame: () => {
      set(initialState);
    },
  }))
);

// Helper functions
function isPositionValid(
  position: Position,
  size: { width: number; height: number },
  state: GameState,
  excludeId?: string
): boolean {
  // Check bounds
  if (position.x < 0 || position.y < 0 ||
      position.x + size.width > state.gridSize.width ||
      position.y + size.height > state.gridSize.height) {
    return false;
  }

  // Check for overlaps with existing components
  for (const [id, component] of state.components) {
    if (excludeId && id === excludeId) continue;
    
    const compDef = COMPONENT_DEFINITIONS[component.type];
    const compPos = component.position;
    
    // Check if rectangles overlap
    if (!(position.x >= compPos.x + compDef.size.width ||
          position.x + size.width <= compPos.x ||
          position.y >= compPos.y + compDef.size.height ||
          position.y + size.height <= compPos.y)) {
      return false;
    }
  }

  return true;
}
