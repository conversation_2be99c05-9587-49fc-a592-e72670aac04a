'use client';

import React, { useState } from 'react';
import { useGameStore } from '@/store/gameStore';
import { ResourceType, ComponentType } from '@/types/game';
import { formatNumber, formatTime } from '@/utils/helpers';
import {
  BarChart3,
  Clock,
  Zap,
  AlertTriangle,
  ChevronDown,
  ChevronRight,
  Download,
  Upload,
  RotateCcw,
  Cog,
  Package
} from 'lucide-react';
import clsx from 'clsx';

interface StatsSectionProps {
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  isExpanded: boolean;
  onToggle: () => void;
}

const StatsSection: React.FC<StatsSectionProps> = ({ 
  title, 
  icon, 
  children, 
  isExpanded, 
  onToggle 
}) => {
  return (
    <div className="mb-4">
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
      >
        <div className="flex items-center gap-2">
          {icon}
          <span className="font-semibold text-white">{title}</span>
        </div>
        {isExpanded ? (
          <ChevronDown className="w-4 h-4 text-gray-300" />
        ) : (
          <ChevronRight className="w-4 h-4 text-gray-300" />
        )}
      </button>
      
      {isExpanded && (
        <div className="mt-2 p-3 bg-gray-800 rounded-lg">
          {children}
        </div>
      )}
    </div>
  );
};

const ResourceDisplay: React.FC<{ type: ResourceType; amount: number }> = ({ type, amount }) => {
  const getResourceColor = (type: ResourceType) => {
    switch (type) {
      case ResourceType.IRON_ORE:
        return 'text-gray-400';
      case ResourceType.COPPER_ORE:
        return 'text-orange-400';
      case ResourceType.COAL:
        return 'text-gray-600';
      case ResourceType.IRON_PLATE:
        return 'text-gray-300';
      case ResourceType.COPPER_PLATE:
        return 'text-orange-300';
      case ResourceType.GEAR:
        return 'text-yellow-400';
      case ResourceType.CIRCUIT:
        return 'text-green-400';
      default:
        return 'text-white';
    }
  };

  const getResourceName = (type: ResourceType) => {
    return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <div className="flex justify-between items-center py-1">
      <span className={clsx('text-sm', getResourceColor(type))}>
        {getResourceName(type)}
      </span>
      <span className="text-white font-mono text-sm">
        {formatNumber(amount)}
      </span>
    </div>
  );
};

const StatsPanel: React.FC = () => {
  const {
    resources,
    statistics,
    components,
    gameTime,
    isRunning,
    getPerformanceMetrics,
    exportGameState,
    importGameState,
    resetGame,
  } = useGameStore();

  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['Production', 'Resources'])
  );

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const metrics = getPerformanceMetrics();

  const handleExport = () => {
    const data = exportGameState();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `factory-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          if (importGameState(content)) {
            alert('Factory imported successfully!');
          } else {
            alert('Failed to import factory. Please check the file format.');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  const componentCounts = Array.from(components.values()).reduce((acc, component) => {
    acc[component.type] = (acc[component.type] || 0) + 1;
    return acc;
  }, {} as Record<ComponentType, number>);

  return (
    <div className="h-full p-4 overflow-y-auto">
      <div className="mb-6">
        <h2 className="text-xl font-bold text-white mb-2">Factory Stats</h2>
        <div className="flex items-center gap-2 text-sm text-gray-300">
          <Clock className="w-4 h-4" />
          <span>Runtime: {formatTime(gameTime)}</span>
        </div>
        <div className="flex items-center gap-2 text-sm text-gray-300 mt-1">
          <Zap className="w-4 h-4" />
          <span className={isRunning ? 'text-green-400' : 'text-red-400'}>
            {isRunning ? 'Running' : 'Paused'}
          </span>
        </div>
      </div>

      {/* Production Stats */}
      <StatsSection
        title="Production"
        icon={<BarChart3 className="w-4 h-4 text-blue-400" />}
        isExpanded={expandedSections.has('Production')}
        onToggle={() => toggleSection('Production')}
      >
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-300">Efficiency</span>
            <span className="text-white font-mono text-sm">
              {(statistics.efficiency * 100).toFixed(1)}%
            </span>
          </div>
          
          {Array.from(statistics.totalProduction.entries()).map(([resource, amount]) => (
            <ResourceDisplay key={resource} type={resource} amount={amount} />
          ))}
          
          {statistics.totalProduction.size === 0 && (
            <p className="text-gray-400 text-sm">No production data yet</p>
          )}
        </div>
      </StatsSection>

      {/* Resources */}
      <StatsSection
        title="Resources"
        icon={<Package className="w-4 h-4 text-green-400" />}
        isExpanded={expandedSections.has('Resources')}
        onToggle={() => toggleSection('Resources')}
      >
        <div className="space-y-1">
          {Array.from(resources.entries()).map(([resource, amount]) => (
            <ResourceDisplay key={resource} type={resource} amount={amount} />
          ))}
        </div>
      </StatsSection>

      {/* Components */}
      <StatsSection
        title="Components"
        icon={<Cog className="w-4 h-4 text-purple-400" />}
        isExpanded={expandedSections.has('Components')}
        onToggle={() => toggleSection('Components')}
      >
        <div className="space-y-1">
          {Object.entries(componentCounts).map(([type, count]) => (
            <div key={type} className="flex justify-between items-center py-1">
              <span className="text-sm text-gray-300 capitalize">
                {type.replace('_', ' ')}
              </span>
              <span className="text-white font-mono text-sm">{count}</span>
            </div>
          ))}
          {Object.keys(componentCounts).length === 0 && (
            <p className="text-gray-400 text-sm">No components placed</p>
          )}
        </div>
      </StatsSection>

      {/* Bottlenecks */}
      {statistics.bottlenecks.length > 0 && (
        <StatsSection
          title="Bottlenecks"
          icon={<AlertTriangle className="w-4 h-4 text-red-400" />}
          isExpanded={expandedSections.has('Bottlenecks')}
          onToggle={() => toggleSection('Bottlenecks')}
        >
          <div className="space-y-1">
            {statistics.bottlenecks.map((componentId, index) => (
              <div key={componentId} className="text-sm text-red-300">
                Component {index + 1}: {componentId.slice(0, 8)}...
              </div>
            ))}
          </div>
        </StatsSection>
      )}

      {/* Actions */}
      <div className="mt-8 space-y-2">
        <h3 className="font-semibold text-white mb-3">Actions</h3>
        
        <button
          onClick={handleExport}
          className="w-full flex items-center gap-2 p-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
        >
          <Download className="w-4 h-4" />
          Export Factory
        </button>
        
        <button
          onClick={handleImport}
          className="w-full flex items-center gap-2 p-2 bg-green-600 hover:bg-green-700 text-white rounded transition-colors"
        >
          <Upload className="w-4 h-4" />
          Import Factory
        </button>
        
        <button
          onClick={() => {
            if (confirm('Are you sure you want to reset the factory? This cannot be undone.')) {
              resetGame();
            }
          }}
          className="w-full flex items-center gap-2 p-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
        >
          <RotateCcw className="w-4 h-4" />
          Reset Factory
        </button>
      </div>

      {/* Performance Metrics */}
      <div className="mt-6 p-3 bg-gray-800 rounded-lg">
        <h3 className="font-semibold text-white mb-2">Performance</h3>
        <div className="text-xs text-gray-300 space-y-1">
          <div>Components: {components.size}</div>
          <div>Active: {Array.from(components.values()).filter(c => c.isActive).length}</div>
          <div>Efficiency: {(metrics.efficiency * 100).toFixed(1)}%</div>
        </div>
      </div>
    </div>
  );
};

export default StatsPanel;
