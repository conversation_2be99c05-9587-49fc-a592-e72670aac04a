// Core game types and interfaces

export interface Position {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export enum ComponentType {
  CONVEYOR = 'conveyor',
  MINER = 'miner',
  ASSEMBLER = 'assembler',
  STORAGE = 'storage',
  SPLITTER = 'splitter',
  MERGER = 'merger',
}

export enum ResourceType {
  IRON_ORE = 'iron_ore',
  COPPER_ORE = 'copper_ore',
  COAL = 'coal',
  IRON_PLATE = 'iron_plate',
  COPPER_PLATE = 'copper_plate',
  GEAR = 'gear',
  CIRCUIT = 'circuit',
}

export enum Direction {
  NORTH = 0,
  EAST = 1,
  SOUTH = 2,
  WEST = 3,
}

export interface Recipe {
  id: string;
  name: string;
  inputs: { resource: ResourceType; amount: number }[];
  outputs: { resource: ResourceType; amount: number }[];
  processingTime: number; // in milliseconds
}

export interface ComponentDefinition {
  type: ComponentType;
  name: string;
  description: string;
  size: Size;
  maxInputs: number;
  maxOutputs: number;
  recipes?: Recipe[];
  speed: number; // items per second
  cost: { resource: ResourceType; amount: number }[];
}

export interface GameComponent {
  id: string;
  type: ComponentType;
  position: Position;
  direction: Direction;
  recipe?: Recipe;
  inventory: Map<ResourceType, number>;
  connections: {
    inputs: string[]; // IDs of connected components
    outputs: string[]; // IDs of connected components
  };
  isActive: boolean;
  lastProcessTime: number;
}

export interface GameState {
  components: Map<string, GameComponent>;
  gridSize: Size;
  isRunning: boolean;
  gameTime: number;
  resources: Map<ResourceType, number>;
  statistics: {
    totalProduction: Map<ResourceType, number>;
    totalConsumption: Map<ResourceType, number>;
    efficiency: number;
    bottlenecks: string[];
  };
}

export interface PerformanceMetrics {
  throughput: Map<ResourceType, number>; // items per minute
  utilization: Map<string, number>; // component ID -> utilization percentage
  bottlenecks: string[]; // component IDs that are bottlenecks
  efficiency: number; // overall factory efficiency (0-1)
}

// Component definitions for the game
export const COMPONENT_DEFINITIONS: Record<ComponentType, ComponentDefinition> = {
  [ComponentType.CONVEYOR]: {
    type: ComponentType.CONVEYOR,
    name: 'Conveyor Belt',
    description: 'Transports items between components',
    size: { width: 1, height: 1 },
    maxInputs: 1,
    maxOutputs: 1,
    speed: 15, // items per second
    cost: [{ resource: ResourceType.IRON_PLATE, amount: 1 }],
  },
  [ComponentType.MINER]: {
    type: ComponentType.MINER,
    name: 'Mining Drill',
    description: 'Extracts raw resources from the ground',
    size: { width: 2, height: 2 },
    maxInputs: 0,
    maxOutputs: 1,
    speed: 0.5, // items per second
    cost: [
      { resource: ResourceType.IRON_PLATE, amount: 10 },
      { resource: ResourceType.GEAR, amount: 5 },
    ],
  },
  [ComponentType.ASSEMBLER]: {
    type: ComponentType.ASSEMBLER,
    name: 'Assembling Machine',
    description: 'Crafts items from raw materials',
    size: { width: 3, height: 3 },
    maxInputs: 2,
    maxOutputs: 1,
    speed: 0.75, // crafting speed multiplier
    cost: [
      { resource: ResourceType.IRON_PLATE, amount: 9 },
      { resource: ResourceType.GEAR, amount: 5 },
      { resource: ResourceType.CIRCUIT, amount: 3 },
    ],
  },
  [ComponentType.STORAGE]: {
    type: ComponentType.STORAGE,
    name: 'Storage Chest',
    description: 'Stores items for later use',
    size: { width: 1, height: 1 },
    maxInputs: 1,
    maxOutputs: 1,
    speed: 30, // items per second throughput
    cost: [{ resource: ResourceType.IRON_PLATE, amount: 8 }],
  },
  [ComponentType.SPLITTER]: {
    type: ComponentType.SPLITTER,
    name: 'Splitter',
    description: 'Splits input into multiple outputs',
    size: { width: 2, height: 1 },
    maxInputs: 1,
    maxOutputs: 2,
    speed: 15, // items per second
    cost: [
      { resource: ResourceType.IRON_PLATE, amount: 5 },
      { resource: ResourceType.CIRCUIT, amount: 5 },
    ],
  },
  [ComponentType.MERGER]: {
    type: ComponentType.MERGER,
    name: 'Merger',
    description: 'Merges multiple inputs into one output',
    size: { width: 2, height: 1 },
    maxInputs: 2,
    maxOutputs: 1,
    speed: 15, // items per second
    cost: [
      { resource: ResourceType.IRON_PLATE, amount: 5 },
      { resource: ResourceType.CIRCUIT, amount: 5 },
    ],
  },
};

// Recipe definitions
export const RECIPES: Record<string, Recipe> = {
  iron_plate: {
    id: 'iron_plate',
    name: 'Iron Plate',
    inputs: [{ resource: ResourceType.IRON_ORE, amount: 1 }],
    outputs: [{ resource: ResourceType.IRON_PLATE, amount: 1 }],
    processingTime: 3200, // 3.2 seconds
  },
  copper_plate: {
    id: 'copper_plate',
    name: 'Copper Plate',
    inputs: [{ resource: ResourceType.COPPER_ORE, amount: 1 }],
    outputs: [{ resource: ResourceType.COPPER_PLATE, amount: 1 }],
    processingTime: 3200, // 3.2 seconds
  },
  gear: {
    id: 'gear',
    name: 'Iron Gear Wheel',
    inputs: [{ resource: ResourceType.IRON_PLATE, amount: 2 }],
    outputs: [{ resource: ResourceType.GEAR, amount: 1 }],
    processingTime: 500, // 0.5 seconds
  },
  circuit: {
    id: 'circuit',
    name: 'Electronic Circuit',
    inputs: [
      { resource: ResourceType.IRON_PLATE, amount: 1 },
      { resource: ResourceType.COPPER_PLATE, amount: 3 },
    ],
    outputs: [{ resource: ResourceType.CIRCUIT, amount: 1 }],
    processingTime: 500, // 0.5 seconds
  },
};
