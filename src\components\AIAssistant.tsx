'use client';

import React, { useState } from 'react';
import { useGameStore } from '@/store/gameStore';
import { GameStateSerializer } from '@/ai/gameStateSerializer';
import { 
  Brain, 
  Copy, 
  Download, 
  Upload, 
  Loader2,
  CheckCircle,
  AlertCircle,
  MessageSquare
} from 'lucide-react';
import clsx from 'clsx';

interface AIAnalysisResult {
  serializedState: any;
  aiPrompt: string;
  metadata: {
    componentCount: number;
    efficiency: number;
    isRunning: boolean;
    timestamp: number;
  };
}

const AIAssistant: React.FC = () => {
  const { 
    components, 
    resources, 
    statistics, 
    gridSize, 
    gameTime, 
    isRunning,
    getFactoryAnalytics 
  } = useGameStore();

  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AIAnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [copySuccess, setCopySuccess] = useState(false);

  const handleAnalyze = async () => {
    setIsAnalyzing(true);
    setError(null);

    try {
      const gameState = {
        components,
        resources,
        statistics,
        gridSize,
        gameTime,
        isRunning,
      };

      const analytics = getFactoryAnalytics();

      const response = await fetch('/api/ai/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          gameState,
          analytics,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to analyze factory');
      }

      const result = await response.json();
      setAnalysisResult(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleCopyPrompt = async () => {
    if (!analysisResult) return;

    try {
      await navigator.clipboard.writeText(analysisResult.aiPrompt);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      setError('Failed to copy to clipboard');
    }
  };

  const handleDownloadState = () => {
    if (!analysisResult) return;

    const blob = new Blob([JSON.stringify(analysisResult.serializedState, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `factory-state-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleDownloadPrompt = () => {
    if (!analysisResult) return;

    const blob = new Blob([analysisResult.aiPrompt], {
      type: 'text/plain',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `factory-analysis-${Date.now()}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="bg-gray-800 rounded-lg p-4">
      <div className="flex items-center gap-2 mb-4">
        <Brain className="w-5 h-5 text-purple-400" />
        <h3 className="text-lg font-semibold text-white">AI Assistant</h3>
      </div>

      <div className="space-y-4">
        <div className="text-sm text-gray-300">
          Generate an AI-friendly analysis of your factory for optimization suggestions.
        </div>

        <button
          onClick={handleAnalyze}
          disabled={isAnalyzing || components.size === 0}
          className={clsx(
            'w-full flex items-center justify-center gap-2 p-3 rounded-lg font-medium transition-colors',
            {
              'bg-purple-600 hover:bg-purple-700 text-white': !isAnalyzing && components.size > 0,
              'bg-gray-600 text-gray-400 cursor-not-allowed': isAnalyzing || components.size === 0,
            }
          )}
        >
          {isAnalyzing ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              Analyzing Factory...
            </>
          ) : (
            <>
              <MessageSquare className="w-4 h-4" />
              Generate AI Analysis
            </>
          )}
        </button>

        {components.size === 0 && (
          <div className="text-sm text-yellow-400 flex items-center gap-2">
            <AlertCircle className="w-4 h-4" />
            Add some components to your factory first
          </div>
        )}

        {error && (
          <div className="text-sm text-red-400 flex items-center gap-2">
            <AlertCircle className="w-4 h-4" />
            {error}
          </div>
        )}

        {analysisResult && (
          <div className="space-y-4 border-t border-gray-700 pt-4">
            <div className="flex items-center gap-2 text-green-400">
              <CheckCircle className="w-4 h-4" />
              <span className="text-sm">Analysis Complete</span>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="bg-gray-700 rounded p-3">
                <div className="text-gray-300">Components</div>
                <div className="text-white font-semibold">
                  {analysisResult.metadata.componentCount}
                </div>
              </div>
              <div className="bg-gray-700 rounded p-3">
                <div className="text-gray-300">Efficiency</div>
                <div className="text-white font-semibold">
                  {(analysisResult.metadata.efficiency * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="text-sm font-medium text-white">Actions</div>
              
              <div className="grid grid-cols-2 gap-2">
                <button
                  onClick={handleCopyPrompt}
                  className="flex items-center justify-center gap-2 p-2 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors"
                >
                  {copySuccess ? (
                    <>
                      <CheckCircle className="w-4 h-4" />
                      Copied!
                    </>
                  ) : (
                    <>
                      <Copy className="w-4 h-4" />
                      Copy Prompt
                    </>
                  )}
                </button>

                <button
                  onClick={handleDownloadPrompt}
                  className="flex items-center justify-center gap-2 p-2 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors"
                >
                  <Download className="w-4 h-4" />
                  Download
                </button>
              </div>

              <button
                onClick={handleDownloadState}
                className="w-full flex items-center justify-center gap-2 p-2 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm transition-colors"
              >
                <Upload className="w-4 h-4" />
                Download State JSON
              </button>
            </div>

            <div className="text-xs text-gray-400">
              Use the copied prompt with any AI assistant (ChatGPT, Claude, etc.) to get optimization suggestions for your factory.
            </div>
          </div>
        )}

        <div className="border-t border-gray-700 pt-4">
          <div className="text-sm font-medium text-white mb-2">How to use:</div>
          <ol className="text-xs text-gray-300 space-y-1">
            <li>1. Build your factory with components</li>
            <li>2. Click "Generate AI Analysis"</li>
            <li>3. Copy the generated prompt</li>
            <li>4. Paste it into any AI assistant</li>
            <li>5. Get optimization suggestions!</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default AIAssistant;
