'use client';

import React from 'react';
import { Position } from '@/types/game';

interface GridOverlayProps {
  cellSize: number;
  offset: Position;
  width: number;
  height: number;
}

const GridOverlay: React.FC<GridOverlayProps> = ({ cellSize, offset, width, height }) => {
  const gridLines = [];

  // Calculate visible grid range
  const startX = Math.floor(-offset.x / cellSize) * cellSize;
  const startY = Math.floor(-offset.y / cellSize) * cellSize;
  const endX = startX + width + cellSize;
  const endY = startY + height + cellSize;

  // Vertical lines
  for (let x = startX; x <= endX; x += cellSize) {
    gridLines.push(
      <line
        key={`v-${x}`}
        x1={x}
        y1={startY}
        x2={x}
        y2={endY}
        stroke="rgba(255, 255, 255, 0.1)"
        strokeWidth="1"
      />
    );
  }

  // Horizontal lines
  for (let y = startY; y <= endY; y += cellSize) {
    gridLines.push(
      <line
        key={`h-${y}`}
        x1={startX}
        y1={y}
        x2={endX}
        y2={y}
        stroke="rgba(255, 255, 255, 0.1)"
        strokeWidth="1"
      />
    );
  }

  return (
    <svg
      className="absolute inset-0 pointer-events-none"
      style={{
        transform: `translate(${offset.x}px, ${offset.y}px)`,
      }}
    >
      {gridLines}
    </svg>
  );
};

export default GridOverlay;
