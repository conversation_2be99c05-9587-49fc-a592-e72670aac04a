import { Position, Direction, Size } from '@/types/game';

// Generate unique IDs for components
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

// Calculate distance between two positions
export function calculateDistance(pos1: Position, pos2: Position): number {
  return Math.sqrt(Math.pow(pos2.x - pos1.x, 2) + Math.pow(pos2.y - pos1.y, 2));
}

// Get direction vector from Direction enum
export function getDirectionVector(direction: Direction): Position {
  switch (direction) {
    case Direction.NORTH:
      return { x: 0, y: -1 };
    case Direction.EAST:
      return { x: 1, y: 0 };
    case Direction.SOUTH:
      return { x: 0, y: 1 };
    case Direction.WEST:
      return { x: -1, y: 0 };
    default:
      return { x: 0, y: 0 };
  }
}

// Get opposite direction
export function getOppositeDirection(direction: Direction): Direction {
  return (direction + 2) % 4;
}

// Check if two rectangles overlap
export function rectanglesOverlap(
  pos1: Position,
  size1: Size,
  pos2: Position,
  size2: Size
): boolean {
  return !(
    pos1.x >= pos2.x + size2.width ||
    pos1.x + size1.width <= pos2.x ||
    pos1.y >= pos2.y + size2.height ||
    pos1.y + size1.height <= pos2.y
  );
}

// Snap position to grid
export function snapToGrid(position: Position, gridSize: number = 1): Position {
  return {
    x: Math.floor(position.x / gridSize) * gridSize,
    y: Math.floor(position.y / gridSize) * gridSize,
  };
}

// Convert screen coordinates to grid coordinates
export function screenToGrid(
  screenPos: Position,
  cellSize: number,
  offset: Position = { x: 0, y: 0 }
): Position {
  return {
    x: Math.floor((screenPos.x - offset.x) / cellSize),
    y: Math.floor((screenPos.y - offset.y) / cellSize),
  };
}

// Convert grid coordinates to screen coordinates
export function gridToScreen(
  gridPos: Position,
  cellSize: number,
  offset: Position = { x: 0, y: 0 }
): Position {
  return {
    x: gridPos.x * cellSize + offset.x,
    y: gridPos.y * cellSize + offset.y,
  };
}

// Format numbers for display
export function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

// Format time duration
export function formatTime(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
  } else if (minutes > 0) {
    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
  } else {
    return `${seconds}s`;
  }
}

// Clamp value between min and max
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

// Linear interpolation
export function lerp(start: number, end: number, factor: number): number {
  return start + (end - start) * factor;
}

// Check if point is inside rectangle
export function pointInRect(point: Position, rect: Position, size: Size): boolean {
  return (
    point.x >= rect.x &&
    point.x < rect.x + size.width &&
    point.y >= rect.y &&
    point.y < rect.y + size.height
  );
}

// Get rotation transform for CSS
export function getRotationTransform(direction: Direction): string {
  const degrees = direction * 90;
  return `rotate(${degrees}deg)`;
}

// Debounce function
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle function
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}
