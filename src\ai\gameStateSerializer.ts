import {
  GameState,
  GameComponent,
  ComponentType,
  ResourceType,
  Direction,
  Position,
  COMPONENT_DEFINITIONS,
  RECIPES,
} from '@/types/game';
import { FactoryAnalytics } from '@/analytics/performanceAnalyzer';

export interface SerializedGameState {
  metadata: {
    version: string;
    timestamp: number;
    gridSize: { width: number; height: number };
    gameTime: number;
    isRunning: boolean;
  };
  components: SerializedComponent[];
  resources: Record<string, number>;
  statistics: {
    totalProduction: Record<string, number>;
    totalConsumption: Record<string, number>;
    efficiency: number;
    bottlenecks: string[];
  };
  analytics?: SerializedAnalytics;
}

export interface SerializedComponent {
  id: string;
  type: ComponentType;
  position: Position;
  direction: Direction;
  recipe?: string; // recipe ID
  inventory: Record<string, number>;
  connections: {
    inputs: string[];
    outputs: string[];
  };
  isActive: boolean;
  lastProcessTime: number;
  // Additional metadata for AI understanding
  metadata: {
    name: string;
    description: string;
    maxInputs: number;
    maxOutputs: number;
    speed: number;
    size: { width: number; height: number };
  };
}

export interface SerializedAnalytics {
  overallEfficiency: number;
  totalThroughput: number;
  performanceScore: number;
  bottlenecks: string[];
  recommendations: string[];
  componentAnalytics: Record<string, {
    utilization: number;
    throughput: number;
    efficiency: number;
    bottleneckScore: number;
    inputStarvation: number;
    outputBlocked: number;
  }>;
  resourceFlows: Record<string, {
    totalProduction: number;
    totalConsumption: number;
    netFlow: number;
    flowEfficiency: number;
    bottleneckComponents: string[];
  }>;
}

export class GameStateSerializer {
  public static serialize(gameState: GameState, analytics?: FactoryAnalytics): SerializedGameState {
    const serialized: SerializedGameState = {
      metadata: {
        version: '1.0.0',
        timestamp: Date.now(),
        gridSize: gameState.gridSize,
        gameTime: gameState.gameTime,
        isRunning: gameState.isRunning,
      },
      components: this.serializeComponents(gameState.components),
      resources: this.serializeResources(gameState.resources),
      statistics: {
        totalProduction: this.mapToRecord(gameState.statistics.totalProduction),
        totalConsumption: this.mapToRecord(gameState.statistics.totalConsumption),
        efficiency: gameState.statistics.efficiency,
        bottlenecks: gameState.statistics.bottlenecks,
      },
    };

    if (analytics) {
      serialized.analytics = this.serializeAnalytics(analytics);
    }

    return serialized;
  }

  public static deserialize(serialized: SerializedGameState): GameState {
    const components = new Map<string, GameComponent>();
    
    for (const comp of serialized.components) {
      const component: GameComponent = {
        id: comp.id,
        type: comp.type,
        position: comp.position,
        direction: comp.direction,
        recipe: comp.recipe ? RECIPES[comp.recipe] : undefined,
        inventory: new Map(Object.entries(comp.inventory).map(([k, v]) => [k as ResourceType, v])),
        connections: comp.connections,
        isActive: comp.isActive,
        lastProcessTime: comp.lastProcessTime,
      };
      components.set(comp.id, component);
    }

    return {
      components,
      gridSize: serialized.metadata.gridSize,
      gameTime: serialized.metadata.gameTime,
      isRunning: serialized.metadata.isRunning,
      resources: new Map(Object.entries(serialized.resources).map(([k, v]) => [k as ResourceType, v])),
      statistics: {
        totalProduction: this.recordToMap(serialized.statistics.totalProduction),
        totalConsumption: this.recordToMap(serialized.statistics.totalConsumption),
        efficiency: serialized.statistics.efficiency,
        bottlenecks: serialized.statistics.bottlenecks,
      },
    };
  }

  private static serializeComponents(components: Map<string, GameComponent>): SerializedComponent[] {
    return Array.from(components.values()).map(component => {
      const definition = COMPONENT_DEFINITIONS[component.type];
      
      return {
        id: component.id,
        type: component.type,
        position: component.position,
        direction: component.direction,
        recipe: component.recipe?.id,
        inventory: this.mapToRecord(component.inventory),
        connections: component.connections,
        isActive: component.isActive,
        lastProcessTime: component.lastProcessTime,
        metadata: {
          name: definition.name,
          description: definition.description,
          maxInputs: definition.maxInputs,
          maxOutputs: definition.maxOutputs,
          speed: definition.speed,
          size: definition.size,
        },
      };
    });
  }

  private static serializeResources(resources: Map<ResourceType, number>): Record<string, number> {
    return this.mapToRecord(resources);
  }

  private static serializeAnalytics(analytics: FactoryAnalytics): SerializedAnalytics {
    const componentAnalytics: Record<string, any> = {};
    analytics.componentAnalytics.forEach((value, key) => {
      componentAnalytics[key] = {
        utilization: value.utilization,
        throughput: value.throughput,
        efficiency: value.efficiency,
        bottleneckScore: value.bottleneckScore,
        inputStarvation: value.inputStarvation,
        outputBlocked: value.outputBlocked,
      };
    });

    const resourceFlows: Record<string, any> = {};
    analytics.resourceFlows.forEach((value, key) => {
      resourceFlows[key] = {
        totalProduction: value.totalProduction,
        totalConsumption: value.totalConsumption,
        netFlow: value.netFlow,
        flowEfficiency: value.flowEfficiency,
        bottleneckComponents: value.bottleneckComponents,
      };
    });

    return {
      overallEfficiency: analytics.overallEfficiency,
      totalThroughput: analytics.totalThroughput,
      performanceScore: analytics.performanceScore,
      bottlenecks: analytics.bottlenecks,
      recommendations: analytics.recommendations,
      componentAnalytics,
      resourceFlows,
    };
  }

  private static mapToRecord<T>(map: Map<string, T>): Record<string, T> {
    const record: Record<string, T> = {};
    map.forEach((value, key) => {
      record[key] = value;
    });
    return record;
  }

  private static recordToMap<T>(record: Record<string, T>): Map<string, T> {
    return new Map(Object.entries(record));
  }

  public static generateAIPrompt(serialized: SerializedGameState): string {
    const prompt = `
# Factory Builder Game State Analysis

## Current Factory Status
- **Grid Size**: ${serialized.metadata.gridSize.width}x${serialized.metadata.gridSize.height}
- **Game Time**: ${Math.round(serialized.metadata.gameTime / 1000)}s
- **Status**: ${serialized.metadata.isRunning ? 'Running' : 'Paused'}
- **Overall Efficiency**: ${(serialized.statistics.efficiency * 100).toFixed(1)}%

## Components (${serialized.components.length} total)
${serialized.components.map(comp => `
- **${comp.metadata.name}** (${comp.id.slice(0, 8)})
  - Position: (${comp.position.x}, ${comp.position.y})
  - Direction: ${['North', 'East', 'South', 'West'][comp.direction]}
  - Active: ${comp.isActive ? 'Yes' : 'No'}
  - Recipe: ${comp.recipe || 'None'}
  - Inventory: ${Object.entries(comp.inventory).map(([r, a]) => `${r}: ${a}`).join(', ') || 'Empty'}
  - Connections: ${comp.connections.inputs.length} inputs, ${comp.connections.outputs.length} outputs
`).join('')}

## Resources
${Object.entries(serialized.resources).map(([resource, amount]) => `
- **${resource.replace('_', ' ')}**: ${amount}
`).join('')}

## Production Statistics
${Object.entries(serialized.statistics.totalProduction).map(([resource, amount]) => `
- **${resource.replace('_', ' ')} Production**: ${amount}/min
`).join('')}

${Object.entries(serialized.statistics.totalConsumption).map(([resource, amount]) => `
- **${resource.replace('_', ' ')} Consumption**: ${amount}/min
`).join('')}

${serialized.analytics ? `
## Performance Analytics
- **Performance Score**: ${serialized.analytics.performanceScore}/100
- **Total Throughput**: ${serialized.analytics.totalThroughput.toFixed(1)} items/min
- **Bottlenecks**: ${serialized.analytics.bottlenecks.length} components

### Recommendations
${serialized.analytics.recommendations.map(rec => `- ${rec}`).join('\n')}

### Component Performance
${Object.entries(serialized.analytics.componentAnalytics).map(([id, analytics]) => `
- **${id.slice(0, 8)}**: ${(analytics.utilization * 100).toFixed(1)}% utilization, ${analytics.throughput.toFixed(1)} items/min
`).join('')}
` : ''}

## Game Mechanics Reference

### Component Types
- **Conveyor Belt**: Transports items (15 items/sec)
- **Mining Drill**: Extracts resources (0.5 items/sec)
- **Assembling Machine**: Crafts items (0.75x speed multiplier)
- **Storage Chest**: Stores items (30 items/sec throughput)
- **Splitter**: Divides input into 2 outputs (15 items/sec)
- **Merger**: Combines 2 inputs into 1 output (15 items/sec)

### Available Recipes
- **Iron Plate**: 1 Iron Ore → 1 Iron Plate (3.2s)
- **Copper Plate**: 1 Copper Ore → 1 Copper Plate (3.2s)
- **Iron Gear**: 2 Iron Plates → 1 Gear (0.5s)
- **Electronic Circuit**: 1 Iron Plate + 3 Copper Plates → 1 Circuit (0.5s)

### Resource Types
- **Raw Materials**: Iron Ore, Copper Ore, Coal
- **Intermediate Products**: Iron Plate, Copper Plate
- **Advanced Products**: Gear, Electronic Circuit

Please analyze this factory and provide suggestions for optimization, efficiency improvements, or problem identification.
`;

    return prompt.trim();
  }
}
