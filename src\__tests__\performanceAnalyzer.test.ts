import { PerformanceAnalyzer } from '@/analytics/performanceAnalyzer';
import {
  GameState,
  GameComponent,
  ComponentType,
  ResourceType,
  Direction,
  RECIPES,
} from '@/types/game';

describe('PerformanceAnalyzer', () => {
  let analyzer: PerformanceAnalyzer;
  let mockGameState: GameState;

  beforeEach(() => {
    analyzer = new PerformanceAnalyzer();
    mockGameState = {
      components: new Map(),
      gridSize: { width: 50, height: 50 },
      isRunning: true,
      gameTime: 0,
      resources: new Map([
        [ResourceType.IRON_ORE, 1000],
        [ResourceType.COPPER_ORE, 1000],
        [ResourceType.IRON_PLATE, 100],
      ]),
      statistics: {
        totalProduction: new Map([[ResourceType.IRON_ORE, 50]]),
        totalConsumption: new Map([[ResourceType.IRON_ORE, 30]]),
        efficiency: 0.8,
        bottlenecks: [],
      },
    };
  });

  describe('Factory Analysis', () => {
    it('should analyze an empty factory', () => {
      const analytics = analyzer.analyzeFactory(mockGameState);

      expect(analytics).toBeDefined();
      expect(analytics.overallEfficiency).toBe(0);
      expect(analytics.totalThroughput).toBe(0);
      expect(analytics.componentAnalytics.size).toBe(0);
      expect(analytics.bottlenecks).toHaveLength(0);
      expect(analytics.performanceScore).toBeGreaterThanOrEqual(0);
    });

    it('should analyze a factory with components', () => {
      const miner: GameComponent = {
        id: 'miner1',
        type: ComponentType.MINER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map([[ResourceType.IRON_ORE, 10]]),
        connections: { inputs: [], outputs: ['conveyor1'] },
        isActive: true,
        lastProcessTime: Date.now(),
      };

      const conveyor: GameComponent = {
        id: 'conveyor1',
        type: ComponentType.CONVEYOR,
        position: { x: 1, y: 0 },
        direction: Direction.EAST,
        inventory: new Map([[ResourceType.IRON_ORE, 5]]),
        connections: { inputs: ['miner1'], outputs: ['assembler1'] },
        isActive: true,
        lastProcessTime: Date.now(),
      };

      const assembler: GameComponent = {
        id: 'assembler1',
        type: ComponentType.ASSEMBLER,
        position: { x: 2, y: 0 },
        direction: Direction.NORTH,
        recipe: RECIPES.iron_plate,
        inventory: new Map([[ResourceType.IRON_ORE, 3]]),
        connections: { inputs: ['conveyor1'], outputs: [] },
        isActive: true,
        lastProcessTime: Date.now(),
      };

      mockGameState.components.set('miner1', miner);
      mockGameState.components.set('conveyor1', conveyor);
      mockGameState.components.set('assembler1', assembler);

      const analytics = analyzer.analyzeFactory(mockGameState);

      expect(analytics.componentAnalytics.size).toBe(3);
      expect(analytics.overallEfficiency).toBeGreaterThan(0);
      expect(analytics.totalThroughput).toBeGreaterThan(0);
      expect(analytics.performanceScore).toBeGreaterThan(0);
    });
  });

  describe('Component Analytics', () => {
    it('should calculate utilization correctly', () => {
      const activeComponent: GameComponent = {
        id: 'active1',
        type: ComponentType.MINER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: true,
        lastProcessTime: Date.now(),
      };

      const inactiveComponent: GameComponent = {
        id: 'inactive1',
        type: ComponentType.MINER,
        position: { x: 1, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: false,
        lastProcessTime: 0,
      };

      mockGameState.components.set('active1', activeComponent);
      mockGameState.components.set('inactive1', inactiveComponent);

      const analytics = analyzer.analyzeFactory(mockGameState);

      const activeAnalytics = analytics.componentAnalytics.get('active1');
      const inactiveAnalytics = analytics.componentAnalytics.get('inactive1');

      expect(activeAnalytics?.utilization).toBe(1.0);
      expect(inactiveAnalytics?.utilization).toBe(0.0);
    });

    it('should calculate throughput based on component type', () => {
      const miner: GameComponent = {
        id: 'miner1',
        type: ComponentType.MINER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: true,
        lastProcessTime: Date.now(),
      };

      const conveyor: GameComponent = {
        id: 'conveyor1',
        type: ComponentType.CONVEYOR,
        position: { x: 1, y: 0 },
        direction: Direction.EAST,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: true,
        lastProcessTime: Date.now(),
      };

      mockGameState.components.set('miner1', miner);
      mockGameState.components.set('conveyor1', conveyor);

      const analytics = analyzer.analyzeFactory(mockGameState);

      const minerAnalytics = analytics.componentAnalytics.get('miner1');
      const conveyorAnalytics = analytics.componentAnalytics.get('conveyor1');

      expect(minerAnalytics?.throughput).toBe(30); // 0.5 * 60
      expect(conveyorAnalytics?.throughput).toBe(900); // 15 * 60
    });

    it('should identify bottlenecks', () => {
      const blockedComponent: GameComponent = {
        id: 'blocked1',
        type: ComponentType.MINER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map([[ResourceType.IRON_ORE, 50]]), // Full inventory
        connections: { inputs: [], outputs: ['inactive1'] },
        isActive: false,
        lastProcessTime: Date.now(),
      };

      const inactiveComponent: GameComponent = {
        id: 'inactive1',
        type: ComponentType.CONVEYOR,
        position: { x: 1, y: 0 },
        direction: Direction.EAST,
        inventory: new Map(),
        connections: { inputs: ['blocked1'], outputs: [] },
        isActive: false,
        lastProcessTime: 0,
      };

      mockGameState.components.set('blocked1', blockedComponent);
      mockGameState.components.set('inactive1', inactiveComponent);

      const analytics = analyzer.analyzeFactory(mockGameState);

      expect(analytics.bottlenecks.length).toBeGreaterThan(0);
    });
  });

  describe('Resource Flow Analysis', () => {
    it('should track resource production and consumption', () => {
      const miner: GameComponent = {
        id: 'miner1',
        type: ComponentType.MINER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: true,
        lastProcessTime: Date.now(),
      };

      const assembler: GameComponent = {
        id: 'assembler1',
        type: ComponentType.ASSEMBLER,
        position: { x: 1, y: 0 },
        direction: Direction.NORTH,
        recipe: RECIPES.iron_plate,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: true,
        lastProcessTime: Date.now(),
      };

      mockGameState.components.set('miner1', miner);
      mockGameState.components.set('assembler1', assembler);

      const analytics = analyzer.analyzeFactory(mockGameState);

      const ironOreFlow = analytics.resourceFlows.get(ResourceType.IRON_ORE);
      const ironPlateFlow = analytics.resourceFlows.get(ResourceType.IRON_PLATE);

      expect(ironOreFlow).toBeDefined();
      expect(ironPlateFlow).toBeDefined();
      expect(ironOreFlow?.totalProduction).toBeGreaterThan(0);
      expect(ironPlateFlow?.totalProduction).toBeGreaterThan(0);
    });

    it('should calculate flow efficiency', () => {
      mockGameState.components.set('test1', {
        id: 'test1',
        type: ComponentType.MINER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: true,
        lastProcessTime: Date.now(),
      });

      const analytics = analyzer.analyzeFactory(mockGameState);

      analytics.resourceFlows.forEach(flow => {
        expect(flow.flowEfficiency).toBeGreaterThanOrEqual(0);
        expect(flow.flowEfficiency).toBeLessThanOrEqual(1);
      });
    });
  });

  describe('Performance Scoring', () => {
    it('should calculate performance score within valid range', () => {
      const component: GameComponent = {
        id: 'component1',
        type: ComponentType.MINER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: true,
        lastProcessTime: Date.now(),
      };

      mockGameState.components.set('component1', component);

      const analytics = analyzer.analyzeFactory(mockGameState);

      expect(analytics.performanceScore).toBeGreaterThanOrEqual(0);
      expect(analytics.performanceScore).toBeLessThanOrEqual(100);
    });

    it('should provide recommendations', () => {
      const inefficientComponent: GameComponent = {
        id: 'inefficient1',
        type: ComponentType.MINER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: false,
        lastProcessTime: 0,
      };

      mockGameState.components.set('inefficient1', inefficientComponent);

      const analytics = analyzer.analyzeFactory(mockGameState);

      expect(Array.isArray(analytics.recommendations)).toBe(true);
    });
  });

  describe('Historical Data', () => {
    it('should track historical data', () => {
      const component: GameComponent = {
        id: 'component1',
        type: ComponentType.MINER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: true,
        lastProcessTime: Date.now(),
      };

      mockGameState.components.set('component1', component);

      // Run analysis multiple times to build history
      analyzer.analyzeFactory(mockGameState);
      analyzer.analyzeFactory(mockGameState);

      const efficiencyHistory = analyzer.getHistoricalData('efficiency');
      expect(Array.isArray(efficiencyHistory)).toBe(true);
    });

    it('should limit historical data length', () => {
      const component: GameComponent = {
        id: 'component1',
        type: ComponentType.MINER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: true,
        lastProcessTime: Date.now(),
      };

      mockGameState.components.set('component1', component);

      // Run analysis many times
      for (let i = 0; i < 30; i++) {
        analyzer.analyzeFactory(mockGameState);
      }

      const efficiencyHistory = analyzer.getHistoricalData('efficiency');
      expect(efficiencyHistory.length).toBeLessThanOrEqual(20); // HISTORY_LENGTH = 20
    });
  });
});
