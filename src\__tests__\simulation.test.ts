import { SimulationEngine } from '@/engine/simulation';
import {
  GameState,
  GameComponent,
  ComponentType,
  ResourceType,
  Direction,
  RECIPES,
} from '@/types/game';

describe('SimulationEngine', () => {
  let engine: SimulationEngine;
  let mockGameState: GameState;

  beforeEach(() => {
    engine = new SimulationEngine();
    mockGameState = {
      components: new Map(),
      gridSize: { width: 50, height: 50 },
      isRunning: true,
      gameTime: 0,
      resources: new Map([
        [ResourceType.IRON_ORE, 1000],
        [ResourceType.COPPER_ORE, 1000],
        [ResourceType.IRON_PLATE, 100],
      ]),
      statistics: {
        totalProduction: new Map(),
        totalConsumption: new Map(),
        efficiency: 0,
        bottlenecks: [],
      },
    };
  });

  describe('Miner Processing', () => {
    it('should produce resources when active', () => {
      const miner: GameComponent = {
        id: 'miner1',
        type: ComponentType.MINER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: false,
        lastProcessTime: 0,
      };

      mockGameState.components.set('miner1', miner);

      const result = engine.updateSimulation(mockGameState);

      expect(result.components).toBeDefined();
      if (result.components) {
        const updatedMiner = result.components.get('miner1');
        expect(updatedMiner).toBeDefined();
        // Note: The actual production depends on timing, so we check if the miner can become active
      }
    });

    it('should stop producing when storage is full', () => {
      const miner: GameComponent = {
        id: 'miner1',
        type: ComponentType.MINER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map([[ResourceType.IRON_ORE, 50]]), // Full storage
        connections: { inputs: [], outputs: [] },
        isActive: true,
        lastProcessTime: Date.now() - 3000, // 3 seconds ago
      };

      mockGameState.components.set('miner1', miner);

      const result = engine.updateSimulation(mockGameState);

      if (result.components) {
        const updatedMiner = result.components.get('miner1');
        expect(updatedMiner?.isActive).toBe(false);
      }
    });
  });

  describe('Assembler Processing', () => {
    it('should process recipe when inputs are available', () => {
      const assembler: GameComponent = {
        id: 'assembler1',
        type: ComponentType.ASSEMBLER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        recipe: RECIPES.iron_plate,
        inventory: new Map([[ResourceType.IRON_ORE, 5]]),
        connections: { inputs: [], outputs: [] },
        isActive: false,
        lastProcessTime: 0,
      };

      mockGameState.components.set('assembler1', assembler);

      const result = engine.updateSimulation(mockGameState);

      expect(result.components).toBeDefined();
      if (result.components) {
        const updatedAssembler = result.components.get('assembler1');
        expect(updatedAssembler).toBeDefined();
      }
    });

    it('should not process without sufficient inputs', () => {
      const assembler: GameComponent = {
        id: 'assembler1',
        type: ComponentType.ASSEMBLER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        recipe: RECIPES.iron_plate,
        inventory: new Map(), // No inputs
        connections: { inputs: [], outputs: [] },
        isActive: false,
        lastProcessTime: 0,
      };

      mockGameState.components.set('assembler1', assembler);

      const result = engine.updateSimulation(mockGameState);

      if (result.components) {
        const updatedAssembler = result.components.get('assembler1');
        expect(updatedAssembler?.isActive).toBe(false);
      }
    });

    it('should not process without a recipe', () => {
      const assembler: GameComponent = {
        id: 'assembler1',
        type: ComponentType.ASSEMBLER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        recipe: undefined,
        inventory: new Map([[ResourceType.IRON_ORE, 5]]),
        connections: { inputs: [], outputs: [] },
        isActive: false,
        lastProcessTime: 0,
      };

      mockGameState.components.set('assembler1', assembler);

      const result = engine.updateSimulation(mockGameState);

      if (result.components) {
        const updatedAssembler = result.components.get('assembler1');
        expect(updatedAssembler?.isActive).toBe(false);
      }
    });
  });

  describe('Conveyor Processing', () => {
    it('should transfer items between connected components', () => {
      const conveyor: GameComponent = {
        id: 'conveyor1',
        type: ComponentType.CONVEYOR,
        position: { x: 1, y: 0 },
        direction: Direction.EAST,
        inventory: new Map([[ResourceType.IRON_ORE, 5]]),
        connections: { inputs: [], outputs: ['storage1'] },
        isActive: false,
        lastProcessTime: 0,
      };

      const storage: GameComponent = {
        id: 'storage1',
        type: ComponentType.STORAGE,
        position: { x: 2, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map(),
        connections: { inputs: ['conveyor1'], outputs: [] },
        isActive: false,
        lastProcessTime: 0,
      };

      mockGameState.components.set('conveyor1', conveyor);
      mockGameState.components.set('storage1', storage);

      const result = engine.updateSimulation(mockGameState);

      expect(result.components).toBeDefined();
      if (result.components) {
        const updatedConveyor = result.components.get('conveyor1');
        const updatedStorage = result.components.get('storage1');
        
        // Items should be transferred from conveyor to storage
        expect(updatedConveyor).toBeDefined();
        expect(updatedStorage).toBeDefined();
      }
    });
  });

  describe('Splitter Processing', () => {
    it('should split items evenly between outputs', () => {
      const splitter: GameComponent = {
        id: 'splitter1',
        type: ComponentType.SPLITTER,
        position: { x: 1, y: 0 },
        direction: Direction.EAST,
        inventory: new Map([[ResourceType.IRON_ORE, 10]]),
        connections: { inputs: [], outputs: ['conveyor1', 'conveyor2'] },
        isActive: false,
        lastProcessTime: 0,
      };

      const conveyor1: GameComponent = {
        id: 'conveyor1',
        type: ComponentType.CONVEYOR,
        position: { x: 2, y: 0 },
        direction: Direction.EAST,
        inventory: new Map(),
        connections: { inputs: ['splitter1'], outputs: [] },
        isActive: false,
        lastProcessTime: 0,
      };

      const conveyor2: GameComponent = {
        id: 'conveyor2',
        type: ComponentType.CONVEYOR,
        position: { x: 2, y: 1 },
        direction: Direction.EAST,
        inventory: new Map(),
        connections: { inputs: ['splitter1'], outputs: [] },
        isActive: false,
        lastProcessTime: 0,
      };

      mockGameState.components.set('splitter1', splitter);
      mockGameState.components.set('conveyor1', conveyor1);
      mockGameState.components.set('conveyor2', conveyor2);

      const result = engine.updateSimulation(mockGameState);

      expect(result.components).toBeDefined();
      if (result.components) {
        const updatedSplitter = result.components.get('splitter1');
        expect(updatedSplitter).toBeDefined();
      }
    });
  });

  describe('Performance Metrics', () => {
    it('should calculate efficiency correctly', () => {
      const activeComponent: GameComponent = {
        id: 'active1',
        type: ComponentType.MINER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: true,
        lastProcessTime: Date.now(),
      };

      const inactiveComponent: GameComponent = {
        id: 'inactive1',
        type: ComponentType.MINER,
        position: { x: 1, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: false,
        lastProcessTime: 0,
      };

      mockGameState.components.set('active1', activeComponent);
      mockGameState.components.set('inactive1', inactiveComponent);

      const result = engine.updateSimulation(mockGameState);

      expect(result.statistics).toBeDefined();
      if (result.statistics) {
        expect(result.statistics.efficiency).toBeGreaterThanOrEqual(0);
        expect(result.statistics.efficiency).toBeLessThanOrEqual(1);
      }
    });

    it('should track production statistics', () => {
      const miner: GameComponent = {
        id: 'miner1',
        type: ComponentType.MINER,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map(),
        connections: { inputs: [], outputs: [] },
        isActive: true,
        lastProcessTime: 0,
      };

      mockGameState.components.set('miner1', miner);

      const result = engine.updateSimulation(mockGameState);

      expect(result.statistics).toBeDefined();
      if (result.statistics) {
        expect(result.statistics.totalProduction).toBeDefined();
        expect(result.statistics.totalConsumption).toBeDefined();
      }
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty game state', () => {
      const emptyState: GameState = {
        ...mockGameState,
        components: new Map(),
      };

      const result = engine.updateSimulation(emptyState);

      expect(result).toBeDefined();
      expect(result.components?.size).toBe(0);
    });

    it('should handle components with invalid connections', () => {
      const component: GameComponent = {
        id: 'component1',
        type: ComponentType.CONVEYOR,
        position: { x: 0, y: 0 },
        direction: Direction.NORTH,
        inventory: new Map(),
        connections: { inputs: ['nonexistent'], outputs: ['alsononexistent'] },
        isActive: false,
        lastProcessTime: 0,
      };

      mockGameState.components.set('component1', component);

      const result = engine.updateSimulation(mockGameState);

      expect(result).toBeDefined();
      // Should not crash with invalid connections
    });
  });
});
