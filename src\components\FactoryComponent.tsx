'use client';

import React, { useState } from 'react';
import { useDrag } from 'react-dnd';
import { GameComponent, Position, COMPONENT_DEFINITIONS, ComponentType } from '@/types/game';
import { useGameStore } from '@/store/gameStore';
import { screenToGrid, getRotationTransform } from '@/utils/helpers';
import { 
  ArrowRight, 
  Cog, 
  Package, 
  Pickaxe, 
  Split, 
  Merge,
  RotateCw,
  Settings
} from 'lucide-react';
import clsx from 'clsx';

interface FactoryComponentProps {
  component: GameComponent;
  position: Position;
  cellSize: number;
  isSelected: boolean;
  onSelect: () => void;
  onMove: (newPosition: Position) => void;
}

const FactoryComponent: React.FC<FactoryComponentProps> = ({
  component,
  position,
  cellSize,
  isSelected,
  onSelect,
  onMove,
}) => {
  const { rotateComponent, setComponentRecipe } = useGameStore();
  const [showTooltip, setShowTooltip] = useState(false);
  const definition = COMPONENT_DEFINITIONS[component.type];

  const [{ isDragging }, drag] = useDrag({
    type: 'placed-component',
    item: { id: component.id, originalPosition: component.position },
    end: (item, monitor) => {
      const dropResult = monitor.getDropResult();
      if (!dropResult) return;

      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;

      // Calculate new grid position
      const newGridPos = screenToGrid(
        { x: clientOffset.x, y: clientOffset.y },
        cellSize
      );
      onMove(newGridPos);
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const getComponentIcon = () => {
    switch (component.type) {
      case ComponentType.CONVEYOR:
        return <ArrowRight className="w-6 h-6" />;
      case ComponentType.MINER:
        return <Pickaxe className="w-6 h-6" />;
      case ComponentType.ASSEMBLER:
        return <Cog className="w-6 h-6" />;
      case ComponentType.STORAGE:
        return <Package className="w-6 h-6" />;
      case ComponentType.SPLITTER:
        return <Split className="w-6 h-6" />;
      case ComponentType.MERGER:
        return <Merge className="w-6 h-6" />;
      default:
        return <Cog className="w-6 h-6" />;
    }
  };

  const getComponentColor = () => {
    switch (component.type) {
      case ComponentType.CONVEYOR:
        return 'bg-yellow-600 border-yellow-500';
      case ComponentType.MINER:
        return 'bg-blue-600 border-blue-500';
      case ComponentType.ASSEMBLER:
        return 'bg-green-600 border-green-500';
      case ComponentType.STORAGE:
        return 'bg-purple-600 border-purple-500';
      case ComponentType.SPLITTER:
        return 'bg-orange-600 border-orange-500';
      case ComponentType.MERGER:
        return 'bg-red-600 border-red-500';
      default:
        return 'bg-gray-600 border-gray-500';
    }
  };

  const handleRightClick = (e: React.MouseEvent) => {
    e.preventDefault();
    rotateComponent(component.id);
  };

  const handleDoubleClick = () => {
    if (component.type === ComponentType.ASSEMBLER) {
      // Open recipe selection (simplified for now)
      const recipeId = prompt('Enter recipe ID (iron_plate, copper_plate, gear, circuit):');
      if (recipeId) {
        setComponentRecipe(component.id, recipeId);
      }
    }
  };

  const width = definition.size.width * cellSize;
  const height = definition.size.height * cellSize;

  return (
    <div
      ref={drag}
      className={clsx(
        'absolute border-2 rounded cursor-pointer transition-all duration-200 flex items-center justify-center',
        getComponentColor(),
        {
          'ring-2 ring-blue-400 ring-opacity-75': isSelected,
          'opacity-50': isDragging,
          'shadow-lg': component.isActive,
          'animate-pulse': component.isActive && component.type === ComponentType.MINER,
        }
      )}
      style={{
        left: position.x,
        top: position.y,
        width,
        height,
        transform: getRotationTransform(component.direction),
      }}
      onClick={onSelect}
      onContextMenu={handleRightClick}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      {/* Component icon */}
      <div className="text-white">
        {getComponentIcon()}
      </div>

      {/* Activity indicator */}
      {component.isActive && (
        <div className="absolute top-1 right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse" />
      )}

      {/* Inventory indicator */}
      {component.inventory.size > 0 && (
        <div className="absolute bottom-1 left-1 text-xs text-white bg-black bg-opacity-50 rounded px-1">
          {Array.from(component.inventory.values()).reduce((sum, amount) => sum + amount, 0)}
        </div>
      )}

      {/* Recipe indicator for assemblers */}
      {component.type === ComponentType.ASSEMBLER && component.recipe && (
        <div className="absolute top-1 left-1 text-xs text-white bg-black bg-opacity-50 rounded px-1">
          {component.recipe.name.slice(0, 3)}
        </div>
      )}

      {/* Connection points */}
      {definition.maxInputs > 0 && (
        <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-green-400 rounded-full" />
      )}
      {definition.maxOutputs > 0 && (
        <div className="absolute -right-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-red-400 rounded-full" />
      )}

      {/* Tooltip */}
      {showTooltip && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black bg-opacity-90 text-white text-xs rounded whitespace-nowrap z-50">
          <div className="font-semibold">{definition.name}</div>
          <div>{definition.description}</div>
          {component.recipe && (
            <div className="text-yellow-300">Recipe: {component.recipe.name}</div>
          )}
          <div className="text-gray-300">
            Right-click to rotate, Double-click for settings
          </div>
        </div>
      )}

      {/* Selection controls */}
      {isSelected && (
        <div className="absolute -top-8 left-0 flex gap-1">
          <button
            onClick={(e) => {
              e.stopPropagation();
              rotateComponent(component.id);
            }}
            className="p-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            title="Rotate"
          >
            <RotateCw className="w-3 h-3" />
          </button>
          {component.type === ComponentType.ASSEMBLER && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleDoubleClick();
              }}
              className="p-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              title="Configure Recipe"
            >
              <Settings className="w-3 h-3" />
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default FactoryComponent;
