'use client';

import React, { useEffect, useRef } from 'react';
import { FactoryAnalytics } from '@/analytics/performanceAnalyzer';

interface PerformanceChartProps {
  analytics: FactoryAnalytics;
  historicalData: number[];
  title: string;
  color?: string;
  height?: number;
}

const PerformanceChart: React.FC<PerformanceChartProps> = ({
  analytics,
  historicalData,
  title,
  color = '#3b82f6',
  height = 100,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    // Clear canvas
    ctx.clearRect(0, 0, rect.width, rect.height);

    if (historicalData.length < 2) return;

    // Calculate bounds
    const maxValue = Math.max(...historicalData, 1);
    const minValue = Math.min(...historicalData, 0);
    const range = maxValue - minValue || 1;

    // Draw grid lines
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;
    
    // Horizontal grid lines
    for (let i = 0; i <= 4; i++) {
      const y = (i / 4) * height;
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(rect.width, y);
      ctx.stroke();
    }

    // Vertical grid lines
    for (let i = 0; i <= 10; i++) {
      const x = (i / 10) * rect.width;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }

    // Draw the line chart
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.beginPath();

    historicalData.forEach((value, index) => {
      const x = (index / (historicalData.length - 1)) * rect.width;
      const y = height - ((value - minValue) / range) * height;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();

    // Draw fill area
    ctx.fillStyle = color + '20'; // Add transparency
    ctx.beginPath();
    
    historicalData.forEach((value, index) => {
      const x = (index / (historicalData.length - 1)) * rect.width;
      const y = height - ((value - minValue) / range) * height;

      if (index === 0) {
        ctx.moveTo(x, height);
        ctx.lineTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.lineTo(rect.width, height);
    ctx.closePath();
    ctx.fill();

    // Draw current value point
    if (historicalData.length > 0) {
      const lastValue = historicalData[historicalData.length - 1];
      const x = rect.width;
      const y = height - ((lastValue - minValue) / range) * height;

      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.arc(x - 5, y, 3, 0, 2 * Math.PI);
      ctx.fill();
    }

  }, [historicalData, color, height]);

  const currentValue = historicalData[historicalData.length - 1] || 0;
  const previousValue = historicalData[historicalData.length - 2] || 0;
  const trend = currentValue - previousValue;

  return (
    <div className="bg-gray-800 rounded-lg p-4">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-sm font-semibold text-white">{title}</h3>
        <div className="flex items-center gap-2">
          <span className="text-lg font-bold text-white">
            {currentValue.toFixed(1)}
          </span>
          {trend !== 0 && (
            <span className={`text-xs ${trend > 0 ? 'text-green-400' : 'text-red-400'}`}>
              {trend > 0 ? '↗' : '↘'} {Math.abs(trend).toFixed(1)}
            </span>
          )}
        </div>
      </div>
      
      <div className="relative">
        <canvas
          ref={canvasRef}
          className="w-full"
          style={{ height: `${height}px` }}
        />
      </div>
    </div>
  );
};

export default PerformanceChart;
