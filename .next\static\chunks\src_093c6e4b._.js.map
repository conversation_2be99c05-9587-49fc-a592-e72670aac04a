{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/src/types/game.ts"], "sourcesContent": ["// Core game types and interfaces\n\nexport interface Position {\n  x: number;\n  y: number;\n}\n\nexport interface Size {\n  width: number;\n  height: number;\n}\n\nexport enum ComponentType {\n  CONVEYOR = 'conveyor',\n  MINER = 'miner',\n  ASSEMBLER = 'assembler',\n  STORAGE = 'storage',\n  SPLITTER = 'splitter',\n  MERGER = 'merger',\n}\n\nexport enum ResourceType {\n  IRON_ORE = 'iron_ore',\n  COPPER_ORE = 'copper_ore',\n  COAL = 'coal',\n  IRON_PLATE = 'iron_plate',\n  COPPER_PLATE = 'copper_plate',\n  GEAR = 'gear',\n  CIRCUIT = 'circuit',\n}\n\nexport enum Direction {\n  NORTH = 0,\n  EAST = 1,\n  SOUTH = 2,\n  WEST = 3,\n}\n\nexport interface Recipe {\n  id: string;\n  name: string;\n  inputs: { resource: ResourceType; amount: number }[];\n  outputs: { resource: ResourceType; amount: number }[];\n  processingTime: number; // in milliseconds\n}\n\nexport interface ComponentDefinition {\n  type: ComponentType;\n  name: string;\n  description: string;\n  size: Size;\n  maxInputs: number;\n  maxOutputs: number;\n  recipes?: Recipe[];\n  speed: number; // items per second\n  cost: { resource: ResourceType; amount: number }[];\n}\n\nexport interface GameComponent {\n  id: string;\n  type: ComponentType;\n  position: Position;\n  direction: Direction;\n  recipe?: Recipe;\n  inventory: Map<ResourceType, number>;\n  connections: {\n    inputs: string[]; // IDs of connected components\n    outputs: string[]; // IDs of connected components\n  };\n  isActive: boolean;\n  lastProcessTime: number;\n}\n\nexport interface GameState {\n  components: Map<string, GameComponent>;\n  gridSize: Size;\n  isRunning: boolean;\n  gameTime: number;\n  resources: Map<ResourceType, number>;\n  statistics: {\n    totalProduction: Map<ResourceType, number>;\n    totalConsumption: Map<ResourceType, number>;\n    efficiency: number;\n    bottlenecks: string[];\n  };\n}\n\nexport interface PerformanceMetrics {\n  throughput: Map<ResourceType, number>; // items per minute\n  utilization: Map<string, number>; // component ID -> utilization percentage\n  bottlenecks: string[]; // component IDs that are bottlenecks\n  efficiency: number; // overall factory efficiency (0-1)\n}\n\n// Component definitions for the game\nexport const COMPONENT_DEFINITIONS: Record<ComponentType, ComponentDefinition> = {\n  [ComponentType.CONVEYOR]: {\n    type: ComponentType.CONVEYOR,\n    name: 'Conveyor Belt',\n    description: 'Transports items between components',\n    size: { width: 1, height: 1 },\n    maxInputs: 1,\n    maxOutputs: 1,\n    speed: 15, // items per second\n    cost: [{ resource: ResourceType.IRON_PLATE, amount: 1 }],\n  },\n  [ComponentType.MINER]: {\n    type: ComponentType.MINER,\n    name: 'Mining Drill',\n    description: 'Extracts raw resources from the ground',\n    size: { width: 2, height: 2 },\n    maxInputs: 0,\n    maxOutputs: 1,\n    speed: 0.5, // items per second\n    cost: [\n      { resource: ResourceType.IRON_PLATE, amount: 10 },\n      { resource: ResourceType.GEAR, amount: 5 },\n    ],\n  },\n  [ComponentType.ASSEMBLER]: {\n    type: ComponentType.ASSEMBLER,\n    name: 'Assembling Machine',\n    description: 'Crafts items from raw materials',\n    size: { width: 3, height: 3 },\n    maxInputs: 2,\n    maxOutputs: 1,\n    speed: 0.75, // crafting speed multiplier\n    cost: [\n      { resource: ResourceType.IRON_PLATE, amount: 9 },\n      { resource: ResourceType.GEAR, amount: 5 },\n      { resource: ResourceType.CIRCUIT, amount: 3 },\n    ],\n  },\n  [ComponentType.STORAGE]: {\n    type: ComponentType.STORAGE,\n    name: 'Storage Chest',\n    description: 'Stores items for later use',\n    size: { width: 1, height: 1 },\n    maxInputs: 1,\n    maxOutputs: 1,\n    speed: 30, // items per second throughput\n    cost: [{ resource: ResourceType.IRON_PLATE, amount: 8 }],\n  },\n  [ComponentType.SPLITTER]: {\n    type: ComponentType.SPLITTER,\n    name: 'Splitter',\n    description: 'Splits input into multiple outputs',\n    size: { width: 2, height: 1 },\n    maxInputs: 1,\n    maxOutputs: 2,\n    speed: 15, // items per second\n    cost: [\n      { resource: ResourceType.IRON_PLATE, amount: 5 },\n      { resource: ResourceType.CIRCUIT, amount: 5 },\n    ],\n  },\n  [ComponentType.MERGER]: {\n    type: ComponentType.MERGER,\n    name: 'Merger',\n    description: 'Merges multiple inputs into one output',\n    size: { width: 2, height: 1 },\n    maxInputs: 2,\n    maxOutputs: 1,\n    speed: 15, // items per second\n    cost: [\n      { resource: ResourceType.IRON_PLATE, amount: 5 },\n      { resource: ResourceType.CIRCUIT, amount: 5 },\n    ],\n  },\n};\n\n// Recipe definitions\nexport const RECIPES: Record<string, Recipe> = {\n  iron_plate: {\n    id: 'iron_plate',\n    name: 'Iron Plate',\n    inputs: [{ resource: ResourceType.IRON_ORE, amount: 1 }],\n    outputs: [{ resource: ResourceType.IRON_PLATE, amount: 1 }],\n    processingTime: 3200, // 3.2 seconds\n  },\n  copper_plate: {\n    id: 'copper_plate',\n    name: 'Copper Plate',\n    inputs: [{ resource: ResourceType.COPPER_ORE, amount: 1 }],\n    outputs: [{ resource: ResourceType.COPPER_PLATE, amount: 1 }],\n    processingTime: 3200, // 3.2 seconds\n  },\n  gear: {\n    id: 'gear',\n    name: 'Iron Gear Wheel',\n    inputs: [{ resource: ResourceType.IRON_PLATE, amount: 2 }],\n    outputs: [{ resource: ResourceType.GEAR, amount: 1 }],\n    processingTime: 500, // 0.5 seconds\n  },\n  circuit: {\n    id: 'circuit',\n    name: 'Electronic Circuit',\n    inputs: [\n      { resource: ResourceType.IRON_PLATE, amount: 1 },\n      { resource: ResourceType.COPPER_PLATE, amount: 3 },\n    ],\n    outputs: [{ resource: ResourceType.CIRCUIT, amount: 1 }],\n    processingTime: 500, // 0.5 seconds\n  },\n};\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;AAY1B,IAAA,AAAK,uCAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,sCAAA;;;;;;;;WAAA;;AAUL,IAAA,AAAK,mCAAA;;;;;WAAA;;AAgEL,MAAM,wBAAoE;IAC/E,YAAwB,EAAE;QACxB,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;YAAE,OAAO;YAAG,QAAQ;QAAE;QAC5B,WAAW;QACX,YAAY;QACZ,OAAO;QACP,MAAM;YAAC;gBAAE,QAAQ;gBAA2B,QAAQ;YAAE;SAAE;IAC1D;IACA,SAAqB,EAAE;QACrB,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;YAAE,OAAO;YAAG,QAAQ;QAAE;QAC5B,WAAW;QACX,YAAY;QACZ,OAAO;QACP,MAAM;YACJ;gBAAE,QAAQ;gBAA2B,QAAQ;YAAG;YAChD;gBAAE,QAAQ;gBAAqB,QAAQ;YAAE;SAC1C;IACH;IACA,aAAyB,EAAE;QACzB,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;YAAE,OAAO;YAAG,QAAQ;QAAE;QAC5B,WAAW;QACX,YAAY;QACZ,OAAO;QACP,MAAM;YACJ;gBAAE,QAAQ;gBAA2B,QAAQ;YAAE;YAC/C;gBAAE,QAAQ;gBAAqB,QAAQ;YAAE;YACzC;gBAAE,QAAQ;gBAAwB,QAAQ;YAAE;SAC7C;IACH;IACA,WAAuB,EAAE;QACvB,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;YAAE,OAAO;YAAG,QAAQ;QAAE;QAC5B,WAAW;QACX,YAAY;QACZ,OAAO;QACP,MAAM;YAAC;gBAAE,QAAQ;gBAA2B,QAAQ;YAAE;SAAE;IAC1D;IACA,YAAwB,EAAE;QACxB,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;YAAE,OAAO;YAAG,QAAQ;QAAE;QAC5B,WAAW;QACX,YAAY;QACZ,OAAO;QACP,MAAM;YACJ;gBAAE,QAAQ;gBAA2B,QAAQ;YAAE;YAC/C;gBAAE,QAAQ;gBAAwB,QAAQ;YAAE;SAC7C;IACH;IACA,UAAsB,EAAE;QACtB,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;YAAE,OAAO;YAAG,QAAQ;QAAE;QAC5B,WAAW;QACX,YAAY;QACZ,OAAO;QACP,MAAM;YACJ;gBAAE,QAAQ;gBAA2B,QAAQ;YAAE;YAC/C;gBAAE,QAAQ;gBAAwB,QAAQ;YAAE;SAC7C;IACH;AACF;AAGO,MAAM,UAAkC;IAC7C,YAAY;QACV,IAAI;QACJ,MAAM;QACN,QAAQ;YAAC;gBAAE,QAAQ;gBAAyB,QAAQ;YAAE;SAAE;QACxD,SAAS;YAAC;gBAAE,QAAQ;gBAA2B,QAAQ;YAAE;SAAE;QAC3D,gBAAgB;IAClB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,QAAQ;YAAC;gBAAE,QAAQ;gBAA2B,QAAQ;YAAE;SAAE;QAC1D,SAAS;YAAC;gBAAE,QAAQ;gBAA6B,QAAQ;YAAE;SAAE;QAC7D,gBAAgB;IAClB;IACA,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,QAAQ;YAAC;gBAAE,QAAQ;gBAA2B,QAAQ;YAAE;SAAE;QAC1D,SAAS;YAAC;gBAAE,QAAQ;gBAAqB,QAAQ;YAAE;SAAE;QACrD,gBAAgB;IAClB;IACA,SAAS;QACP,IAAI;QACJ,MAAM;QACN,QAAQ;YACN;gBAAE,QAAQ;gBAA2B,QAAQ;YAAE;YAC/C;gBAAE,QAAQ;gBAA6B,QAAQ;YAAE;SAClD;QACD,SAAS;YAAC;gBAAE,QAAQ;gBAAwB,QAAQ;YAAE;SAAE;QACxD,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/src/utils/helpers.ts"], "sourcesContent": ["import { Position, Direction, Size } from '@/types/game';\n\n// Generate unique IDs for components\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\n// Calculate distance between two positions\nexport function calculateDistance(pos1: Position, pos2: Position): number {\n  return Math.sqrt(Math.pow(pos2.x - pos1.x, 2) + Math.pow(pos2.y - pos1.y, 2));\n}\n\n// Get direction vector from Direction enum\nexport function getDirectionVector(direction: Direction): Position {\n  switch (direction) {\n    case Direction.NORTH:\n      return { x: 0, y: -1 };\n    case Direction.EAST:\n      return { x: 1, y: 0 };\n    case Direction.SOUTH:\n      return { x: 0, y: 1 };\n    case Direction.WEST:\n      return { x: -1, y: 0 };\n    default:\n      return { x: 0, y: 0 };\n  }\n}\n\n// Get opposite direction\nexport function getOppositeDirection(direction: Direction): Direction {\n  return (direction + 2) % 4;\n}\n\n// Check if two rectangles overlap\nexport function rectanglesOverlap(\n  pos1: Position,\n  size1: Size,\n  pos2: Position,\n  size2: Size\n): boolean {\n  return !(\n    pos1.x >= pos2.x + size2.width ||\n    pos1.x + size1.width <= pos2.x ||\n    pos1.y >= pos2.y + size2.height ||\n    pos1.y + size1.height <= pos2.y\n  );\n}\n\n// Snap position to grid\nexport function snapToGrid(position: Position, gridSize: number = 1): Position {\n  return {\n    x: Math.floor(position.x / gridSize) * gridSize,\n    y: Math.floor(position.y / gridSize) * gridSize,\n  };\n}\n\n// Convert screen coordinates to grid coordinates\nexport function screenToGrid(\n  screenPos: Position,\n  cellSize: number,\n  offset: Position = { x: 0, y: 0 }\n): Position {\n  return {\n    x: Math.floor((screenPos.x - offset.x) / cellSize),\n    y: Math.floor((screenPos.y - offset.y) / cellSize),\n  };\n}\n\n// Convert grid coordinates to screen coordinates\nexport function gridToScreen(\n  gridPos: Position,\n  cellSize: number,\n  offset: Position = { x: 0, y: 0 }\n): Position {\n  return {\n    x: gridPos.x * cellSize + offset.x,\n    y: gridPos.y * cellSize + offset.y,\n  };\n}\n\n// Format numbers for display\nexport function formatNumber(num: number): string {\n  if (num >= 1000000) {\n    return (num / 1000000).toFixed(1) + 'M';\n  } else if (num >= 1000) {\n    return (num / 1000).toFixed(1) + 'K';\n  }\n  return num.toString();\n}\n\n// Format time duration\nexport function formatTime(milliseconds: number): string {\n  const seconds = Math.floor(milliseconds / 1000);\n  const minutes = Math.floor(seconds / 60);\n  const hours = Math.floor(minutes / 60);\n\n  if (hours > 0) {\n    return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;\n  } else if (minutes > 0) {\n    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;\n  } else {\n    return `${seconds}s`;\n  }\n}\n\n// Clamp value between min and max\nexport function clamp(value: number, min: number, max: number): number {\n  return Math.min(Math.max(value, min), max);\n}\n\n// Linear interpolation\nexport function lerp(start: number, end: number, factor: number): number {\n  return start + (end - start) * factor;\n}\n\n// Check if point is inside rectangle\nexport function pointInRect(point: Position, rect: Position, size: Size): boolean {\n  return (\n    point.x >= rect.x &&\n    point.x < rect.x + size.width &&\n    point.y >= rect.y &&\n    point.y < rect.y + size.height\n  );\n}\n\n// Get rotation transform for CSS\nexport function getRotationTransform(direction: Direction): string {\n  const degrees = direction * 90;\n  return `rotate(${degrees}deg)`;\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Throttle function\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAGO,SAAS,kBAAkB,IAAc,EAAE,IAAc;IAC9D,OAAO,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,EAAE;AAC5E;AAGO,SAAS,mBAAmB,SAAoB;IACrD,OAAQ;QACN,KAAK,uHAAA,CAAA,YAAS,CAAC,KAAK;YAClB,OAAO;gBAAE,GAAG;gBAAG,GAAG,CAAC;YAAE;QACvB,KAAK,uHAAA,CAAA,YAAS,CAAC,IAAI;YACjB,OAAO;gBAAE,GAAG;gBAAG,GAAG;YAAE;QACtB,KAAK,uHAAA,CAAA,YAAS,CAAC,KAAK;YAClB,OAAO;gBAAE,GAAG;gBAAG,GAAG;YAAE;QACtB,KAAK,uHAAA,CAAA,YAAS,CAAC,IAAI;YACjB,OAAO;gBAAE,GAAG,CAAC;gBAAG,GAAG;YAAE;QACvB;YACE,OAAO;gBAAE,GAAG;gBAAG,GAAG;YAAE;IACxB;AACF;AAGO,SAAS,qBAAqB,SAAoB;IACvD,OAAO,CAAC,YAAY,CAAC,IAAI;AAC3B;AAGO,SAAS,kBACd,IAAc,EACd,KAAW,EACX,IAAc,EACd,KAAW;IAEX,OAAO,CAAC,CACN,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,MAAM,KAAK,IAC9B,KAAK,CAAC,GAAG,MAAM,KAAK,IAAI,KAAK,CAAC,IAC9B,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,MAAM,MAAM,IAC/B,KAAK,CAAC,GAAG,MAAM,MAAM,IAAI,KAAK,CAAC,AACjC;AACF;AAGO,SAAS,WAAW,QAAkB,EAAE,WAAmB,CAAC;IACjE,OAAO;QACL,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG,YAAY;QACvC,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG,YAAY;IACzC;AACF;AAGO,SAAS,aACd,SAAmB,EACnB,QAAgB,EAChB,SAAmB;IAAE,GAAG;IAAG,GAAG;AAAE,CAAC;IAEjC,OAAO;QACL,GAAG,KAAK,KAAK,CAAC,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,IAAI;QACzC,GAAG,KAAK,KAAK,CAAC,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,IAAI;IAC3C;AACF;AAGO,SAAS,aACd,OAAiB,EACjB,QAAgB,EAChB,SAAmB;IAAE,GAAG;IAAG,GAAG;AAAE,CAAC;IAEjC,OAAO;QACL,GAAG,QAAQ,CAAC,GAAG,WAAW,OAAO,CAAC;QAClC,GAAG,QAAQ,CAAC,GAAG,WAAW,OAAO,CAAC;IACpC;AACF;AAGO,SAAS,aAAa,GAAW;IACtC,IAAI,OAAO,SAAS;QAClB,OAAO,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;IACtC,OAAO,IAAI,OAAO,MAAM;QACtB,OAAO,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,KAAK;IACnC;IACA,OAAO,IAAI,QAAQ;AACrB;AAGO,SAAS,WAAW,YAAoB;IAC7C,MAAM,UAAU,KAAK,KAAK,CAAC,eAAe;IAC1C,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IAEnC,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC/G,OAAO,IAAI,UAAU,GAAG;QACtB,OAAO,GAAG,QAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACnE,OAAO;QACL,OAAO,GAAG,QAAQ,CAAC,CAAC;IACtB;AACF;AAGO,SAAS,MAAM,KAAa,EAAE,GAAW,EAAE,GAAW;IAC3D,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,MAAM;AACxC;AAGO,SAAS,KAAK,KAAa,EAAE,GAAW,EAAE,MAAc;IAC7D,OAAO,QAAQ,CAAC,MAAM,KAAK,IAAI;AACjC;AAGO,SAAS,YAAY,KAAe,EAAE,IAAc,EAAE,IAAU;IACrE,OACE,MAAM,CAAC,IAAI,KAAK,CAAC,IACjB,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,KAAK,IAC7B,MAAM,CAAC,IAAI,KAAK,CAAC,IACjB,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,MAAM;AAElC;AAGO,SAAS,qBAAqB,SAAoB;IACvD,MAAM,UAAU,YAAY;IAC5B,OAAO,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC;AAChC;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/src/engine/simulation.ts"], "sourcesContent": ["import {\n  GameComponent,\n  ComponentType,\n  ResourceType,\n  COMPONENT_DEFINITIONS,\n  PerformanceMetrics,\n  GameState,\n} from '@/types/game';\n\nexport class SimulationEngine {\n  private lastUpdateTime: number = 0;\n  private readonly UPDATE_INTERVAL = 100; // 100ms updates\n\n  constructor() {\n    this.lastUpdateTime = Date.now();\n  }\n\n  public updateSimulation(gameState: GameState): Partial<GameState> {\n    const currentTime = Date.now();\n    const deltaTime = currentTime - this.lastUpdateTime;\n    \n    if (deltaTime < this.UPDATE_INTERVAL) {\n      return {};\n    }\n\n    this.lastUpdateTime = currentTime;\n\n    const updatedComponents = new Map(gameState.components);\n    const updatedResources = new Map(gameState.resources);\n    const productionStats = new Map<ResourceType, number>();\n    const consumptionStats = new Map<ResourceType, number>();\n\n    // Process each component\n    for (const [id, component] of updatedComponents) {\n      const updatedComponent = this.processComponent(\n        component,\n        updatedComponents,\n        deltaTime,\n        productionStats,\n        consumptionStats\n      );\n      updatedComponents.set(id, updatedComponent);\n    }\n\n    // Update global resources (for miners)\n    this.updateGlobalResources(updatedComponents, updatedResources, deltaTime);\n\n    // Calculate performance metrics\n    const metrics = this.calculatePerformanceMetrics(updatedComponents, productionStats, consumptionStats);\n\n    return {\n      components: updatedComponents,\n      resources: updatedResources,\n      gameTime: gameState.gameTime + deltaTime,\n      statistics: {\n        totalProduction: productionStats,\n        totalConsumption: consumptionStats,\n        efficiency: metrics.efficiency,\n        bottlenecks: metrics.bottlenecks,\n      },\n    };\n  }\n\n  private processComponent(\n    component: GameComponent,\n    allComponents: Map<string, GameComponent>,\n    deltaTime: number,\n    productionStats: Map<ResourceType, number>,\n    consumptionStats: Map<ResourceType, number>\n  ): GameComponent {\n    const definition = COMPONENT_DEFINITIONS[component.type];\n    const updatedComponent = { ...component };\n\n    switch (component.type) {\n      case ComponentType.MINER:\n        this.processMiner(updatedComponent, deltaTime, productionStats);\n        break;\n      case ComponentType.ASSEMBLER:\n        this.processAssembler(updatedComponent, deltaTime, productionStats, consumptionStats);\n        break;\n      case ComponentType.CONVEYOR:\n        this.processConveyor(updatedComponent, allComponents, deltaTime);\n        break;\n      case ComponentType.STORAGE:\n        this.processStorage(updatedComponent, allComponents, deltaTime);\n        break;\n      case ComponentType.SPLITTER:\n        this.processSplitter(updatedComponent, allComponents, deltaTime);\n        break;\n      case ComponentType.MERGER:\n        this.processMerger(updatedComponent, allComponents, deltaTime);\n        break;\n    }\n\n    return updatedComponent;\n  }\n\n  private processMiner(\n    component: GameComponent,\n    deltaTime: number,\n    productionStats: Map<ResourceType, number>\n  ): void {\n    const definition = COMPONENT_DEFINITIONS[component.type];\n    const timeSinceLastProcess = Date.now() - component.lastProcessTime;\n    const processInterval = 1000 / definition.speed; // Convert speed to interval\n\n    if (timeSinceLastProcess >= processInterval) {\n      // For simplicity, miners produce iron ore\n      const resourceType = ResourceType.IRON_ORE;\n      const currentAmount = component.inventory.get(resourceType) || 0;\n      const maxStorage = 50; // Max items a miner can store\n\n      if (currentAmount < maxStorage) {\n        component.inventory.set(resourceType, currentAmount + 1);\n        component.lastProcessTime = Date.now();\n        component.isActive = true;\n\n        // Update production stats\n        const currentProduction = productionStats.get(resourceType) || 0;\n        productionStats.set(resourceType, currentProduction + 1);\n      } else {\n        component.isActive = false; // Storage full\n      }\n    }\n  }\n\n  private processAssembler(\n    component: GameComponent,\n    deltaTime: number,\n    productionStats: Map<ResourceType, number>,\n    consumptionStats: Map<ResourceType, number>\n  ): void {\n    if (!component.recipe) {\n      component.isActive = false;\n      return;\n    }\n\n    const timeSinceLastProcess = Date.now() - component.lastProcessTime;\n    \n    if (timeSinceLastProcess >= component.recipe.processingTime) {\n      // Check if we have enough inputs\n      const canProcess = component.recipe.inputs.every(input => {\n        const available = component.inventory.get(input.resource) || 0;\n        return available >= input.amount;\n      });\n\n      if (canProcess) {\n        // Consume inputs\n        component.recipe.inputs.forEach(input => {\n          const current = component.inventory.get(input.resource) || 0;\n          component.inventory.set(input.resource, current - input.amount);\n          \n          // Update consumption stats\n          const currentConsumption = consumptionStats.get(input.resource) || 0;\n          consumptionStats.set(input.resource, currentConsumption + input.amount);\n        });\n\n        // Produce outputs\n        component.recipe.outputs.forEach(output => {\n          const current = component.inventory.get(output.resource) || 0;\n          component.inventory.set(output.resource, current + output.amount);\n          \n          // Update production stats\n          const currentProduction = productionStats.get(output.resource) || 0;\n          productionStats.set(output.resource, currentProduction + output.amount);\n        });\n\n        component.lastProcessTime = Date.now();\n        component.isActive = true;\n      } else {\n        component.isActive = false; // Waiting for inputs\n      }\n    }\n  }\n\n  private processConveyor(\n    component: GameComponent,\n    allComponents: Map<string, GameComponent>,\n    deltaTime: number\n  ): void {\n    const definition = COMPONENT_DEFINITIONS[component.type];\n    const transferRate = definition.speed * (deltaTime / 1000); // Items per update\n\n    // Try to move items to connected outputs\n    component.connections.outputs.forEach(outputId => {\n      const outputComponent = allComponents.get(outputId);\n      if (!outputComponent) return;\n\n      // Transfer items from this component to the output\n      for (const [resourceType, amount] of component.inventory) {\n        if (amount > 0) {\n          const transferAmount = Math.min(amount, Math.floor(transferRate));\n          if (transferAmount > 0) {\n            const outputCurrent = outputComponent.inventory.get(resourceType) || 0;\n            const maxCapacity = this.getComponentCapacity(outputComponent);\n            const canAccept = Math.min(transferAmount, maxCapacity - outputCurrent);\n\n            if (canAccept > 0) {\n              component.inventory.set(resourceType, amount - canAccept);\n              outputComponent.inventory.set(resourceType, outputCurrent + canAccept);\n              component.isActive = true;\n            }\n          }\n          break; // Only transfer one resource type per update\n        }\n      }\n    });\n  }\n\n  private processStorage(\n    component: GameComponent,\n    allComponents: Map<string, GameComponent>,\n    deltaTime: number\n  ): void {\n    // Storage just holds items, but can pass them through\n    this.processConveyor(component, allComponents, deltaTime);\n  }\n\n  private processSplitter(\n    component: GameComponent,\n    allComponents: Map<string, GameComponent>,\n    deltaTime: number\n  ): void {\n    const definition = COMPONENT_DEFINITIONS[component.type];\n    const transferRate = definition.speed * (deltaTime / 1000);\n\n    // Split items evenly between outputs\n    const outputs = component.connections.outputs\n      .map(id => allComponents.get(id))\n      .filter(comp => comp !== undefined);\n\n    if (outputs.length === 0) return;\n\n    for (const [resourceType, amount] of component.inventory) {\n      if (amount > 0) {\n        const transferAmount = Math.min(amount, Math.floor(transferRate));\n        const perOutput = Math.floor(transferAmount / outputs.length);\n\n        if (perOutput > 0) {\n          outputs.forEach(output => {\n            const outputCurrent = output!.inventory.get(resourceType) || 0;\n            const maxCapacity = this.getComponentCapacity(output!);\n            const canAccept = Math.min(perOutput, maxCapacity - outputCurrent);\n\n            if (canAccept > 0) {\n              const currentAmount = component.inventory.get(resourceType) || 0;\n              component.inventory.set(resourceType, currentAmount - canAccept);\n              output!.inventory.set(resourceType, outputCurrent + canAccept);\n              component.isActive = true;\n            }\n          });\n        }\n        break; // Only process one resource type per update\n      }\n    }\n  }\n\n  private processMerger(\n    component: GameComponent,\n    allComponents: Map<string, GameComponent>,\n    deltaTime: number\n  ): void {\n    // Merger just passes items through like a conveyor\n    this.processConveyor(component, allComponents, deltaTime);\n  }\n\n  private getComponentCapacity(component: GameComponent): number {\n    // Return max capacity for different component types\n    switch (component.type) {\n      case ComponentType.STORAGE:\n        return 1000;\n      case ComponentType.ASSEMBLER:\n        return 100;\n      case ComponentType.MINER:\n        return 50;\n      default:\n        return 10; // Default for conveyors, splitters, etc.\n    }\n  }\n\n  private updateGlobalResources(\n    components: Map<string, GameComponent>,\n    resources: Map<ResourceType, number>,\n    deltaTime: number\n  ): void {\n    // This could be used for global resource depletion, but for now we keep it simple\n  }\n\n  private calculatePerformanceMetrics(\n    components: Map<string, GameComponent>,\n    productionStats: Map<ResourceType, number>,\n    consumptionStats: Map<ResourceType, number>\n  ): PerformanceMetrics {\n    const throughput = new Map<ResourceType, number>();\n    const utilization = new Map<string, number>();\n    const bottlenecks: string[] = [];\n\n    // Calculate throughput (items per minute)\n    for (const [resource, amount] of productionStats) {\n      throughput.set(resource, amount * 600); // Convert to per minute\n    }\n\n    // Calculate utilization for each component\n    let totalUtilization = 0;\n    let activeComponents = 0;\n\n    for (const [id, component] of components) {\n      const util = component.isActive ? 100 : 0;\n      utilization.set(id, util);\n      totalUtilization += util;\n      activeComponents++;\n\n      // Identify bottlenecks (components that are inactive due to full outputs or empty inputs)\n      if (!component.isActive && component.type !== ComponentType.STORAGE) {\n        bottlenecks.push(id);\n      }\n    }\n\n    const efficiency = activeComponents > 0 ? totalUtilization / activeComponents / 100 : 0;\n\n    return {\n      throughput,\n      utilization,\n      bottlenecks,\n      efficiency,\n    };\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AASO,MAAM;IACH,iBAAyB,EAAE;IAClB,kBAAkB,IAAI;IAEvC,aAAc;QACZ,IAAI,CAAC,cAAc,GAAG,KAAK,GAAG;IAChC;IAEO,iBAAiB,SAAoB,EAAsB;QAChE,MAAM,cAAc,KAAK,GAAG;QAC5B,MAAM,YAAY,cAAc,IAAI,CAAC,cAAc;QAEnD,IAAI,YAAY,IAAI,CAAC,eAAe,EAAE;YACpC,OAAO,CAAC;QACV;QAEA,IAAI,CAAC,cAAc,GAAG;QAEtB,MAAM,oBAAoB,IAAI,IAAI,UAAU,UAAU;QACtD,MAAM,mBAAmB,IAAI,IAAI,UAAU,SAAS;QACpD,MAAM,kBAAkB,IAAI;QAC5B,MAAM,mBAAmB,IAAI;QAE7B,yBAAyB;QACzB,KAAK,MAAM,CAAC,IAAI,UAAU,IAAI,kBAAmB;YAC/C,MAAM,mBAAmB,IAAI,CAAC,gBAAgB,CAC5C,WACA,mBACA,WACA,iBACA;YAEF,kBAAkB,GAAG,CAAC,IAAI;QAC5B;QAEA,uCAAuC;QACvC,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,kBAAkB;QAEhE,gCAAgC;QAChC,MAAM,UAAU,IAAI,CAAC,2BAA2B,CAAC,mBAAmB,iBAAiB;QAErF,OAAO;YACL,YAAY;YACZ,WAAW;YACX,UAAU,UAAU,QAAQ,GAAG;YAC/B,YAAY;gBACV,iBAAiB;gBACjB,kBAAkB;gBAClB,YAAY,QAAQ,UAAU;gBAC9B,aAAa,QAAQ,WAAW;YAClC;QACF;IACF;IAEQ,iBACN,SAAwB,EACxB,aAAyC,EACzC,SAAiB,EACjB,eAA0C,EAC1C,gBAA2C,EAC5B;QACf,MAAM,aAAa,uHAAA,CAAA,wBAAqB,CAAC,UAAU,IAAI,CAAC;QACxD,MAAM,mBAAmB;YAAE,GAAG,SAAS;QAAC;QAExC,OAAQ,UAAU,IAAI;YACpB,KAAK,uHAAA,CAAA,gBAAa,CAAC,KAAK;gBACtB,IAAI,CAAC,YAAY,CAAC,kBAAkB,WAAW;gBAC/C;YACF,KAAK,uHAAA,CAAA,gBAAa,CAAC,SAAS;gBAC1B,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,WAAW,iBAAiB;gBACpE;YACF,KAAK,uHAAA,CAAA,gBAAa,CAAC,QAAQ;gBACzB,IAAI,CAAC,eAAe,CAAC,kBAAkB,eAAe;gBACtD;YACF,KAAK,uHAAA,CAAA,gBAAa,CAAC,OAAO;gBACxB,IAAI,CAAC,cAAc,CAAC,kBAAkB,eAAe;gBACrD;YACF,KAAK,uHAAA,CAAA,gBAAa,CAAC,QAAQ;gBACzB,IAAI,CAAC,eAAe,CAAC,kBAAkB,eAAe;gBACtD;YACF,KAAK,uHAAA,CAAA,gBAAa,CAAC,MAAM;gBACvB,IAAI,CAAC,aAAa,CAAC,kBAAkB,eAAe;gBACpD;QACJ;QAEA,OAAO;IACT;IAEQ,aACN,SAAwB,EACxB,SAAiB,EACjB,eAA0C,EACpC;QACN,MAAM,aAAa,uHAAA,CAAA,wBAAqB,CAAC,UAAU,IAAI,CAAC;QACxD,MAAM,uBAAuB,KAAK,GAAG,KAAK,UAAU,eAAe;QACnE,MAAM,kBAAkB,OAAO,WAAW,KAAK,EAAE,4BAA4B;QAE7E,IAAI,wBAAwB,iBAAiB;YAC3C,0CAA0C;YAC1C,MAAM,eAAe,uHAAA,CAAA,eAAY,CAAC,QAAQ;YAC1C,MAAM,gBAAgB,UAAU,SAAS,CAAC,GAAG,CAAC,iBAAiB;YAC/D,MAAM,aAAa,IAAI,8BAA8B;YAErD,IAAI,gBAAgB,YAAY;gBAC9B,UAAU,SAAS,CAAC,GAAG,CAAC,cAAc,gBAAgB;gBACtD,UAAU,eAAe,GAAG,KAAK,GAAG;gBACpC,UAAU,QAAQ,GAAG;gBAErB,0BAA0B;gBAC1B,MAAM,oBAAoB,gBAAgB,GAAG,CAAC,iBAAiB;gBAC/D,gBAAgB,GAAG,CAAC,cAAc,oBAAoB;YACxD,OAAO;gBACL,UAAU,QAAQ,GAAG,OAAO,eAAe;YAC7C;QACF;IACF;IAEQ,iBACN,SAAwB,EACxB,SAAiB,EACjB,eAA0C,EAC1C,gBAA2C,EACrC;QACN,IAAI,CAAC,UAAU,MAAM,EAAE;YACrB,UAAU,QAAQ,GAAG;YACrB;QACF;QAEA,MAAM,uBAAuB,KAAK,GAAG,KAAK,UAAU,eAAe;QAEnE,IAAI,wBAAwB,UAAU,MAAM,CAAC,cAAc,EAAE;YAC3D,iCAAiC;YACjC,MAAM,aAAa,UAAU,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;gBAC/C,MAAM,YAAY,UAAU,SAAS,CAAC,GAAG,CAAC,MAAM,QAAQ,KAAK;gBAC7D,OAAO,aAAa,MAAM,MAAM;YAClC;YAEA,IAAI,YAAY;gBACd,iBAAiB;gBACjB,UAAU,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;oBAC9B,MAAM,UAAU,UAAU,SAAS,CAAC,GAAG,CAAC,MAAM,QAAQ,KAAK;oBAC3D,UAAU,SAAS,CAAC,GAAG,CAAC,MAAM,QAAQ,EAAE,UAAU,MAAM,MAAM;oBAE9D,2BAA2B;oBAC3B,MAAM,qBAAqB,iBAAiB,GAAG,CAAC,MAAM,QAAQ,KAAK;oBACnE,iBAAiB,GAAG,CAAC,MAAM,QAAQ,EAAE,qBAAqB,MAAM,MAAM;gBACxE;gBAEA,kBAAkB;gBAClB,UAAU,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;oBAC/B,MAAM,UAAU,UAAU,SAAS,CAAC,GAAG,CAAC,OAAO,QAAQ,KAAK;oBAC5D,UAAU,SAAS,CAAC,GAAG,CAAC,OAAO,QAAQ,EAAE,UAAU,OAAO,MAAM;oBAEhE,0BAA0B;oBAC1B,MAAM,oBAAoB,gBAAgB,GAAG,CAAC,OAAO,QAAQ,KAAK;oBAClE,gBAAgB,GAAG,CAAC,OAAO,QAAQ,EAAE,oBAAoB,OAAO,MAAM;gBACxE;gBAEA,UAAU,eAAe,GAAG,KAAK,GAAG;gBACpC,UAAU,QAAQ,GAAG;YACvB,OAAO;gBACL,UAAU,QAAQ,GAAG,OAAO,qBAAqB;YACnD;QACF;IACF;IAEQ,gBACN,SAAwB,EACxB,aAAyC,EACzC,SAAiB,EACX;QACN,MAAM,aAAa,uHAAA,CAAA,wBAAqB,CAAC,UAAU,IAAI,CAAC;QACxD,MAAM,eAAe,WAAW,KAAK,GAAG,CAAC,YAAY,IAAI,GAAG,mBAAmB;QAE/E,yCAAyC;QACzC,UAAU,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YACpC,MAAM,kBAAkB,cAAc,GAAG,CAAC;YAC1C,IAAI,CAAC,iBAAiB;YAEtB,mDAAmD;YACnD,KAAK,MAAM,CAAC,cAAc,OAAO,IAAI,UAAU,SAAS,CAAE;gBACxD,IAAI,SAAS,GAAG;oBACd,MAAM,iBAAiB,KAAK,GAAG,CAAC,QAAQ,KAAK,KAAK,CAAC;oBACnD,IAAI,iBAAiB,GAAG;wBACtB,MAAM,gBAAgB,gBAAgB,SAAS,CAAC,GAAG,CAAC,iBAAiB;wBACrE,MAAM,cAAc,IAAI,CAAC,oBAAoB,CAAC;wBAC9C,MAAM,YAAY,KAAK,GAAG,CAAC,gBAAgB,cAAc;wBAEzD,IAAI,YAAY,GAAG;4BACjB,UAAU,SAAS,CAAC,GAAG,CAAC,cAAc,SAAS;4BAC/C,gBAAgB,SAAS,CAAC,GAAG,CAAC,cAAc,gBAAgB;4BAC5D,UAAU,QAAQ,GAAG;wBACvB;oBACF;oBACA,OAAO,6CAA6C;gBACtD;YACF;QACF;IACF;IAEQ,eACN,SAAwB,EACxB,aAAyC,EACzC,SAAiB,EACX;QACN,sDAAsD;QACtD,IAAI,CAAC,eAAe,CAAC,WAAW,eAAe;IACjD;IAEQ,gBACN,SAAwB,EACxB,aAAyC,EACzC,SAAiB,EACX;QACN,MAAM,aAAa,uHAAA,CAAA,wBAAqB,CAAC,UAAU,IAAI,CAAC;QACxD,MAAM,eAAe,WAAW,KAAK,GAAG,CAAC,YAAY,IAAI;QAEzD,qCAAqC;QACrC,MAAM,UAAU,UAAU,WAAW,CAAC,OAAO,CAC1C,GAAG,CAAC,CAAA,KAAM,cAAc,GAAG,CAAC,KAC5B,MAAM,CAAC,CAAA,OAAQ,SAAS;QAE3B,IAAI,QAAQ,MAAM,KAAK,GAAG;QAE1B,KAAK,MAAM,CAAC,cAAc,OAAO,IAAI,UAAU,SAAS,CAAE;YACxD,IAAI,SAAS,GAAG;gBACd,MAAM,iBAAiB,KAAK,GAAG,CAAC,QAAQ,KAAK,KAAK,CAAC;gBACnD,MAAM,YAAY,KAAK,KAAK,CAAC,iBAAiB,QAAQ,MAAM;gBAE5D,IAAI,YAAY,GAAG;oBACjB,QAAQ,OAAO,CAAC,CAAA;wBACd,MAAM,gBAAgB,OAAQ,SAAS,CAAC,GAAG,CAAC,iBAAiB;wBAC7D,MAAM,cAAc,IAAI,CAAC,oBAAoB,CAAC;wBAC9C,MAAM,YAAY,KAAK,GAAG,CAAC,WAAW,cAAc;wBAEpD,IAAI,YAAY,GAAG;4BACjB,MAAM,gBAAgB,UAAU,SAAS,CAAC,GAAG,CAAC,iBAAiB;4BAC/D,UAAU,SAAS,CAAC,GAAG,CAAC,cAAc,gBAAgB;4BACtD,OAAQ,SAAS,CAAC,GAAG,CAAC,cAAc,gBAAgB;4BACpD,UAAU,QAAQ,GAAG;wBACvB;oBACF;gBACF;gBACA,OAAO,4CAA4C;YACrD;QACF;IACF;IAEQ,cACN,SAAwB,EACxB,aAAyC,EACzC,SAAiB,EACX;QACN,mDAAmD;QACnD,IAAI,CAAC,eAAe,CAAC,WAAW,eAAe;IACjD;IAEQ,qBAAqB,SAAwB,EAAU;QAC7D,oDAAoD;QACpD,OAAQ,UAAU,IAAI;YACpB,KAAK,uHAAA,CAAA,gBAAa,CAAC,OAAO;gBACxB,OAAO;YACT,KAAK,uHAAA,CAAA,gBAAa,CAAC,SAAS;gBAC1B,OAAO;YACT,KAAK,uHAAA,CAAA,gBAAa,CAAC,KAAK;gBACtB,OAAO;YACT;gBACE,OAAO,IAAI,yCAAyC;QACxD;IACF;IAEQ,sBACN,UAAsC,EACtC,SAAoC,EACpC,SAAiB,EACX;IACN,kFAAkF;IACpF;IAEQ,4BACN,UAAsC,EACtC,eAA0C,EAC1C,gBAA2C,EACvB;QACpB,MAAM,aAAa,IAAI;QACvB,MAAM,cAAc,IAAI;QACxB,MAAM,cAAwB,EAAE;QAEhC,0CAA0C;QAC1C,KAAK,MAAM,CAAC,UAAU,OAAO,IAAI,gBAAiB;YAChD,WAAW,GAAG,CAAC,UAAU,SAAS,MAAM,wBAAwB;QAClE;QAEA,2CAA2C;QAC3C,IAAI,mBAAmB;QACvB,IAAI,mBAAmB;QAEvB,KAAK,MAAM,CAAC,IAAI,UAAU,IAAI,WAAY;YACxC,MAAM,OAAO,UAAU,QAAQ,GAAG,MAAM;YACxC,YAAY,GAAG,CAAC,IAAI;YACpB,oBAAoB;YACpB;YAEA,0FAA0F;YAC1F,IAAI,CAAC,UAAU,QAAQ,IAAI,UAAU,IAAI,KAAK,uHAAA,CAAA,gBAAa,CAAC,OAAO,EAAE;gBACnE,YAAY,IAAI,CAAC;YACnB;QACF;QAEA,MAAM,aAAa,mBAAmB,IAAI,mBAAmB,mBAAmB,MAAM;QAEtF,OAAO;YACL;YACA;YACA;YACA;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/src/analytics/performanceAnalyzer.ts"], "sourcesContent": ["import {\n  GameComponent,\n  ComponentType,\n  ResourceType,\n  PerformanceMetrics,\n  GameState,\n  COMPONENT_DEFINITIONS,\n} from '@/types/game';\n\nexport interface ComponentAnalytics {\n  id: string;\n  type: ComponentType;\n  utilization: number; // 0-1\n  throughput: number; // items per minute\n  efficiency: number; // 0-1\n  bottleneckScore: number; // 0-1, higher means more of a bottleneck\n  inputStarvation: number; // 0-1, higher means starved for inputs\n  outputBlocked: number; // 0-1, higher means outputs are blocked\n  averageInventory: number; // average items in inventory\n  processingTime: number; // average time to process items\n}\n\nexport interface ResourceFlowAnalytics {\n  resource: ResourceType;\n  totalProduction: number; // items per minute\n  totalConsumption: number; // items per minute\n  netFlow: number; // production - consumption\n  flowEfficiency: number; // 0-1\n  bottleneckComponents: string[]; // component IDs causing bottlenecks\n}\n\nexport interface FactoryAnalytics {\n  overallEfficiency: number; // 0-1\n  totalThroughput: number; // total items processed per minute\n  componentAnalytics: Map<string, ComponentAnalytics>;\n  resourceFlows: Map<ResourceType, ResourceFlowAnalytics>;\n  bottlenecks: string[]; // sorted by severity\n  recommendations: string[];\n  performanceScore: number; // 0-100\n}\n\nexport class PerformanceAnalyzer {\n  private historicalData: Map<string, number[]> = new Map();\n  private lastAnalysisTime: number = 0;\n  private readonly ANALYSIS_INTERVAL = 5000; // 5 seconds\n  private readonly HISTORY_LENGTH = 20; // Keep 20 data points\n\n  public analyzeFactory(gameState: GameState): FactoryAnalytics {\n    const currentTime = Date.now();\n    \n    // Only run full analysis periodically\n    if (currentTime - this.lastAnalysisTime < this.ANALYSIS_INTERVAL) {\n      return this.getLastAnalysis(gameState);\n    }\n\n    this.lastAnalysisTime = currentTime;\n\n    const componentAnalytics = this.analyzeComponents(gameState);\n    const resourceFlows = this.analyzeResourceFlows(gameState, componentAnalytics);\n    const bottlenecks = this.identifyBottlenecks(componentAnalytics);\n    const overallEfficiency = this.calculateOverallEfficiency(componentAnalytics);\n    const totalThroughput = this.calculateTotalThroughput(componentAnalytics);\n    const recommendations = this.generateRecommendations(componentAnalytics, resourceFlows, bottlenecks);\n    const performanceScore = this.calculatePerformanceScore(overallEfficiency, totalThroughput, bottlenecks.length);\n\n    // Store historical data\n    this.updateHistoricalData('efficiency', overallEfficiency);\n    this.updateHistoricalData('throughput', totalThroughput);\n\n    return {\n      overallEfficiency,\n      totalThroughput,\n      componentAnalytics,\n      resourceFlows,\n      bottlenecks,\n      recommendations,\n      performanceScore,\n    };\n  }\n\n  private analyzeComponents(gameState: GameState): Map<string, ComponentAnalytics> {\n    const analytics = new Map<string, ComponentAnalytics>();\n\n    for (const [id, component] of gameState.components) {\n      const definition = COMPONENT_DEFINITIONS[component.type];\n      const utilization = this.calculateUtilization(component);\n      const throughput = this.calculateThroughput(component);\n      const efficiency = this.calculateComponentEfficiency(component);\n      const bottleneckScore = this.calculateBottleneckScore(component, gameState);\n      const inputStarvation = this.calculateInputStarvation(component, gameState);\n      const outputBlocked = this.calculateOutputBlocked(component, gameState);\n      const averageInventory = this.calculateAverageInventory(component);\n      const processingTime = this.calculateProcessingTime(component);\n\n      analytics.set(id, {\n        id,\n        type: component.type,\n        utilization,\n        throughput,\n        efficiency,\n        bottleneckScore,\n        inputStarvation,\n        outputBlocked,\n        averageInventory,\n        processingTime,\n      });\n    }\n\n    return analytics;\n  }\n\n  private calculateUtilization(component: GameComponent): number {\n    // Simple utilization based on activity\n    return component.isActive ? 1.0 : 0.0;\n  }\n\n  private calculateThroughput(component: GameComponent): number {\n    const definition = COMPONENT_DEFINITIONS[component.type];\n    const baseSpeed = definition.speed;\n    \n    // Convert to items per minute\n    const itemsPerMinute = baseSpeed * 60;\n    \n    // Adjust based on actual utilization\n    return component.isActive ? itemsPerMinute : 0;\n  }\n\n  private calculateComponentEfficiency(component: GameComponent): number {\n    const definition = COMPONENT_DEFINITIONS[component.type];\n    \n    // For assemblers, check if they have the right inputs\n    if (component.type === ComponentType.ASSEMBLER && component.recipe) {\n      const hasAllInputs = component.recipe.inputs.every(input => {\n        const available = component.inventory.get(input.resource) || 0;\n        return available >= input.amount;\n      });\n      return hasAllInputs ? 1.0 : 0.5;\n    }\n\n    // For other components, efficiency is based on activity\n    return component.isActive ? 1.0 : 0.0;\n  }\n\n  private calculateBottleneckScore(component: GameComponent, gameState: GameState): number {\n    // A component is a bottleneck if it's blocking others or being blocked\n    let score = 0;\n\n    // Check if outputs are connected but not flowing\n    const outputComponents = component.connections.outputs\n      .map(id => gameState.components.get(id))\n      .filter(comp => comp !== undefined);\n\n    if (outputComponents.length > 0) {\n      const blockedOutputs = outputComponents.filter(comp => !comp!.isActive).length;\n      score += blockedOutputs / outputComponents.length * 0.5;\n    }\n\n    // Check if inputs are starved\n    const inputComponents = component.connections.inputs\n      .map(id => gameState.components.get(id))\n      .filter(comp => comp !== undefined);\n\n    if (inputComponents.length > 0) {\n      const starvedInputs = inputComponents.filter(comp => !comp!.isActive).length;\n      score += starvedInputs / inputComponents.length * 0.5;\n    }\n\n    return Math.min(score, 1.0);\n  }\n\n  private calculateInputStarvation(component: GameComponent, gameState: GameState): number {\n    if (component.type === ComponentType.MINER) return 0; // Miners don't need inputs\n\n    const totalInventory = Array.from(component.inventory.values()).reduce((sum, amount) => sum + amount, 0);\n    const maxCapacity = this.getComponentCapacity(component);\n    \n    return 1 - (totalInventory / Math.max(maxCapacity * 0.5, 1)); // Starved if less than 50% capacity\n  }\n\n  private calculateOutputBlocked(component: GameComponent, gameState: GameState): number {\n    const totalInventory = Array.from(component.inventory.values()).reduce((sum, amount) => sum + amount, 0);\n    const maxCapacity = this.getComponentCapacity(component);\n    \n    return totalInventory / Math.max(maxCapacity, 1); // Blocked if inventory is full\n  }\n\n  private calculateAverageInventory(component: GameComponent): number {\n    const totalInventory = Array.from(component.inventory.values()).reduce((sum, amount) => sum + amount, 0);\n    return totalInventory;\n  }\n\n  private calculateProcessingTime(component: GameComponent): number {\n    if (component.recipe) {\n      return component.recipe.processingTime;\n    }\n    \n    const definition = COMPONENT_DEFINITIONS[component.type];\n    return 1000 / definition.speed; // Convert speed to processing time\n  }\n\n  private getComponentCapacity(component: GameComponent): number {\n    switch (component.type) {\n      case ComponentType.STORAGE:\n        return 1000;\n      case ComponentType.ASSEMBLER:\n        return 100;\n      case ComponentType.MINER:\n        return 50;\n      default:\n        return 10;\n    }\n  }\n\n  private analyzeResourceFlows(\n    gameState: GameState,\n    componentAnalytics: Map<string, ComponentAnalytics>\n  ): Map<ResourceType, ResourceFlowAnalytics> {\n    const flows = new Map<ResourceType, ResourceFlowAnalytics>();\n\n    // Initialize all resource types\n    Object.values(ResourceType).forEach(resource => {\n      flows.set(resource, {\n        resource,\n        totalProduction: 0,\n        totalConsumption: 0,\n        netFlow: 0,\n        flowEfficiency: 0,\n        bottleneckComponents: [],\n      });\n    });\n\n    // Calculate production and consumption\n    for (const [id, component] of gameState.components) {\n      const analytics = componentAnalytics.get(id);\n      if (!analytics) continue;\n\n      // Production (miners and assemblers)\n      if (component.type === ComponentType.MINER) {\n        const flow = flows.get(ResourceType.IRON_ORE)!;\n        flow.totalProduction += analytics.throughput;\n      } else if (component.type === ComponentType.ASSEMBLER && component.recipe) {\n        component.recipe.outputs.forEach(output => {\n          const flow = flows.get(output.resource)!;\n          flow.totalProduction += analytics.throughput * output.amount;\n        });\n\n        component.recipe.inputs.forEach(input => {\n          const flow = flows.get(input.resource)!;\n          flow.totalConsumption += analytics.throughput * input.amount;\n        });\n      }\n\n      // Check for bottlenecks\n      if (analytics.bottleneckScore > 0.5) {\n        Object.values(ResourceType).forEach(resource => {\n          if (component.inventory.has(resource)) {\n            flows.get(resource)!.bottleneckComponents.push(id);\n          }\n        });\n      }\n    }\n\n    // Calculate net flow and efficiency\n    flows.forEach(flow => {\n      flow.netFlow = flow.totalProduction - flow.totalConsumption;\n      flow.flowEfficiency = flow.totalConsumption > 0 \n        ? Math.min(flow.totalProduction / flow.totalConsumption, 1.0)\n        : flow.totalProduction > 0 ? 1.0 : 0.0;\n    });\n\n    return flows;\n  }\n\n  private identifyBottlenecks(componentAnalytics: Map<string, ComponentAnalytics>): string[] {\n    return Array.from(componentAnalytics.entries())\n      .filter(([_, analytics]) => analytics.bottleneckScore > 0.3)\n      .sort((a, b) => b[1].bottleneckScore - a[1].bottleneckScore)\n      .map(([id, _]) => id);\n  }\n\n  private calculateOverallEfficiency(componentAnalytics: Map<string, ComponentAnalytics>): number {\n    if (componentAnalytics.size === 0) return 0;\n\n    const totalEfficiency = Array.from(componentAnalytics.values())\n      .reduce((sum, analytics) => sum + analytics.efficiency, 0);\n    \n    return totalEfficiency / componentAnalytics.size;\n  }\n\n  private calculateTotalThroughput(componentAnalytics: Map<string, ComponentAnalytics>): number {\n    return Array.from(componentAnalytics.values())\n      .reduce((sum, analytics) => sum + analytics.throughput, 0);\n  }\n\n  private generateRecommendations(\n    componentAnalytics: Map<string, ComponentAnalytics>,\n    resourceFlows: Map<ResourceType, ResourceFlowAnalytics>,\n    bottlenecks: string[]\n  ): string[] {\n    const recommendations: string[] = [];\n\n    // Check for bottlenecks\n    if (bottlenecks.length > 0) {\n      recommendations.push(`Address ${bottlenecks.length} bottleneck(s) to improve efficiency`);\n    }\n\n    // Check for resource imbalances\n    resourceFlows.forEach(flow => {\n      if (flow.netFlow < -10) {\n        recommendations.push(`Increase production of ${flow.resource.replace('_', ' ')}`);\n      } else if (flow.netFlow > 50) {\n        recommendations.push(`Consider using excess ${flow.resource.replace('_', ' ')}`);\n      }\n    });\n\n    // Check for underutilized components\n    const underutilized = Array.from(componentAnalytics.values())\n      .filter(analytics => analytics.utilization < 0.5);\n    \n    if (underutilized.length > 0) {\n      recommendations.push(`${underutilized.length} components are underutilized`);\n    }\n\n    return recommendations;\n  }\n\n  private calculatePerformanceScore(\n    efficiency: number,\n    throughput: number,\n    bottleneckCount: number\n  ): number {\n    const efficiencyScore = efficiency * 40; // 40 points max\n    const throughputScore = Math.min(throughput / 100, 1) * 40; // 40 points max\n    const bottleneckPenalty = bottleneckCount * 5; // -5 points per bottleneck\n    \n    return Math.max(0, Math.min(100, efficiencyScore + throughputScore - bottleneckPenalty));\n  }\n\n  private updateHistoricalData(key: string, value: number): void {\n    if (!this.historicalData.has(key)) {\n      this.historicalData.set(key, []);\n    }\n\n    const data = this.historicalData.get(key)!;\n    data.push(value);\n\n    // Keep only recent data\n    if (data.length > this.HISTORY_LENGTH) {\n      data.shift();\n    }\n  }\n\n  private getLastAnalysis(gameState: GameState): FactoryAnalytics {\n    // Return a simplified analysis for frequent updates\n    const componentAnalytics = new Map<string, ComponentAnalytics>();\n    \n    for (const [id, component] of gameState.components) {\n      componentAnalytics.set(id, {\n        id,\n        type: component.type,\n        utilization: component.isActive ? 1.0 : 0.0,\n        throughput: 0,\n        efficiency: 0,\n        bottleneckScore: 0,\n        inputStarvation: 0,\n        outputBlocked: 0,\n        averageInventory: 0,\n        processingTime: 0,\n      });\n    }\n\n    return {\n      overallEfficiency: 0,\n      totalThroughput: 0,\n      componentAnalytics,\n      resourceFlows: new Map(),\n      bottlenecks: [],\n      recommendations: [],\n      performanceScore: 0,\n    };\n  }\n\n  public getHistoricalData(key: string): number[] {\n    return this.historicalData.get(key) || [];\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAyCO,MAAM;IACH,iBAAwC,IAAI,MAAM;IAClD,mBAA2B,EAAE;IACpB,oBAAoB,KAAK;IACzB,iBAAiB,GAAG;IAE9B,eAAe,SAAoB,EAAoB;QAC5D,MAAM,cAAc,KAAK,GAAG;QAE5B,sCAAsC;QACtC,IAAI,cAAc,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,EAAE;YAChE,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B;QAEA,IAAI,CAAC,gBAAgB,GAAG;QAExB,MAAM,qBAAqB,IAAI,CAAC,iBAAiB,CAAC;QAClD,MAAM,gBAAgB,IAAI,CAAC,oBAAoB,CAAC,WAAW;QAC3D,MAAM,cAAc,IAAI,CAAC,mBAAmB,CAAC;QAC7C,MAAM,oBAAoB,IAAI,CAAC,0BAA0B,CAAC;QAC1D,MAAM,kBAAkB,IAAI,CAAC,wBAAwB,CAAC;QACtD,MAAM,kBAAkB,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,eAAe;QACxF,MAAM,mBAAmB,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,iBAAiB,YAAY,MAAM;QAE9G,wBAAwB;QACxB,IAAI,CAAC,oBAAoB,CAAC,cAAc;QACxC,IAAI,CAAC,oBAAoB,CAAC,cAAc;QAExC,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEQ,kBAAkB,SAAoB,EAAmC;QAC/E,MAAM,YAAY,IAAI;QAEtB,KAAK,MAAM,CAAC,IAAI,UAAU,IAAI,UAAU,UAAU,CAAE;YAClD,MAAM,aAAa,uHAAA,CAAA,wBAAqB,CAAC,UAAU,IAAI,CAAC;YACxD,MAAM,cAAc,IAAI,CAAC,oBAAoB,CAAC;YAC9C,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;YAC5C,MAAM,aAAa,IAAI,CAAC,4BAA4B,CAAC;YACrD,MAAM,kBAAkB,IAAI,CAAC,wBAAwB,CAAC,WAAW;YACjE,MAAM,kBAAkB,IAAI,CAAC,wBAAwB,CAAC,WAAW;YACjE,MAAM,gBAAgB,IAAI,CAAC,sBAAsB,CAAC,WAAW;YAC7D,MAAM,mBAAmB,IAAI,CAAC,yBAAyB,CAAC;YACxD,MAAM,iBAAiB,IAAI,CAAC,uBAAuB,CAAC;YAEpD,UAAU,GAAG,CAAC,IAAI;gBAChB;gBACA,MAAM,UAAU,IAAI;gBACpB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF;QAEA,OAAO;IACT;IAEQ,qBAAqB,SAAwB,EAAU;QAC7D,uCAAuC;QACvC,OAAO,UAAU,QAAQ,GAAG,MAAM;IACpC;IAEQ,oBAAoB,SAAwB,EAAU;QAC5D,MAAM,aAAa,uHAAA,CAAA,wBAAqB,CAAC,UAAU,IAAI,CAAC;QACxD,MAAM,YAAY,WAAW,KAAK;QAElC,8BAA8B;QAC9B,MAAM,iBAAiB,YAAY;QAEnC,qCAAqC;QACrC,OAAO,UAAU,QAAQ,GAAG,iBAAiB;IAC/C;IAEQ,6BAA6B,SAAwB,EAAU;QACrE,MAAM,aAAa,uHAAA,CAAA,wBAAqB,CAAC,UAAU,IAAI,CAAC;QAExD,sDAAsD;QACtD,IAAI,UAAU,IAAI,KAAK,uHAAA,CAAA,gBAAa,CAAC,SAAS,IAAI,UAAU,MAAM,EAAE;YAClE,MAAM,eAAe,UAAU,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;gBACjD,MAAM,YAAY,UAAU,SAAS,CAAC,GAAG,CAAC,MAAM,QAAQ,KAAK;gBAC7D,OAAO,aAAa,MAAM,MAAM;YAClC;YACA,OAAO,eAAe,MAAM;QAC9B;QAEA,wDAAwD;QACxD,OAAO,UAAU,QAAQ,GAAG,MAAM;IACpC;IAEQ,yBAAyB,SAAwB,EAAE,SAAoB,EAAU;QACvF,uEAAuE;QACvE,IAAI,QAAQ;QAEZ,iDAAiD;QACjD,MAAM,mBAAmB,UAAU,WAAW,CAAC,OAAO,CACnD,GAAG,CAAC,CAAA,KAAM,UAAU,UAAU,CAAC,GAAG,CAAC,KACnC,MAAM,CAAC,CAAA,OAAQ,SAAS;QAE3B,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,MAAM,iBAAiB,iBAAiB,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAM,QAAQ,EAAE,MAAM;YAC9E,SAAS,iBAAiB,iBAAiB,MAAM,GAAG;QACtD;QAEA,8BAA8B;QAC9B,MAAM,kBAAkB,UAAU,WAAW,CAAC,MAAM,CACjD,GAAG,CAAC,CAAA,KAAM,UAAU,UAAU,CAAC,GAAG,CAAC,KACnC,MAAM,CAAC,CAAA,OAAQ,SAAS;QAE3B,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,MAAM,gBAAgB,gBAAgB,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAM,QAAQ,EAAE,MAAM;YAC5E,SAAS,gBAAgB,gBAAgB,MAAM,GAAG;QACpD;QAEA,OAAO,KAAK,GAAG,CAAC,OAAO;IACzB;IAEQ,yBAAyB,SAAwB,EAAE,SAAoB,EAAU;QACvF,IAAI,UAAU,IAAI,KAAK,uHAAA,CAAA,gBAAa,CAAC,KAAK,EAAE,OAAO,GAAG,2BAA2B;QAEjF,MAAM,iBAAiB,MAAM,IAAI,CAAC,UAAU,SAAS,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,QAAQ;QACtG,MAAM,cAAc,IAAI,CAAC,oBAAoB,CAAC;QAE9C,OAAO,IAAK,iBAAiB,KAAK,GAAG,CAAC,cAAc,KAAK,IAAK,oCAAoC;IACpG;IAEQ,uBAAuB,SAAwB,EAAE,SAAoB,EAAU;QACrF,MAAM,iBAAiB,MAAM,IAAI,CAAC,UAAU,SAAS,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,QAAQ;QACtG,MAAM,cAAc,IAAI,CAAC,oBAAoB,CAAC;QAE9C,OAAO,iBAAiB,KAAK,GAAG,CAAC,aAAa,IAAI,+BAA+B;IACnF;IAEQ,0BAA0B,SAAwB,EAAU;QAClE,MAAM,iBAAiB,MAAM,IAAI,CAAC,UAAU,SAAS,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,QAAQ;QACtG,OAAO;IACT;IAEQ,wBAAwB,SAAwB,EAAU;QAChE,IAAI,UAAU,MAAM,EAAE;YACpB,OAAO,UAAU,MAAM,CAAC,cAAc;QACxC;QAEA,MAAM,aAAa,uHAAA,CAAA,wBAAqB,CAAC,UAAU,IAAI,CAAC;QACxD,OAAO,OAAO,WAAW,KAAK,EAAE,mCAAmC;IACrE;IAEQ,qBAAqB,SAAwB,EAAU;QAC7D,OAAQ,UAAU,IAAI;YACpB,KAAK,uHAAA,CAAA,gBAAa,CAAC,OAAO;gBACxB,OAAO;YACT,KAAK,uHAAA,CAAA,gBAAa,CAAC,SAAS;gBAC1B,OAAO;YACT,KAAK,uHAAA,CAAA,gBAAa,CAAC,KAAK;gBACtB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEQ,qBACN,SAAoB,EACpB,kBAAmD,EACT;QAC1C,MAAM,QAAQ,IAAI;QAElB,gCAAgC;QAChC,OAAO,MAAM,CAAC,uHAAA,CAAA,eAAY,EAAE,OAAO,CAAC,CAAA;YAClC,MAAM,GAAG,CAAC,UAAU;gBAClB;gBACA,iBAAiB;gBACjB,kBAAkB;gBAClB,SAAS;gBACT,gBAAgB;gBAChB,sBAAsB,EAAE;YAC1B;QACF;QAEA,uCAAuC;QACvC,KAAK,MAAM,CAAC,IAAI,UAAU,IAAI,UAAU,UAAU,CAAE;YAClD,MAAM,YAAY,mBAAmB,GAAG,CAAC;YACzC,IAAI,CAAC,WAAW;YAEhB,qCAAqC;YACrC,IAAI,UAAU,IAAI,KAAK,uHAAA,CAAA,gBAAa,CAAC,KAAK,EAAE;gBAC1C,MAAM,OAAO,MAAM,GAAG,CAAC,uHAAA,CAAA,eAAY,CAAC,QAAQ;gBAC5C,KAAK,eAAe,IAAI,UAAU,UAAU;YAC9C,OAAO,IAAI,UAAU,IAAI,KAAK,uHAAA,CAAA,gBAAa,CAAC,SAAS,IAAI,UAAU,MAAM,EAAE;gBACzE,UAAU,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;oBAC/B,MAAM,OAAO,MAAM,GAAG,CAAC,OAAO,QAAQ;oBACtC,KAAK,eAAe,IAAI,UAAU,UAAU,GAAG,OAAO,MAAM;gBAC9D;gBAEA,UAAU,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;oBAC9B,MAAM,OAAO,MAAM,GAAG,CAAC,MAAM,QAAQ;oBACrC,KAAK,gBAAgB,IAAI,UAAU,UAAU,GAAG,MAAM,MAAM;gBAC9D;YACF;YAEA,wBAAwB;YACxB,IAAI,UAAU,eAAe,GAAG,KAAK;gBACnC,OAAO,MAAM,CAAC,uHAAA,CAAA,eAAY,EAAE,OAAO,CAAC,CAAA;oBAClC,IAAI,UAAU,SAAS,CAAC,GAAG,CAAC,WAAW;wBACrC,MAAM,GAAG,CAAC,UAAW,oBAAoB,CAAC,IAAI,CAAC;oBACjD;gBACF;YACF;QACF;QAEA,oCAAoC;QACpC,MAAM,OAAO,CAAC,CAAA;YACZ,KAAK,OAAO,GAAG,KAAK,eAAe,GAAG,KAAK,gBAAgB;YAC3D,KAAK,cAAc,GAAG,KAAK,gBAAgB,GAAG,IAC1C,KAAK,GAAG,CAAC,KAAK,eAAe,GAAG,KAAK,gBAAgB,EAAE,OACvD,KAAK,eAAe,GAAG,IAAI,MAAM;QACvC;QAEA,OAAO;IACT;IAEQ,oBAAoB,kBAAmD,EAAY;QACzF,OAAO,MAAM,IAAI,CAAC,mBAAmB,OAAO,IACzC,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU,GAAK,UAAU,eAAe,GAAG,KACvD,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,CAAC,eAAe,GAAG,CAAC,CAAC,EAAE,CAAC,eAAe,EAC1D,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,GAAK;IACtB;IAEQ,2BAA2B,kBAAmD,EAAU;QAC9F,IAAI,mBAAmB,IAAI,KAAK,GAAG,OAAO;QAE1C,MAAM,kBAAkB,MAAM,IAAI,CAAC,mBAAmB,MAAM,IACzD,MAAM,CAAC,CAAC,KAAK,YAAc,MAAM,UAAU,UAAU,EAAE;QAE1D,OAAO,kBAAkB,mBAAmB,IAAI;IAClD;IAEQ,yBAAyB,kBAAmD,EAAU;QAC5F,OAAO,MAAM,IAAI,CAAC,mBAAmB,MAAM,IACxC,MAAM,CAAC,CAAC,KAAK,YAAc,MAAM,UAAU,UAAU,EAAE;IAC5D;IAEQ,wBACN,kBAAmD,EACnD,aAAuD,EACvD,WAAqB,EACX;QACV,MAAM,kBAA4B,EAAE;QAEpC,wBAAwB;QACxB,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,gBAAgB,IAAI,CAAC,CAAC,QAAQ,EAAE,YAAY,MAAM,CAAC,oCAAoC,CAAC;QAC1F;QAEA,gCAAgC;QAChC,cAAc,OAAO,CAAC,CAAA;YACpB,IAAI,KAAK,OAAO,GAAG,CAAC,IAAI;gBACtB,gBAAgB,IAAI,CAAC,CAAC,uBAAuB,EAAE,KAAK,QAAQ,CAAC,OAAO,CAAC,KAAK,MAAM;YAClF,OAAO,IAAI,KAAK,OAAO,GAAG,IAAI;gBAC5B,gBAAgB,IAAI,CAAC,CAAC,sBAAsB,EAAE,KAAK,QAAQ,CAAC,OAAO,CAAC,KAAK,MAAM;YACjF;QACF;QAEA,qCAAqC;QACrC,MAAM,gBAAgB,MAAM,IAAI,CAAC,mBAAmB,MAAM,IACvD,MAAM,CAAC,CAAA,YAAa,UAAU,WAAW,GAAG;QAE/C,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,gBAAgB,IAAI,CAAC,GAAG,cAAc,MAAM,CAAC,6BAA6B,CAAC;QAC7E;QAEA,OAAO;IACT;IAEQ,0BACN,UAAkB,EAClB,UAAkB,EAClB,eAAuB,EACf;QACR,MAAM,kBAAkB,aAAa,IAAI,gBAAgB;QACzD,MAAM,kBAAkB,KAAK,GAAG,CAAC,aAAa,KAAK,KAAK,IAAI,gBAAgB;QAC5E,MAAM,oBAAoB,kBAAkB,GAAG,2BAA2B;QAE1E,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,kBAAkB,kBAAkB;IACvE;IAEQ,qBAAqB,GAAW,EAAE,KAAa,EAAQ;QAC7D,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM;YACjC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,EAAE;QACjC;QAEA,MAAM,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QACrC,KAAK,IAAI,CAAC;QAEV,wBAAwB;QACxB,IAAI,KAAK,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE;YACrC,KAAK,KAAK;QACZ;IACF;IAEQ,gBAAgB,SAAoB,EAAoB;QAC9D,oDAAoD;QACpD,MAAM,qBAAqB,IAAI;QAE/B,KAAK,MAAM,CAAC,IAAI,UAAU,IAAI,UAAU,UAAU,CAAE;YAClD,mBAAmB,GAAG,CAAC,IAAI;gBACzB;gBACA,MAAM,UAAU,IAAI;gBACpB,aAAa,UAAU,QAAQ,GAAG,MAAM;gBACxC,YAAY;gBACZ,YAAY;gBACZ,iBAAiB;gBACjB,iBAAiB;gBACjB,eAAe;gBACf,kBAAkB;gBAClB,gBAAgB;YAClB;QACF;QAEA,OAAO;YACL,mBAAmB;YACnB,iBAAiB;YACjB;YACA,eAAe,IAAI;YACnB,aAAa,EAAE;YACf,iBAAiB,EAAE;YACnB,kBAAkB;QACpB;IACF;IAEO,kBAAkB,GAAW,EAAY;QAC9C,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE;IAC3C;AACF", "debugId": null}}, {"offset": {"line": 917, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/src/store/gameStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { subscribeWithSelector } from 'zustand/middleware';\nimport {\n  GameState,\n  GameComponent,\n  ComponentType,\n  Position,\n  Direction,\n  ResourceType,\n  PerformanceMetrics,\n  COMPONENT_DEFINITIONS,\n  RECIPES,\n} from '@/types/game';\nimport { generateId } from '@/utils/helpers';\nimport { SimulationEngine } from '@/engine/simulation';\nimport { PerformanceAnalyzer, FactoryAnalytics } from '@/analytics/performanceAnalyzer';\n\ninterface GameStore extends GameState {\n  // Analytics\n  factoryAnalytics: FactoryAnalytics | null;\n\n  // Actions\n  addComponent: (type: ComponentType, position: Position, direction?: Direction) => string | null;\n  removeComponent: (id: string) => void;\n  moveComponent: (id: string, position: Position) => void;\n  rotateComponent: (id: string) => void;\n  connectComponents: (fromId: string, toId: string) => boolean;\n  disconnectComponents: (fromId: string, toId: string) => void;\n  setComponentRecipe: (id: string, recipeId: string) => void;\n  toggleSimulation: () => void;\n  updateSimulation: () => void;\n  getPerformanceMetrics: () => PerformanceMetrics;\n  getFactoryAnalytics: () => FactoryAnalytics;\n  getHistoricalData: (key: string) => number[];\n  exportGameState: () => string;\n  importGameState: (jsonState: string) => boolean;\n  resetGame: () => void;\n}\n\nconst GRID_SIZE = { width: 50, height: 50 };\nconst simulationEngine = new SimulationEngine();\nconst performanceAnalyzer = new PerformanceAnalyzer();\n\nconst initialState: GameState = {\n  components: new Map(),\n  gridSize: GRID_SIZE,\n  isRunning: false,\n  gameTime: 0,\n  resources: new Map([\n    [ResourceType.IRON_ORE, 1000],\n    [ResourceType.COPPER_ORE, 1000],\n    [ResourceType.COAL, 1000],\n    [ResourceType.IRON_PLATE, 100],\n    [ResourceType.COPPER_PLATE, 100],\n    [ResourceType.GEAR, 50],\n    [ResourceType.CIRCUIT, 50],\n  ]),\n  statistics: {\n    totalProduction: new Map(),\n    totalConsumption: new Map(),\n    efficiency: 0,\n    bottlenecks: [],\n  },\n};\n\nconst initialStoreState = {\n  ...initialState,\n  factoryAnalytics: null as FactoryAnalytics | null,\n};\n\nexport const useGameStore = create<GameStore>()(\n  subscribeWithSelector((set, get) => ({\n    ...initialStoreState,\n\n    addComponent: (type: ComponentType, position: Position, direction = Direction.NORTH) => {\n      const state = get();\n      const definition = COMPONENT_DEFINITIONS[type];\n      \n      // Check if position is valid and not occupied\n      if (!isPositionValid(position, definition.size, state)) {\n        return null;\n      }\n\n      const id = generateId();\n      const component: GameComponent = {\n        id,\n        type,\n        position,\n        direction,\n        inventory: new Map(),\n        connections: { inputs: [], outputs: [] },\n        isActive: false,\n        lastProcessTime: 0,\n      };\n\n      set((state) => ({\n        components: new Map(state.components).set(id, component),\n      }));\n\n      return id;\n    },\n\n    removeComponent: (id: string) => {\n      set((state) => {\n        const newComponents = new Map(state.components);\n        const component = newComponents.get(id);\n        \n        if (component) {\n          // Remove all connections to this component\n          newComponents.forEach((comp) => {\n            comp.connections.inputs = comp.connections.inputs.filter(connId => connId !== id);\n            comp.connections.outputs = comp.connections.outputs.filter(connId => connId !== id);\n          });\n          \n          newComponents.delete(id);\n        }\n\n        return { components: newComponents };\n      });\n    },\n\n    moveComponent: (id: string, position: Position) => {\n      set((state) => {\n        const component = state.components.get(id);\n        if (!component) return state;\n\n        const definition = COMPONENT_DEFINITIONS[component.type];\n        if (!isPositionValid(position, definition.size, state, id)) {\n          return state;\n        }\n\n        const newComponents = new Map(state.components);\n        newComponents.set(id, { ...component, position });\n        return { components: newComponents };\n      });\n    },\n\n    rotateComponent: (id: string) => {\n      set((state) => {\n        const component = state.components.get(id);\n        if (!component) return state;\n\n        const newDirection = (component.direction + 1) % 4;\n        const newComponents = new Map(state.components);\n        newComponents.set(id, { ...component, direction: newDirection });\n        return { components: newComponents };\n      });\n    },\n\n    connectComponents: (fromId: string, toId: string) => {\n      const state = get();\n      const fromComponent = state.components.get(fromId);\n      const toComponent = state.components.get(toId);\n\n      if (!fromComponent || !toComponent || fromId === toId) {\n        return false;\n      }\n\n      const fromDef = COMPONENT_DEFINITIONS[fromComponent.type];\n      const toDef = COMPONENT_DEFINITIONS[toComponent.type];\n\n      // Check connection limits\n      if (fromComponent.connections.outputs.length >= fromDef.maxOutputs ||\n          toComponent.connections.inputs.length >= toDef.maxInputs) {\n        return false;\n      }\n\n      // Check if already connected\n      if (fromComponent.connections.outputs.includes(toId)) {\n        return false;\n      }\n\n      set((state) => {\n        const newComponents = new Map(state.components);\n        const newFromComponent = { ...fromComponent };\n        const newToComponent = { ...toComponent };\n\n        newFromComponent.connections.outputs.push(toId);\n        newToComponent.connections.inputs.push(fromId);\n\n        newComponents.set(fromId, newFromComponent);\n        newComponents.set(toId, newToComponent);\n\n        return { components: newComponents };\n      });\n\n      return true;\n    },\n\n    disconnectComponents: (fromId: string, toId: string) => {\n      set((state) => {\n        const newComponents = new Map(state.components);\n        const fromComponent = newComponents.get(fromId);\n        const toComponent = newComponents.get(toId);\n\n        if (fromComponent && toComponent) {\n          fromComponent.connections.outputs = fromComponent.connections.outputs.filter(id => id !== toId);\n          toComponent.connections.inputs = toComponent.connections.inputs.filter(id => id !== fromId);\n        }\n\n        return { components: newComponents };\n      });\n    },\n\n    setComponentRecipe: (id: string, recipeId: string) => {\n      set((state) => {\n        const component = state.components.get(id);\n        if (!component) return state;\n\n        const recipe = RECIPES[recipeId];\n        if (!recipe) return state;\n\n        const newComponents = new Map(state.components);\n        newComponents.set(id, { ...component, recipe });\n        return { components: newComponents };\n      });\n    },\n\n    toggleSimulation: () => {\n      set((state) => ({ isRunning: !state.isRunning }));\n    },\n\n    updateSimulation: () => {\n      const state = get();\n      if (!state.isRunning) return;\n\n      const updates = simulationEngine.updateSimulation(state);\n      if (Object.keys(updates).length > 0) {\n        const newState = { ...state, ...updates };\n        const analytics = performanceAnalyzer.analyzeFactory(newState);\n        set((currentState) => ({\n          ...currentState,\n          ...updates,\n          factoryAnalytics: analytics\n        }));\n      }\n    },\n\n    getPerformanceMetrics: (): PerformanceMetrics => {\n      const state = get();\n      return {\n        throughput: state.statistics.totalProduction,\n        utilization: new Map(),\n        bottlenecks: state.statistics.bottlenecks,\n        efficiency: state.statistics.efficiency,\n      };\n    },\n\n    getFactoryAnalytics: (): FactoryAnalytics => {\n      const state = get();\n      if (state.factoryAnalytics) {\n        return state.factoryAnalytics;\n      }\n      // Generate analytics on demand if not available\n      return performanceAnalyzer.analyzeFactory(state);\n    },\n\n    getHistoricalData: (key: string): number[] => {\n      return performanceAnalyzer.getHistoricalData(key);\n    },\n\n    exportGameState: () => {\n      const state = get();\n      const exportData = {\n        components: Array.from(state.components.entries()),\n        gridSize: state.gridSize,\n        gameTime: state.gameTime,\n        resources: Array.from(state.resources.entries()),\n      };\n      return JSON.stringify(exportData, null, 2);\n    },\n\n    importGameState: (jsonState: string) => {\n      try {\n        const data = JSON.parse(jsonState);\n        set({\n          components: new Map(data.components),\n          gridSize: data.gridSize || GRID_SIZE,\n          gameTime: data.gameTime || 0,\n          resources: new Map(data.resources),\n          isRunning: false,\n        });\n        return true;\n      } catch (error) {\n        console.error('Failed to import game state:', error);\n        return false;\n      }\n    },\n\n    resetGame: () => {\n      set(initialState);\n    },\n  }))\n);\n\n// Helper functions\nfunction isPositionValid(\n  position: Position,\n  size: { width: number; height: number },\n  state: GameState,\n  excludeId?: string\n): boolean {\n  // Check bounds\n  if (position.x < 0 || position.y < 0 ||\n      position.x + size.width > state.gridSize.width ||\n      position.y + size.height > state.gridSize.height) {\n    return false;\n  }\n\n  // Check for overlaps with existing components\n  for (const [id, component] of state.components) {\n    if (excludeId && id === excludeId) continue;\n    \n    const compDef = COMPONENT_DEFINITIONS[component.type];\n    const compPos = component.position;\n    \n    // Check if rectangles overlap\n    if (!(position.x >= compPos.x + compDef.size.width ||\n          position.x + size.width <= compPos.x ||\n          position.y >= compPos.y + compDef.size.height ||\n          position.y + size.height <= compPos.y)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAWA;AACA;AACA;;;;;;;AAwBA,MAAM,YAAY;IAAE,OAAO;IAAI,QAAQ;AAAG;AAC1C,MAAM,mBAAmB,IAAI,8HAAA,CAAA,mBAAgB;AAC7C,MAAM,sBAAsB,IAAI,0IAAA,CAAA,sBAAmB;AAEnD,MAAM,eAA0B;IAC9B,YAAY,IAAI;IAChB,UAAU;IACV,WAAW;IACX,UAAU;IACV,WAAW,IAAI,IAAI;QACjB;YAAC,uHAAA,CAAA,eAAY,CAAC,QAAQ;YAAE;SAAK;QAC7B;YAAC,uHAAA,CAAA,eAAY,CAAC,UAAU;YAAE;SAAK;QAC/B;YAAC,uHAAA,CAAA,eAAY,CAAC,IAAI;YAAE;SAAK;QACzB;YAAC,uHAAA,CAAA,eAAY,CAAC,UAAU;YAAE;SAAI;QAC9B;YAAC,uHAAA,CAAA,eAAY,CAAC,YAAY;YAAE;SAAI;QAChC;YAAC,uHAAA,CAAA,eAAY,CAAC,IAAI;YAAE;SAAG;QACvB;YAAC,uHAAA,CAAA,eAAY,CAAC,OAAO;YAAE;SAAG;KAC3B;IACD,YAAY;QACV,iBAAiB,IAAI;QACrB,kBAAkB,IAAI;QACtB,YAAY;QACZ,aAAa,EAAE;IACjB;AACF;AAEA,MAAM,oBAAoB;IACxB,GAAG,YAAY;IACf,kBAAkB;AACpB;AAEO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,KAAK,MAAQ,CAAC;QACnC,GAAG,iBAAiB;QAEpB,cAAc,CAAC,MAAqB,UAAoB,YAAY,uHAAA,CAAA,YAAS,CAAC,KAAK;YACjF,MAAM,QAAQ;YACd,MAAM,aAAa,uHAAA,CAAA,wBAAqB,CAAC,KAAK;YAE9C,8CAA8C;YAC9C,IAAI,CAAC,gBAAgB,UAAU,WAAW,IAAI,EAAE,QAAQ;gBACtD,OAAO;YACT;YAEA,MAAM,KAAK,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD;YACpB,MAAM,YAA2B;gBAC/B;gBACA;gBACA;gBACA;gBACA,WAAW,IAAI;gBACf,aAAa;oBAAE,QAAQ,EAAE;oBAAE,SAAS,EAAE;gBAAC;gBACvC,UAAU;gBACV,iBAAiB;YACnB;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,YAAY,IAAI,IAAI,MAAM,UAAU,EAAE,GAAG,CAAC,IAAI;gBAChD,CAAC;YAED,OAAO;QACT;QAEA,iBAAiB,CAAC;YAChB,IAAI,CAAC;gBACH,MAAM,gBAAgB,IAAI,IAAI,MAAM,UAAU;gBAC9C,MAAM,YAAY,cAAc,GAAG,CAAC;gBAEpC,IAAI,WAAW;oBACb,2CAA2C;oBAC3C,cAAc,OAAO,CAAC,CAAC;wBACrB,KAAK,WAAW,CAAC,MAAM,GAAG,KAAK,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,SAAU,WAAW;wBAC9E,KAAK,WAAW,CAAC,OAAO,GAAG,KAAK,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,SAAU,WAAW;oBAClF;oBAEA,cAAc,MAAM,CAAC;gBACvB;gBAEA,OAAO;oBAAE,YAAY;gBAAc;YACrC;QACF;QAEA,eAAe,CAAC,IAAY;YAC1B,IAAI,CAAC;gBACH,MAAM,YAAY,MAAM,UAAU,CAAC,GAAG,CAAC;gBACvC,IAAI,CAAC,WAAW,OAAO;gBAEvB,MAAM,aAAa,uHAAA,CAAA,wBAAqB,CAAC,UAAU,IAAI,CAAC;gBACxD,IAAI,CAAC,gBAAgB,UAAU,WAAW,IAAI,EAAE,OAAO,KAAK;oBAC1D,OAAO;gBACT;gBAEA,MAAM,gBAAgB,IAAI,IAAI,MAAM,UAAU;gBAC9C,cAAc,GAAG,CAAC,IAAI;oBAAE,GAAG,SAAS;oBAAE;gBAAS;gBAC/C,OAAO;oBAAE,YAAY;gBAAc;YACrC;QACF;QAEA,iBAAiB,CAAC;YAChB,IAAI,CAAC;gBACH,MAAM,YAAY,MAAM,UAAU,CAAC,GAAG,CAAC;gBACvC,IAAI,CAAC,WAAW,OAAO;gBAEvB,MAAM,eAAe,CAAC,UAAU,SAAS,GAAG,CAAC,IAAI;gBACjD,MAAM,gBAAgB,IAAI,IAAI,MAAM,UAAU;gBAC9C,cAAc,GAAG,CAAC,IAAI;oBAAE,GAAG,SAAS;oBAAE,WAAW;gBAAa;gBAC9D,OAAO;oBAAE,YAAY;gBAAc;YACrC;QACF;QAEA,mBAAmB,CAAC,QAAgB;YAClC,MAAM,QAAQ;YACd,MAAM,gBAAgB,MAAM,UAAU,CAAC,GAAG,CAAC;YAC3C,MAAM,cAAc,MAAM,UAAU,CAAC,GAAG,CAAC;YAEzC,IAAI,CAAC,iBAAiB,CAAC,eAAe,WAAW,MAAM;gBACrD,OAAO;YACT;YAEA,MAAM,UAAU,uHAAA,CAAA,wBAAqB,CAAC,cAAc,IAAI,CAAC;YACzD,MAAM,QAAQ,uHAAA,CAAA,wBAAqB,CAAC,YAAY,IAAI,CAAC;YAErD,0BAA0B;YAC1B,IAAI,cAAc,WAAW,CAAC,OAAO,CAAC,MAAM,IAAI,QAAQ,UAAU,IAC9D,YAAY,WAAW,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,SAAS,EAAE;gBAC5D,OAAO;YACT;YAEA,6BAA6B;YAC7B,IAAI,cAAc,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO;gBACpD,OAAO;YACT;YAEA,IAAI,CAAC;gBACH,MAAM,gBAAgB,IAAI,IAAI,MAAM,UAAU;gBAC9C,MAAM,mBAAmB;oBAAE,GAAG,aAAa;gBAAC;gBAC5C,MAAM,iBAAiB;oBAAE,GAAG,WAAW;gBAAC;gBAExC,iBAAiB,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC1C,eAAe,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC;gBAEvC,cAAc,GAAG,CAAC,QAAQ;gBAC1B,cAAc,GAAG,CAAC,MAAM;gBAExB,OAAO;oBAAE,YAAY;gBAAc;YACrC;YAEA,OAAO;QACT;QAEA,sBAAsB,CAAC,QAAgB;YACrC,IAAI,CAAC;gBACH,MAAM,gBAAgB,IAAI,IAAI,MAAM,UAAU;gBAC9C,MAAM,gBAAgB,cAAc,GAAG,CAAC;gBACxC,MAAM,cAAc,cAAc,GAAG,CAAC;gBAEtC,IAAI,iBAAiB,aAAa;oBAChC,cAAc,WAAW,CAAC,OAAO,GAAG,cAAc,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;oBAC1F,YAAY,WAAW,CAAC,MAAM,GAAG,YAAY,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;gBACtF;gBAEA,OAAO;oBAAE,YAAY;gBAAc;YACrC;QACF;QAEA,oBAAoB,CAAC,IAAY;YAC/B,IAAI,CAAC;gBACH,MAAM,YAAY,MAAM,UAAU,CAAC,GAAG,CAAC;gBACvC,IAAI,CAAC,WAAW,OAAO;gBAEvB,MAAM,SAAS,uHAAA,CAAA,UAAO,CAAC,SAAS;gBAChC,IAAI,CAAC,QAAQ,OAAO;gBAEpB,MAAM,gBAAgB,IAAI,IAAI,MAAM,UAAU;gBAC9C,cAAc,GAAG,CAAC,IAAI;oBAAE,GAAG,SAAS;oBAAE;gBAAO;gBAC7C,OAAO;oBAAE,YAAY;gBAAc;YACrC;QACF;QAEA,kBAAkB;YAChB,IAAI,CAAC,QAAU,CAAC;oBAAE,WAAW,CAAC,MAAM,SAAS;gBAAC,CAAC;QACjD;QAEA,kBAAkB;YAChB,MAAM,QAAQ;YACd,IAAI,CAAC,MAAM,SAAS,EAAE;YAEtB,MAAM,UAAU,iBAAiB,gBAAgB,CAAC;YAClD,IAAI,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG,GAAG;gBACnC,MAAM,WAAW;oBAAE,GAAG,KAAK;oBAAE,GAAG,OAAO;gBAAC;gBACxC,MAAM,YAAY,oBAAoB,cAAc,CAAC;gBACrD,IAAI,CAAC,eAAiB,CAAC;wBACrB,GAAG,YAAY;wBACf,GAAG,OAAO;wBACV,kBAAkB;oBACpB,CAAC;YACH;QACF;QAEA,uBAAuB;YACrB,MAAM,QAAQ;YACd,OAAO;gBACL,YAAY,MAAM,UAAU,CAAC,eAAe;gBAC5C,aAAa,IAAI;gBACjB,aAAa,MAAM,UAAU,CAAC,WAAW;gBACzC,YAAY,MAAM,UAAU,CAAC,UAAU;YACzC;QACF;QAEA,qBAAqB;YACnB,MAAM,QAAQ;YACd,IAAI,MAAM,gBAAgB,EAAE;gBAC1B,OAAO,MAAM,gBAAgB;YAC/B;YACA,gDAAgD;YAChD,OAAO,oBAAoB,cAAc,CAAC;QAC5C;QAEA,mBAAmB,CAAC;YAClB,OAAO,oBAAoB,iBAAiB,CAAC;QAC/C;QAEA,iBAAiB;YACf,MAAM,QAAQ;YACd,MAAM,aAAa;gBACjB,YAAY,MAAM,IAAI,CAAC,MAAM,UAAU,CAAC,OAAO;gBAC/C,UAAU,MAAM,QAAQ;gBACxB,UAAU,MAAM,QAAQ;gBACxB,WAAW,MAAM,IAAI,CAAC,MAAM,SAAS,CAAC,OAAO;YAC/C;YACA,OAAO,KAAK,SAAS,CAAC,YAAY,MAAM;QAC1C;QAEA,iBAAiB,CAAC;YAChB,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC;gBACxB,IAAI;oBACF,YAAY,IAAI,IAAI,KAAK,UAAU;oBACnC,UAAU,KAAK,QAAQ,IAAI;oBAC3B,UAAU,KAAK,QAAQ,IAAI;oBAC3B,WAAW,IAAI,IAAI,KAAK,SAAS;oBACjC,WAAW;gBACb;gBACA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,OAAO;YACT;QACF;QAEA,WAAW;YACT,IAAI;QACN;IACF,CAAC;AAGH,mBAAmB;AACnB,SAAS,gBACP,QAAkB,EAClB,IAAuC,EACvC,KAAgB,EAChB,SAAkB;IAElB,eAAe;IACf,IAAI,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,KAC/B,SAAS,CAAC,GAAG,KAAK,KAAK,GAAG,MAAM,QAAQ,CAAC,KAAK,IAC9C,SAAS,CAAC,GAAG,KAAK,MAAM,GAAG,MAAM,QAAQ,CAAC,MAAM,EAAE;QACpD,OAAO;IACT;IAEA,8CAA8C;IAC9C,KAAK,MAAM,CAAC,IAAI,UAAU,IAAI,MAAM,UAAU,CAAE;QAC9C,IAAI,aAAa,OAAO,WAAW;QAEnC,MAAM,UAAU,uHAAA,CAAA,wBAAqB,CAAC,UAAU,IAAI,CAAC;QACrD,MAAM,UAAU,UAAU,QAAQ;QAElC,8BAA8B;QAC9B,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI,QAAQ,CAAC,GAAG,QAAQ,IAAI,CAAC,KAAK,IAC5C,SAAS,CAAC,GAAG,KAAK,KAAK,IAAI,QAAQ,CAAC,IACpC,SAAS,CAAC,IAAI,QAAQ,CAAC,GAAG,QAAQ,IAAI,CAAC,MAAM,IAC7C,SAAS,CAAC,GAAG,KAAK,MAAM,IAAI,QAAQ,CAAC,GAAG;YAC5C,OAAO;QACT;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/src/components/FactoryComponent.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useDrag } from 'react-dnd';\nimport { GameComponent, Position, COMPONENT_DEFINITIONS, ComponentType } from '@/types/game';\nimport { useGameStore } from '@/store/gameStore';\nimport { screenToGrid, getRotationTransform } from '@/utils/helpers';\nimport { \n  ArrowRight, \n  Cog, \n  Package, \n  Pickaxe, \n  Split, \n  Merge,\n  RotateCw,\n  Settings\n} from 'lucide-react';\nimport clsx from 'clsx';\n\ninterface FactoryComponentProps {\n  component: GameComponent;\n  position: Position;\n  cellSize: number;\n  isSelected: boolean;\n  onSelect: () => void;\n  onMove: (newPosition: Position) => void;\n}\n\nconst FactoryComponent: React.FC<FactoryComponentProps> = ({\n  component,\n  position,\n  cellSize,\n  isSelected,\n  onSelect,\n  onMove,\n}) => {\n  const { rotateComponent, setComponentRecipe } = useGameStore();\n  const [showTooltip, setShowTooltip] = useState(false);\n  const definition = COMPONENT_DEFINITIONS[component.type];\n\n  const [{ isDragging }, drag] = useDrag({\n    type: 'placed-component',\n    item: { id: component.id, originalPosition: component.position },\n    end: (item, monitor) => {\n      const dropResult = monitor.getDropResult();\n      if (!dropResult) return;\n\n      const clientOffset = monitor.getClientOffset();\n      if (!clientOffset) return;\n\n      // Calculate new grid position\n      const newGridPos = screenToGrid(\n        { x: clientOffset.x, y: clientOffset.y },\n        cellSize\n      );\n      onMove(newGridPos);\n    },\n    collect: (monitor) => ({\n      isDragging: monitor.isDragging(),\n    }),\n  });\n\n  const getComponentIcon = () => {\n    switch (component.type) {\n      case ComponentType.CONVEYOR:\n        return <ArrowRight className=\"w-6 h-6\" />;\n      case ComponentType.MINER:\n        return <Pickaxe className=\"w-6 h-6\" />;\n      case ComponentType.ASSEMBLER:\n        return <Cog className=\"w-6 h-6\" />;\n      case ComponentType.STORAGE:\n        return <Package className=\"w-6 h-6\" />;\n      case ComponentType.SPLITTER:\n        return <Split className=\"w-6 h-6\" />;\n      case ComponentType.MERGER:\n        return <Merge className=\"w-6 h-6\" />;\n      default:\n        return <Cog className=\"w-6 h-6\" />;\n    }\n  };\n\n  const getComponentColor = () => {\n    switch (component.type) {\n      case ComponentType.CONVEYOR:\n        return 'bg-yellow-600 border-yellow-500';\n      case ComponentType.MINER:\n        return 'bg-blue-600 border-blue-500';\n      case ComponentType.ASSEMBLER:\n        return 'bg-green-600 border-green-500';\n      case ComponentType.STORAGE:\n        return 'bg-purple-600 border-purple-500';\n      case ComponentType.SPLITTER:\n        return 'bg-orange-600 border-orange-500';\n      case ComponentType.MERGER:\n        return 'bg-red-600 border-red-500';\n      default:\n        return 'bg-gray-600 border-gray-500';\n    }\n  };\n\n  const handleRightClick = (e: React.MouseEvent) => {\n    e.preventDefault();\n    rotateComponent(component.id);\n  };\n\n  const handleDoubleClick = () => {\n    if (component.type === ComponentType.ASSEMBLER) {\n      // Open recipe selection (simplified for now)\n      const recipeId = prompt('Enter recipe ID (iron_plate, copper_plate, gear, circuit):');\n      if (recipeId) {\n        setComponentRecipe(component.id, recipeId);\n      }\n    }\n  };\n\n  const width = definition.size.width * cellSize;\n  const height = definition.size.height * cellSize;\n\n  return (\n    <div\n      ref={drag as any}\n      className={clsx(\n        'absolute border-2 rounded cursor-pointer transition-all duration-200 flex items-center justify-center',\n        getComponentColor(),\n        {\n          'ring-2 ring-blue-400 ring-opacity-75': isSelected,\n          'opacity-50': isDragging,\n          'shadow-lg': component.isActive,\n          'animate-pulse': component.isActive && component.type === ComponentType.MINER,\n        }\n      )}\n      style={{\n        left: position.x,\n        top: position.y,\n        width,\n        height,\n        transform: getRotationTransform(component.direction),\n      }}\n      onClick={onSelect}\n      onContextMenu={handleRightClick}\n      onDoubleClick={handleDoubleClick}\n      onMouseEnter={() => setShowTooltip(true)}\n      onMouseLeave={() => setShowTooltip(false)}\n    >\n      {/* Component icon */}\n      <div className=\"text-white\">\n        {getComponentIcon()}\n      </div>\n\n      {/* Activity indicator */}\n      {component.isActive && (\n        <div className=\"absolute top-1 right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse\" />\n      )}\n\n      {/* Inventory indicator */}\n      {component.inventory.size > 0 && (\n        <div className=\"absolute bottom-1 left-1 text-xs text-white bg-black bg-opacity-50 rounded px-1\">\n          {Array.from(component.inventory.values()).reduce((sum, amount) => sum + amount, 0)}\n        </div>\n      )}\n\n      {/* Recipe indicator for assemblers */}\n      {component.type === ComponentType.ASSEMBLER && component.recipe && (\n        <div className=\"absolute top-1 left-1 text-xs text-white bg-black bg-opacity-50 rounded px-1\">\n          {component.recipe.name.slice(0, 3)}\n        </div>\n      )}\n\n      {/* Connection points */}\n      {definition.maxInputs > 0 && (\n        <div className=\"absolute -left-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-green-400 rounded-full\" />\n      )}\n      {definition.maxOutputs > 0 && (\n        <div className=\"absolute -right-1 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-red-400 rounded-full\" />\n      )}\n\n      {/* Tooltip */}\n      {showTooltip && (\n        <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black bg-opacity-90 text-white text-xs rounded whitespace-nowrap z-50\">\n          <div className=\"font-semibold\">{definition.name}</div>\n          <div>{definition.description}</div>\n          {component.recipe && (\n            <div className=\"text-yellow-300\">Recipe: {component.recipe.name}</div>\n          )}\n          <div className=\"text-gray-300\">\n            Right-click to rotate, Double-click for settings\n          </div>\n        </div>\n      )}\n\n      {/* Selection controls */}\n      {isSelected && (\n        <div className=\"absolute -top-8 left-0 flex gap-1\">\n          <button\n            onClick={(e) => {\n              e.stopPropagation();\n              rotateComponent(component.id);\n            }}\n            className=\"p-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\n            title=\"Rotate\"\n          >\n            <RotateCw className=\"w-3 h-3\" />\n          </button>\n          {component.type === ComponentType.ASSEMBLER && (\n            <button\n              onClick={(e) => {\n                e.stopPropagation();\n                handleDoubleClick();\n              }}\n              className=\"p-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors\"\n              title=\"Configure Recipe\"\n            >\n              <Settings className=\"w-3 h-3\" />\n            </button>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FactoryComponent;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAjBA;;;;;;;;AA4BA,MAAM,mBAAoD,CAAC,EACzD,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,MAAM,EACP;;IACC,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,aAAa,uHAAA,CAAA,wBAAqB,CAAC,UAAU,IAAI,CAAC;IAExD,MAAM,CAAC,EAAE,UAAU,EAAE,EAAE,KAAK,GAAG,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACrC,MAAM;QACN,MAAM;YAAE,IAAI,UAAU,EAAE;YAAE,kBAAkB,UAAU,QAAQ;QAAC;QAC/D,GAAG;wCAAE,CAAC,MAAM;gBACV,MAAM,aAAa,QAAQ,aAAa;gBACxC,IAAI,CAAC,YAAY;gBAEjB,MAAM,eAAe,QAAQ,eAAe;gBAC5C,IAAI,CAAC,cAAc;gBAEnB,8BAA8B;gBAC9B,MAAM,aAAa,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAC5B;oBAAE,GAAG,aAAa,CAAC;oBAAE,GAAG,aAAa,CAAC;gBAAC,GACvC;gBAEF,OAAO;YACT;;QACA,OAAO;wCAAE,CAAC,UAAY,CAAC;oBACrB,YAAY,QAAQ,UAAU;gBAChC,CAAC;;IACH;IAEA,MAAM,mBAAmB;QACvB,OAAQ,UAAU,IAAI;YACpB,KAAK,uHAAA,CAAA,gBAAa,CAAC,QAAQ;gBACzB,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK,uHAAA,CAAA,gBAAa,CAAC,KAAK;gBACtB,qBAAO,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK,uHAAA,CAAA,gBAAa,CAAC,SAAS;gBAC1B,qBAAO,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB,KAAK,uHAAA,CAAA,gBAAa,CAAC,OAAO;gBACxB,qBAAO,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK,uHAAA,CAAA,gBAAa,CAAC,QAAQ;gBACzB,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK,uHAAA,CAAA,gBAAa,CAAC,MAAM;gBACvB,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;QAC1B;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ,UAAU,IAAI;YACpB,KAAK,uHAAA,CAAA,gBAAa,CAAC,QAAQ;gBACzB,OAAO;YACT,KAAK,uHAAA,CAAA,gBAAa,CAAC,KAAK;gBACtB,OAAO;YACT,KAAK,uHAAA,CAAA,gBAAa,CAAC,SAAS;gBAC1B,OAAO;YACT,KAAK,uHAAA,CAAA,gBAAa,CAAC,OAAO;gBACxB,OAAO;YACT,KAAK,uHAAA,CAAA,gBAAa,CAAC,QAAQ;gBACzB,OAAO;YACT,KAAK,uHAAA,CAAA,gBAAa,CAAC,MAAM;gBACvB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,EAAE,cAAc;QAChB,gBAAgB,UAAU,EAAE;IAC9B;IAEA,MAAM,oBAAoB;QACxB,IAAI,UAAU,IAAI,KAAK,uHAAA,CAAA,gBAAa,CAAC,SAAS,EAAE;YAC9C,6CAA6C;YAC7C,MAAM,WAAW,OAAO;YACxB,IAAI,UAAU;gBACZ,mBAAmB,UAAU,EAAE,EAAE;YACnC;QACF;IACF;IAEA,MAAM,QAAQ,WAAW,IAAI,CAAC,KAAK,GAAG;IACtC,MAAM,SAAS,WAAW,IAAI,CAAC,MAAM,GAAG;IAExC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,yGACA,qBACA;YACE,wCAAwC;YACxC,cAAc;YACd,aAAa,UAAU,QAAQ;YAC/B,iBAAiB,UAAU,QAAQ,IAAI,UAAU,IAAI,KAAK,uHAAA,CAAA,gBAAa,CAAC,KAAK;QAC/E;QAEF,OAAO;YACL,MAAM,SAAS,CAAC;YAChB,KAAK,SAAS,CAAC;YACf;YACA;YACA,WAAW,CAAA,GAAA,0HAAA,CAAA,uBAAoB,AAAD,EAAE,UAAU,SAAS;QACrD;QACA,SAAS;QACT,eAAe;QACf,eAAe;QACf,cAAc,IAAM,eAAe;QACnC,cAAc,IAAM,eAAe;;0BAGnC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAIF,UAAU,QAAQ,kBACjB,6LAAC;gBAAI,WAAU;;;;;;YAIhB,UAAU,SAAS,CAAC,IAAI,GAAG,mBAC1B,6LAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC,UAAU,SAAS,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,QAAQ;;;;;;YAKnF,UAAU,IAAI,KAAK,uHAAA,CAAA,gBAAa,CAAC,SAAS,IAAI,UAAU,MAAM,kBAC7D,6LAAC;gBAAI,WAAU;0BACZ,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG;;;;;;YAKnC,WAAW,SAAS,GAAG,mBACtB,6LAAC;gBAAI,WAAU;;;;;;YAEhB,WAAW,UAAU,GAAG,mBACvB,6LAAC;gBAAI,WAAU;;;;;;YAIhB,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAiB,WAAW,IAAI;;;;;;kCAC/C,6LAAC;kCAAK,WAAW,WAAW;;;;;;oBAC3B,UAAU,MAAM,kBACf,6LAAC;wBAAI,WAAU;;4BAAkB;4BAAS,UAAU,MAAM,CAAC,IAAI;;;;;;;kCAEjE,6LAAC;wBAAI,WAAU;kCAAgB;;;;;;;;;;;;YAOlC,4BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,CAAC;4BACR,EAAE,eAAe;4BACjB,gBAAgB,UAAU,EAAE;wBAC9B;wBACA,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;oBAErB,UAAU,IAAI,KAAK,uHAAA,CAAA,gBAAa,CAAC,SAAS,kBACzC,6LAAC;wBACC,SAAS,CAAC;4BACR,EAAE,eAAe;4BACjB;wBACF;wBACA,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAOlC;GA/LM;;QAQ4C,4HAAA,CAAA,eAAY;QAI7B,sKAAA,CAAA,UAAO;;;KAZlC;uCAiMS", "debugId": null}}, {"offset": {"line": 1560, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/src/components/GridOverlay.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Position } from '@/types/game';\n\ninterface GridOverlayProps {\n  cellSize: number;\n  offset: Position;\n  width: number;\n  height: number;\n}\n\nconst GridOverlay: React.FC<GridOverlayProps> = ({ cellSize, offset, width, height }) => {\n  const gridLines = [];\n\n  // Calculate visible grid range\n  const startX = Math.floor(-offset.x / cellSize) * cellSize;\n  const startY = Math.floor(-offset.y / cellSize) * cellSize;\n  const endX = startX + width + cellSize;\n  const endY = startY + height + cellSize;\n\n  // Vertical lines\n  for (let x = startX; x <= endX; x += cellSize) {\n    gridLines.push(\n      <line\n        key={`v-${x}`}\n        x1={x}\n        y1={startY}\n        x2={x}\n        y2={endY}\n        stroke=\"rgba(255, 255, 255, 0.1)\"\n        strokeWidth=\"1\"\n      />\n    );\n  }\n\n  // Horizontal lines\n  for (let y = startY; y <= endY; y += cellSize) {\n    gridLines.push(\n      <line\n        key={`h-${y}`}\n        x1={startX}\n        y1={y}\n        x2={endX}\n        y2={y}\n        stroke=\"rgba(255, 255, 255, 0.1)\"\n        strokeWidth=\"1\"\n      />\n    );\n  }\n\n  return (\n    <svg\n      className=\"absolute inset-0 pointer-events-none\"\n      style={{\n        transform: `translate(${offset.x}px, ${offset.y}px)`,\n      }}\n    >\n      {gridLines}\n    </svg>\n  );\n};\n\nexport default GridOverlay;\n"], "names": [], "mappings": ";;;;AAAA;;AAYA,MAAM,cAA0C,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE;IAClF,MAAM,YAAY,EAAE;IAEpB,+BAA+B;IAC/B,MAAM,SAAS,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,GAAG,YAAY;IAClD,MAAM,SAAS,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,GAAG,YAAY;IAClD,MAAM,OAAO,SAAS,QAAQ;IAC9B,MAAM,OAAO,SAAS,SAAS;IAE/B,iBAAiB;IACjB,IAAK,IAAI,IAAI,QAAQ,KAAK,MAAM,KAAK,SAAU;QAC7C,UAAU,IAAI,eACZ,6LAAC;YAEC,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,QAAO;YACP,aAAY;WANP,CAAC,EAAE,EAAE,GAAG;;;;;IASnB;IAEA,mBAAmB;IACnB,IAAK,IAAI,IAAI,QAAQ,KAAK,MAAM,KAAK,SAAU;QAC7C,UAAU,IAAI,eACZ,6LAAC;YAEC,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,QAAO;YACP,aAAY;WANP,CAAC,EAAE,EAAE,GAAG;;;;;IASnB;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YACL,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC;QACtD;kBAEC;;;;;;AAGP;KAjDM;uCAmDS", "debugId": null}}, {"offset": {"line": 1628, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/src/components/GameBoard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useEffect, useState, useCallback } from 'react';\nimport { useDrop } from 'react-dnd';\nimport { useGameStore } from '@/store/gameStore';\nimport { ComponentType, Position } from '@/types/game';\nimport { screenToGrid, gridToScreen } from '@/utils/helpers';\nimport FactoryComponent from './FactoryComponent';\nimport GridOverlay from './GridOverlay';\n\nconst CELL_SIZE = 40;\nconst BOARD_WIDTH = 2000;\nconst BOARD_HEIGHT = 2000;\n\nconst GameBoard: React.FC = () => {\n  const boardRef = useRef<HTMLDivElement>(null);\n  const [viewOffset, setViewOffset] = useState<Position>({ x: 0, y: 0 });\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState<Position>({ x: 0, y: 0 });\n  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);\n\n  const {\n    components,\n    addComponent,\n    moveComponent,\n    removeComponent,\n    isRunning,\n    updateSimulation,\n  } = useGameStore();\n\n  // Animation loop for simulation\n  useEffect(() => {\n    if (!isRunning) return;\n\n    const interval = setInterval(() => {\n      updateSimulation();\n    }, 100);\n\n    return () => clearInterval(interval);\n  }, [isRunning, updateSimulation]);\n\n  // Drop handler for adding components\n  const [{ isOver }, drop] = useDrop({\n    accept: 'component',\n    drop: (item: { type: ComponentType }, monitor) => {\n      const clientOffset = monitor.getClientOffset();\n      if (!clientOffset || !boardRef.current) return;\n\n      const boardRect = boardRef.current.getBoundingClientRect();\n      const screenPos = {\n        x: clientOffset.x - boardRect.left,\n        y: clientOffset.y - boardRect.top,\n      };\n\n      const gridPos = screenToGrid(screenPos, CELL_SIZE, viewOffset);\n      addComponent(item.type, gridPos);\n    },\n    collect: (monitor) => ({\n      isOver: monitor.isOver(),\n    }),\n  });\n\n  // Pan handling\n  const handleMouseDown = useCallback((e: React.MouseEvent) => {\n    if (e.button === 1 || (e.button === 0 && e.ctrlKey)) { // Middle mouse or Ctrl+click\n      setIsDragging(true);\n      setDragStart({ x: e.clientX - viewOffset.x, y: e.clientY - viewOffset.y });\n      e.preventDefault();\n    }\n  }, [viewOffset]);\n\n  const handleMouseMove = useCallback((e: React.MouseEvent) => {\n    if (isDragging) {\n      setViewOffset({\n        x: e.clientX - dragStart.x,\n        y: e.clientY - dragStart.y,\n      });\n    }\n  }, [isDragging, dragStart]);\n\n  const handleMouseUp = useCallback(() => {\n    setIsDragging(false);\n  }, []);\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (e.key === 'Delete' && selectedComponent) {\n        removeComponent(selectedComponent);\n        setSelectedComponent(null);\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [selectedComponent, removeComponent]);\n\n  // Combine refs\n  const combinedRef = useCallback((node: HTMLDivElement) => {\n    boardRef.current = node;\n    drop(node);\n  }, [drop]);\n\n  return (\n    <div\n      ref={combinedRef}\n      className={`relative w-full h-full overflow-hidden bg-gray-900 cursor-${isDragging ? 'grabbing' : 'grab'} ${\n        isOver ? 'bg-gray-800' : ''\n      }`}\n      onMouseDown={handleMouseDown}\n      onMouseMove={handleMouseMove}\n      onMouseUp={handleMouseUp}\n      onMouseLeave={handleMouseUp}\n    >\n      {/* Grid overlay */}\n      <GridOverlay\n        cellSize={CELL_SIZE}\n        offset={viewOffset}\n        width={BOARD_WIDTH}\n        height={BOARD_HEIGHT}\n      />\n\n      {/* Components */}\n      <div\n        className=\"absolute\"\n        style={{\n          transform: `translate(${viewOffset.x}px, ${viewOffset.y}px)`,\n          width: BOARD_WIDTH,\n          height: BOARD_HEIGHT,\n        }}\n      >\n        {Array.from(components.entries()).map(([id, component]) => {\n          const screenPos = gridToScreen(component.position, CELL_SIZE);\n          return (\n            <FactoryComponent\n              key={id}\n              component={component}\n              position={screenPos}\n              cellSize={CELL_SIZE}\n              isSelected={selectedComponent === id}\n              onSelect={() => setSelectedComponent(id)}\n              onMove={(newGridPos) => moveComponent(id, newGridPos)}\n            />\n          );\n        })}\n      </div>\n\n      {/* Instructions overlay */}\n      {components.size === 0 && (\n        <div className=\"absolute inset-0 flex items-center justify-center pointer-events-none\">\n          <div className=\"text-center text-gray-400\">\n            <h3 className=\"text-xl font-semibold mb-2\">Welcome to Factory Builder!</h3>\n            <p className=\"mb-1\">Drag components from the left panel to start building</p>\n            <p className=\"mb-1\">Middle-click or Ctrl+click to pan the view</p>\n            <p>Press Delete to remove selected components</p>\n          </div>\n        </div>\n      )}\n\n      {/* Drop indicator */}\n      {isOver && (\n        <div className=\"absolute inset-0 bg-blue-500 bg-opacity-20 border-2 border-blue-500 border-dashed pointer-events-none\" />\n      )}\n    </div>\n  );\n};\n\nexport default GameBoard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;;;AARA;;;;;;;AAUA,MAAM,YAAY;AAClB,MAAM,cAAc;AACpB,MAAM,eAAe;AAErB,MAAM,YAAsB;;IAC1B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACxC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAE,GAAG;QAAG,GAAG;IAAE;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAE,GAAG;QAAG,GAAG;IAAE;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1E,MAAM,EACJ,UAAU,EACV,YAAY,EACZ,aAAa,EACb,eAAe,EACf,SAAS,EACT,gBAAgB,EACjB,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEf,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,WAAW;YAEhB,MAAM,WAAW;gDAAY;oBAC3B;gBACF;+CAAG;YAEH;uCAAO,IAAM,cAAc;;QAC7B;8BAAG;QAAC;QAAW;KAAiB;IAEhC,qCAAqC;IACrC,MAAM,CAAC,EAAE,MAAM,EAAE,EAAE,KAAK,GAAG,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACjC,QAAQ;QACR,IAAI;iCAAE,CAAC,MAA+B;gBACpC,MAAM,eAAe,QAAQ,eAAe;gBAC5C,IAAI,CAAC,gBAAgB,CAAC,SAAS,OAAO,EAAE;gBAExC,MAAM,YAAY,SAAS,OAAO,CAAC,qBAAqB;gBACxD,MAAM,YAAY;oBAChB,GAAG,aAAa,CAAC,GAAG,UAAU,IAAI;oBAClC,GAAG,aAAa,CAAC,GAAG,UAAU,GAAG;gBACnC;gBAEA,MAAM,UAAU,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,WAAW,WAAW;gBACnD,aAAa,KAAK,IAAI,EAAE;YAC1B;;QACA,OAAO;iCAAE,CAAC,UAAY,CAAC;oBACrB,QAAQ,QAAQ,MAAM;gBACxB,CAAC;;IACH;IAEA,eAAe;IACf,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YACnC,IAAI,EAAE,MAAM,KAAK,KAAM,EAAE,MAAM,KAAK,KAAK,EAAE,OAAO,EAAG;gBACnD,cAAc;gBACd,aAAa;oBAAE,GAAG,EAAE,OAAO,GAAG,WAAW,CAAC;oBAAE,GAAG,EAAE,OAAO,GAAG,WAAW,CAAC;gBAAC;gBACxE,EAAE,cAAc;YAClB;QACF;iDAAG;QAAC;KAAW;IAEf,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YACnC,IAAI,YAAY;gBACd,cAAc;oBACZ,GAAG,EAAE,OAAO,GAAG,UAAU,CAAC;oBAC1B,GAAG,EAAE,OAAO,GAAG,UAAU,CAAC;gBAC5B;YACF;QACF;iDAAG;QAAC;QAAY;KAAU;IAE1B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAChC,cAAc;QAChB;+CAAG,EAAE;IAEL,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;qDAAgB,CAAC;oBACrB,IAAI,EAAE,GAAG,KAAK,YAAY,mBAAmB;wBAC3C,gBAAgB;wBAChB,qBAAqB;oBACvB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;uCAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;8BAAG;QAAC;QAAmB;KAAgB;IAEvC,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,CAAC;YAC/B,SAAS,OAAO,GAAG;YACnB,KAAK;QACP;6CAAG;QAAC;KAAK;IAET,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAC,0DAA0D,EAAE,aAAa,aAAa,OAAO,CAAC,EACxG,SAAS,gBAAgB,IACzB;QACF,aAAa;QACb,aAAa;QACb,WAAW;QACX,cAAc;;0BAGd,6LAAC,oIAAA,CAAA,UAAW;gBACV,UAAU;gBACV,QAAQ;gBACR,OAAO;gBACP,QAAQ;;;;;;0BAIV,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,GAAG,CAAC;oBAC5D,OAAO;oBACP,QAAQ;gBACV;0BAEC,MAAM,IAAI,CAAC,WAAW,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,UAAU;oBACpD,MAAM,YAAY,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,UAAU,QAAQ,EAAE;oBACnD,qBACE,6LAAC,yIAAA,CAAA,UAAgB;wBAEf,WAAW;wBACX,UAAU;wBACV,UAAU;wBACV,YAAY,sBAAsB;wBAClC,UAAU,IAAM,qBAAqB;wBACrC,QAAQ,CAAC,aAAe,cAAc,IAAI;uBANrC;;;;;gBASX;;;;;;YAID,WAAW,IAAI,KAAK,mBACnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCAAO;;;;;;sCACpB,6LAAC;4BAAE,WAAU;sCAAO;;;;;;sCACpB,6LAAC;sCAAE;;;;;;;;;;;;;;;;;YAMR,wBACC,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAIvB;GAvJM;;QAcA,4HAAA,CAAA,eAAY;QAcW,sKAAA,CAAA,UAAO;;;KA5B9B;uCAyJS", "debugId": null}}, {"offset": {"line": 1890, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/src/components/ComponentPalette.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useDrag } from 'react-dnd';\nimport { ComponentType, COMPONENT_DEFINITIONS } from '@/types/game';\nimport { \n  ArrowRight, \n  Cog, \n  Package, \n  Pickaxe, \n  Split, \n  Merge,\n  ChevronDown,\n  ChevronRight\n} from 'lucide-react';\nimport clsx from 'clsx';\n\ninterface DraggableComponentProps {\n  type: ComponentType;\n  definition: typeof COMPONENT_DEFINITIONS[ComponentType];\n}\n\nconst DraggableComponent: React.FC<DraggableComponentProps> = ({ type, definition }) => {\n  const [{ isDragging }, drag] = useDrag({\n    type: 'component',\n    item: { type },\n    collect: (monitor) => ({\n      isDragging: monitor.isDragging(),\n    }),\n  });\n\n  const getIcon = () => {\n    switch (type) {\n      case ComponentType.CONVEYOR:\n        return <ArrowRight className=\"w-8 h-8\" />;\n      case ComponentType.MINER:\n        return <Pickaxe className=\"w-8 h-8\" />;\n      case ComponentType.ASSEMBLER:\n        return <Cog className=\"w-8 h-8\" />;\n      case ComponentType.STORAGE:\n        return <Package className=\"w-8 h-8\" />;\n      case ComponentType.SPLITTER:\n        return <Split className=\"w-8 h-8\" />;\n      case ComponentType.MERGER:\n        return <Merge className=\"w-8 h-8\" />;\n      default:\n        return <Cog className=\"w-8 h-8\" />;\n    }\n  };\n\n  const getColor = () => {\n    switch (type) {\n      case ComponentType.CONVEYOR:\n        return 'bg-yellow-600 hover:bg-yellow-500 border-yellow-500';\n      case ComponentType.MINER:\n        return 'bg-blue-600 hover:bg-blue-500 border-blue-500';\n      case ComponentType.ASSEMBLER:\n        return 'bg-green-600 hover:bg-green-500 border-green-500';\n      case ComponentType.STORAGE:\n        return 'bg-purple-600 hover:bg-purple-500 border-purple-500';\n      case ComponentType.SPLITTER:\n        return 'bg-orange-600 hover:bg-orange-500 border-orange-500';\n      case ComponentType.MERGER:\n        return 'bg-red-600 hover:bg-red-500 border-red-500';\n      default:\n        return 'bg-gray-600 hover:bg-gray-500 border-gray-500';\n    }\n  };\n\n  return (\n    <div\n      ref={drag as any}\n      className={clsx(\n        'p-4 border-2 rounded-lg cursor-grab transition-all duration-200 text-white',\n        getColor(),\n        {\n          'opacity-50 cursor-grabbing': isDragging,\n          'transform scale-105': !isDragging,\n        }\n      )}\n    >\n      <div className=\"flex items-center gap-3\">\n        <div className=\"flex-shrink-0\">\n          {getIcon()}\n        </div>\n        <div className=\"flex-1 min-w-0\">\n          <h3 className=\"font-semibold text-sm truncate\">{definition.name}</h3>\n          <p className=\"text-xs opacity-90 line-clamp-2\">{definition.description}</p>\n          <div className=\"mt-1 text-xs opacity-75\">\n            Speed: {definition.speed}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\ninterface CategorySectionProps {\n  title: string;\n  components: ComponentType[];\n  isExpanded: boolean;\n  onToggle: () => void;\n}\n\nconst CategorySection: React.FC<CategorySectionProps> = ({ \n  title, \n  components, \n  isExpanded, \n  onToggle \n}) => {\n  return (\n    <div className=\"mb-4\">\n      <button\n        onClick={onToggle}\n        className=\"w-full flex items-center justify-between p-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors\"\n      >\n        <span className=\"font-semibold text-white\">{title}</span>\n        {isExpanded ? (\n          <ChevronDown className=\"w-4 h-4 text-gray-300\" />\n        ) : (\n          <ChevronRight className=\"w-4 h-4 text-gray-300\" />\n        )}\n      </button>\n      \n      {isExpanded && (\n        <div className=\"mt-2 space-y-2\">\n          {components.map((type) => (\n            <DraggableComponent\n              key={type}\n              type={type}\n              definition={COMPONENT_DEFINITIONS[type]}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nconst ComponentPalette: React.FC = () => {\n  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(\n    new Set(['Transportation', 'Production'])\n  );\n\n  const toggleCategory = (category: string) => {\n    const newExpanded = new Set(expandedCategories);\n    if (newExpanded.has(category)) {\n      newExpanded.delete(category);\n    } else {\n      newExpanded.add(category);\n    }\n    setExpandedCategories(newExpanded);\n  };\n\n  const categories = [\n    {\n      title: 'Transportation',\n      components: [ComponentType.CONVEYOR, ComponentType.SPLITTER, ComponentType.MERGER],\n    },\n    {\n      title: 'Production',\n      components: [ComponentType.MINER, ComponentType.ASSEMBLER],\n    },\n    {\n      title: 'Storage',\n      components: [ComponentType.STORAGE],\n    },\n  ];\n\n  return (\n    <div className=\"h-full p-4 overflow-y-auto\">\n      <div className=\"mb-6\">\n        <h2 className=\"text-xl font-bold text-white mb-2\">Components</h2>\n        <p className=\"text-sm text-gray-300\">\n          Drag components onto the board to build your factory\n        </p>\n      </div>\n\n      {categories.map((category) => (\n        <CategorySection\n          key={category.title}\n          title={category.title}\n          components={category.components}\n          isExpanded={expandedCategories.has(category.title)}\n          onToggle={() => toggleCategory(category.title)}\n        />\n      ))}\n\n      <div className=\"mt-8 p-4 bg-gray-700 rounded-lg\">\n        <h3 className=\"font-semibold text-white mb-2\">Tips</h3>\n        <ul className=\"text-xs text-gray-300 space-y-1\">\n          <li>• Drag components to place them</li>\n          <li>• Right-click to rotate</li>\n          <li>• Double-click assemblers to set recipes</li>\n          <li>• Connect components by placing them adjacent</li>\n          <li>• Use splitters to divide resource flows</li>\n          <li>• Use mergers to combine resource flows</li>\n        </ul>\n      </div>\n    </div>\n  );\n};\n\nexport default ComponentPalette;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAfA;;;;;;AAsBA,MAAM,qBAAwD,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE;;IACjF,MAAM,CAAC,EAAE,UAAU,EAAE,EAAE,KAAK,GAAG,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACrC,MAAM;QACN,MAAM;YAAE;QAAK;QACb,OAAO;0CAAE,CAAC,UAAY,CAAC;oBACrB,YAAY,QAAQ,UAAU;gBAChC,CAAC;;IACH;IAEA,MAAM,UAAU;QACd,OAAQ;YACN,KAAK,uHAAA,CAAA,gBAAa,CAAC,QAAQ;gBACzB,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK,uHAAA,CAAA,gBAAa,CAAC,KAAK;gBACtB,qBAAO,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK,uHAAA,CAAA,gBAAa,CAAC,SAAS;gBAC1B,qBAAO,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB,KAAK,uHAAA,CAAA,gBAAa,CAAC,OAAO;gBACxB,qBAAO,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK,uHAAA,CAAA,gBAAa,CAAC,QAAQ;gBACzB,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK,uHAAA,CAAA,gBAAa,CAAC,MAAM;gBACvB,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;QAC1B;IACF;IAEA,MAAM,WAAW;QACf,OAAQ;YACN,KAAK,uHAAA,CAAA,gBAAa,CAAC,QAAQ;gBACzB,OAAO;YACT,KAAK,uHAAA,CAAA,gBAAa,CAAC,KAAK;gBACtB,OAAO;YACT,KAAK,uHAAA,CAAA,gBAAa,CAAC,SAAS;gBAC1B,OAAO;YACT,KAAK,uHAAA,CAAA,gBAAa,CAAC,OAAO;gBACxB,OAAO;YACT,KAAK,uHAAA,CAAA,gBAAa,CAAC,QAAQ;gBACzB,OAAO;YACT,KAAK,uHAAA,CAAA,gBAAa,CAAC,MAAM;gBACvB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,8EACA,YACA;YACE,8BAA8B;YAC9B,uBAAuB,CAAC;QAC1B;kBAGF,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ;;;;;;8BAEH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAkC,WAAW,IAAI;;;;;;sCAC/D,6LAAC;4BAAE,WAAU;sCAAmC,WAAW,WAAW;;;;;;sCACtE,6LAAC;4BAAI,WAAU;;gCAA0B;gCAC/B,WAAW,KAAK;;;;;;;;;;;;;;;;;;;;;;;;AAMpC;GAzEM;;QAC2B,sKAAA,CAAA,UAAO;;;KADlC;AAkFN,MAAM,kBAAkD,CAAC,EACvD,KAAK,EACL,UAAU,EACV,UAAU,EACV,QAAQ,EACT;IACC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,6LAAC;wBAAK,WAAU;kCAA4B;;;;;;oBAC3C,2BACC,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;6CAEvB,6LAAC,yNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;YAI3B,4BACC,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;wBAEC,MAAM;wBACN,YAAY,uHAAA,CAAA,wBAAqB,CAAC,KAAK;uBAFlC;;;;;;;;;;;;;;;;AASnB;MAjCM;AAmCN,MAAM,mBAA6B;;IACjC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACzD,IAAI,IAAI;QAAC;QAAkB;KAAa;IAG1C,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,WAAW;YAC7B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,sBAAsB;IACxB;IAEA,MAAM,aAAa;QACjB;YACE,OAAO;YACP,YAAY;gBAAC,uHAAA,CAAA,gBAAa,CAAC,QAAQ;gBAAE,uHAAA,CAAA,gBAAa,CAAC,QAAQ;gBAAE,uHAAA,CAAA,gBAAa,CAAC,MAAM;aAAC;QACpF;QACA;YACE,OAAO;YACP,YAAY;gBAAC,uHAAA,CAAA,gBAAa,CAAC,KAAK;gBAAE,uHAAA,CAAA,gBAAa,CAAC,SAAS;aAAC;QAC5D;QACA;YACE,OAAO;YACP,YAAY;gBAAC,uHAAA,CAAA,gBAAa,CAAC,OAAO;aAAC;QACrC;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;YAKtC,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;oBAEC,OAAO,SAAS,KAAK;oBACrB,YAAY,SAAS,UAAU;oBAC/B,YAAY,mBAAmB,GAAG,CAAC,SAAS,KAAK;oBACjD,UAAU,IAAM,eAAe,SAAS,KAAK;mBAJxC,SAAS,KAAK;;;;;0BAQvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAgC;;;;;;kCAC9C,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd;IA9DM;MAAA;uCAgES", "debugId": null}}, {"offset": {"line": 2301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/src/components/AIAssistant.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useGameStore } from '@/store/gameStore';\nimport { GameStateSerializer } from '@/ai/gameStateSerializer';\nimport { \n  Brain, \n  Copy, \n  Download, \n  Upload, \n  Loader2,\n  CheckCircle,\n  AlertCircle,\n  MessageSquare\n} from 'lucide-react';\nimport clsx from 'clsx';\n\ninterface AIAnalysisResult {\n  serializedState: any;\n  aiPrompt: string;\n  metadata: {\n    componentCount: number;\n    efficiency: number;\n    isRunning: boolean;\n    timestamp: number;\n  };\n}\n\nconst AIAssistant: React.FC = () => {\n  const { \n    components, \n    resources, \n    statistics, \n    gridSize, \n    gameTime, \n    isRunning,\n    getFactoryAnalytics \n  } = useGameStore();\n\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [analysisResult, setAnalysisResult] = useState<AIAnalysisResult | null>(null);\n  const [error, setError] = useState<string | null>(null);\n  const [copySuccess, setCopySuccess] = useState(false);\n\n  const handleAnalyze = async () => {\n    setIsAnalyzing(true);\n    setError(null);\n\n    try {\n      const gameState = {\n        components,\n        resources,\n        statistics,\n        gridSize,\n        gameTime,\n        isRunning,\n      };\n\n      const analytics = getFactoryAnalytics();\n\n      const response = await fetch('/api/ai/analyze', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          gameState,\n          analytics,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to analyze factory');\n      }\n\n      const result = await response.json();\n      setAnalysisResult(result.data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Unknown error occurred');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n  const handleCopyPrompt = async () => {\n    if (!analysisResult) return;\n\n    try {\n      await navigator.clipboard.writeText(analysisResult.aiPrompt);\n      setCopySuccess(true);\n      setTimeout(() => setCopySuccess(false), 2000);\n    } catch (err) {\n      setError('Failed to copy to clipboard');\n    }\n  };\n\n  const handleDownloadState = () => {\n    if (!analysisResult) return;\n\n    const blob = new Blob([JSON.stringify(analysisResult.serializedState, null, 2)], {\n      type: 'application/json',\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `factory-state-${Date.now()}.json`;\n    a.click();\n    URL.revokeObjectURL(url);\n  };\n\n  const handleDownloadPrompt = () => {\n    if (!analysisResult) return;\n\n    const blob = new Blob([analysisResult.aiPrompt], {\n      type: 'text/plain',\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `factory-analysis-${Date.now()}.txt`;\n    a.click();\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div className=\"bg-gray-800 rounded-lg p-4\">\n      <div className=\"flex items-center gap-2 mb-4\">\n        <Brain className=\"w-5 h-5 text-purple-400\" />\n        <h3 className=\"text-lg font-semibold text-white\">AI Assistant</h3>\n      </div>\n\n      <div className=\"space-y-4\">\n        <div className=\"text-sm text-gray-300\">\n          Generate an AI-friendly analysis of your factory for optimization suggestions.\n        </div>\n\n        <button\n          onClick={handleAnalyze}\n          disabled={isAnalyzing || components.size === 0}\n          className={clsx(\n            'w-full flex items-center justify-center gap-2 p-3 rounded-lg font-medium transition-colors',\n            {\n              'bg-purple-600 hover:bg-purple-700 text-white': !isAnalyzing && components.size > 0,\n              'bg-gray-600 text-gray-400 cursor-not-allowed': isAnalyzing || components.size === 0,\n            }\n          )}\n        >\n          {isAnalyzing ? (\n            <>\n              <Loader2 className=\"w-4 h-4 animate-spin\" />\n              Analyzing Factory...\n            </>\n          ) : (\n            <>\n              <MessageSquare className=\"w-4 h-4\" />\n              Generate AI Analysis\n            </>\n          )}\n        </button>\n\n        {components.size === 0 && (\n          <div className=\"text-sm text-yellow-400 flex items-center gap-2\">\n            <AlertCircle className=\"w-4 h-4\" />\n            Add some components to your factory first\n          </div>\n        )}\n\n        {error && (\n          <div className=\"text-sm text-red-400 flex items-center gap-2\">\n            <AlertCircle className=\"w-4 h-4\" />\n            {error}\n          </div>\n        )}\n\n        {analysisResult && (\n          <div className=\"space-y-4 border-t border-gray-700 pt-4\">\n            <div className=\"flex items-center gap-2 text-green-400\">\n              <CheckCircle className=\"w-4 h-4\" />\n              <span className=\"text-sm\">Analysis Complete</span>\n            </div>\n\n            <div className=\"grid grid-cols-2 gap-4 text-sm\">\n              <div className=\"bg-gray-700 rounded p-3\">\n                <div className=\"text-gray-300\">Components</div>\n                <div className=\"text-white font-semibold\">\n                  {analysisResult.metadata.componentCount}\n                </div>\n              </div>\n              <div className=\"bg-gray-700 rounded p-3\">\n                <div className=\"text-gray-300\">Efficiency</div>\n                <div className=\"text-white font-semibold\">\n                  {(analysisResult.metadata.efficiency * 100).toFixed(1)}%\n                </div>\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <div className=\"text-sm font-medium text-white\">Actions</div>\n              \n              <div className=\"grid grid-cols-2 gap-2\">\n                <button\n                  onClick={handleCopyPrompt}\n                  className=\"flex items-center justify-center gap-2 p-2 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors\"\n                >\n                  {copySuccess ? (\n                    <>\n                      <CheckCircle className=\"w-4 h-4\" />\n                      Copied!\n                    </>\n                  ) : (\n                    <>\n                      <Copy className=\"w-4 h-4\" />\n                      Copy Prompt\n                    </>\n                  )}\n                </button>\n\n                <button\n                  onClick={handleDownloadPrompt}\n                  className=\"flex items-center justify-center gap-2 p-2 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors\"\n                >\n                  <Download className=\"w-4 h-4\" />\n                  Download\n                </button>\n              </div>\n\n              <button\n                onClick={handleDownloadState}\n                className=\"w-full flex items-center justify-center gap-2 p-2 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm transition-colors\"\n              >\n                <Upload className=\"w-4 h-4\" />\n                Download State JSON\n              </button>\n            </div>\n\n            <div className=\"text-xs text-gray-400\">\n              Use the copied prompt with any AI assistant (ChatGPT, Claude, etc.) to get optimization suggestions for your factory.\n            </div>\n          </div>\n        )}\n\n        <div className=\"border-t border-gray-700 pt-4\">\n          <div className=\"text-sm font-medium text-white mb-2\">How to use:</div>\n          <ol className=\"text-xs text-gray-300 space-y-1\">\n            <li>1. Build your factory with components</li>\n            <li>2. Click \"Generate AI Analysis\"</li>\n            <li>3. Copy the generated prompt</li>\n            <li>4. Paste it into any AI assistant</li>\n            <li>5. Get optimization suggestions!</li>\n          </ol>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AIAssistant;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAfA;;;;;AA4BA,MAAM,cAAwB;;IAC5B,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,mBAAmB,EACpB,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAC9E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB;QACpB,eAAe;QACf,SAAS;QAET,IAAI;YACF,MAAM,YAAY;gBAChB;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YAEA,MAAM,YAAY;YAElB,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,kBAAkB,OAAO,IAAI;QAC/B,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,eAAe,QAAQ;YAC3D,eAAe;YACf,WAAW,IAAM,eAAe,QAAQ;QAC1C,EAAE,OAAO,KAAK;YACZ,SAAS;QACX;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,gBAAgB;QAErB,MAAM,OAAO,IAAI,KAAK;YAAC,KAAK,SAAS,CAAC,eAAe,eAAe,EAAE,MAAM;SAAG,EAAE;YAC/E,MAAM;QACR;QACA,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,cAAc,EAAE,KAAK,GAAG,GAAG,KAAK,CAAC;QAC/C,EAAE,KAAK;QACP,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,gBAAgB;QAErB,MAAM,OAAO,IAAI,KAAK;YAAC,eAAe,QAAQ;SAAC,EAAE;YAC/C,MAAM;QACR;QACA,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,iBAAiB,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;QACjD,EAAE,KAAK;QACP,IAAI,eAAe,CAAC;IACtB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;;;;;;;0BAGnD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAwB;;;;;;kCAIvC,6LAAC;wBACC,SAAS;wBACT,UAAU,eAAe,WAAW,IAAI,KAAK;wBAC7C,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,8FACA;4BACE,gDAAgD,CAAC,eAAe,WAAW,IAAI,GAAG;4BAClF,gDAAgD,eAAe,WAAW,IAAI,KAAK;wBACrF;kCAGD,4BACC;;8CACE,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAyB;;yDAI9C;;8CACE,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;oBAM1C,WAAW,IAAI,KAAK,mBACnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAY;;;;;;;oBAKtC,uBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BACtB;;;;;;;oBAIJ,gCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,6LAAC;gDAAI,WAAU;0DACZ,eAAe,QAAQ,CAAC,cAAc;;;;;;;;;;;;kDAG3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,6LAAC;gDAAI,WAAU;;oDACZ,CAAC,eAAe,QAAQ,CAAC,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;0CAK7D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAiC;;;;;;kDAEhD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS;gDACT,WAAU;0DAET,4BACC;;sEACE,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAAY;;iFAIrC;;sEACE,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;0DAMlC,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;kDAKpC,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;0CAKlC,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAM3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAsC;;;;;;0CACrD,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB;GAlOM;;QASA,4HAAA,CAAA,eAAY;;;KATZ;uCAoOS", "debugId": null}}, {"offset": {"line": 2797, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/src/components/StatsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useGameStore } from '@/store/gameStore';\nimport { ResourceType, ComponentType } from '@/types/game';\nimport { formatNumber, formatTime } from '@/utils/helpers';\nimport AIAssistant from './AIAssistant';\nimport {\n  BarChart3,\n  Clock,\n  Zap,\n  AlertTriangle,\n  ChevronDown,\n  ChevronRight,\n  Download,\n  Upload,\n  RotateCcw,\n  Cog,\n  Package,\n  Brain\n} from 'lucide-react';\nimport clsx from 'clsx';\n\ninterface StatsSectionProps {\n  title: string;\n  icon: React.ReactNode;\n  children: React.ReactNode;\n  isExpanded: boolean;\n  onToggle: () => void;\n}\n\nconst StatsSection: React.FC<StatsSectionProps> = ({ \n  title, \n  icon, \n  children, \n  isExpanded, \n  onToggle \n}) => {\n  return (\n    <div className=\"mb-4\">\n      <button\n        onClick={onToggle}\n        className=\"w-full flex items-center justify-between p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors\"\n      >\n        <div className=\"flex items-center gap-2\">\n          {icon}\n          <span className=\"font-semibold text-white\">{title}</span>\n        </div>\n        {isExpanded ? (\n          <ChevronDown className=\"w-4 h-4 text-gray-300\" />\n        ) : (\n          <ChevronRight className=\"w-4 h-4 text-gray-300\" />\n        )}\n      </button>\n      \n      {isExpanded && (\n        <div className=\"mt-2 p-3 bg-gray-800 rounded-lg\">\n          {children}\n        </div>\n      )}\n    </div>\n  );\n};\n\nconst ResourceDisplay: React.FC<{ type: ResourceType; amount: number }> = ({ type, amount }) => {\n  const getResourceColor = (type: ResourceType) => {\n    switch (type) {\n      case ResourceType.IRON_ORE:\n        return 'text-gray-400';\n      case ResourceType.COPPER_ORE:\n        return 'text-orange-400';\n      case ResourceType.COAL:\n        return 'text-gray-600';\n      case ResourceType.IRON_PLATE:\n        return 'text-gray-300';\n      case ResourceType.COPPER_PLATE:\n        return 'text-orange-300';\n      case ResourceType.GEAR:\n        return 'text-yellow-400';\n      case ResourceType.CIRCUIT:\n        return 'text-green-400';\n      default:\n        return 'text-white';\n    }\n  };\n\n  const getResourceName = (type: ResourceType) => {\n    return type.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n  };\n\n  return (\n    <div className=\"flex justify-between items-center py-1\">\n      <span className={clsx('text-sm', getResourceColor(type))}>\n        {getResourceName(type)}\n      </span>\n      <span className=\"text-white font-mono text-sm\">\n        {formatNumber(amount)}\n      </span>\n    </div>\n  );\n};\n\nconst StatsPanel: React.FC = () => {\n  const {\n    resources,\n    statistics,\n    components,\n    gameTime,\n    isRunning,\n    getPerformanceMetrics,\n    exportGameState,\n    importGameState,\n    resetGame,\n  } = useGameStore();\n\n  const [expandedSections, setExpandedSections] = useState<Set<string>>(\n    new Set(['Production', 'Resources', 'AI Assistant'])\n  );\n\n  const toggleSection = (section: string) => {\n    const newExpanded = new Set(expandedSections);\n    if (newExpanded.has(section)) {\n      newExpanded.delete(section);\n    } else {\n      newExpanded.add(section);\n    }\n    setExpandedSections(newExpanded);\n  };\n\n  const metrics = getPerformanceMetrics();\n\n  const handleExport = () => {\n    const data = exportGameState();\n    const blob = new Blob([data], { type: 'application/json' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `factory-${Date.now()}.json`;\n    a.click();\n    URL.revokeObjectURL(url);\n  };\n\n  const handleImport = () => {\n    const input = document.createElement('input');\n    input.type = 'file';\n    input.accept = '.json';\n    input.onchange = (e) => {\n      const file = (e.target as HTMLInputElement).files?.[0];\n      if (file) {\n        const reader = new FileReader();\n        reader.onload = (e) => {\n          const content = e.target?.result as string;\n          if (importGameState(content)) {\n            alert('Factory imported successfully!');\n          } else {\n            alert('Failed to import factory. Please check the file format.');\n          }\n        };\n        reader.readAsText(file);\n      }\n    };\n    input.click();\n  };\n\n  const componentCounts = Array.from(components.values()).reduce((acc, component) => {\n    acc[component.type] = (acc[component.type] || 0) + 1;\n    return acc;\n  }, {} as Record<ComponentType, number>);\n\n  return (\n    <div className=\"h-full p-4 overflow-y-auto\">\n      <div className=\"mb-6\">\n        <h2 className=\"text-xl font-bold text-white mb-2\">Factory Stats</h2>\n        <div className=\"flex items-center gap-2 text-sm text-gray-300\">\n          <Clock className=\"w-4 h-4\" />\n          <span>Runtime: {formatTime(gameTime)}</span>\n        </div>\n        <div className=\"flex items-center gap-2 text-sm text-gray-300 mt-1\">\n          <Zap className=\"w-4 h-4\" />\n          <span className={isRunning ? 'text-green-400' : 'text-red-400'}>\n            {isRunning ? 'Running' : 'Paused'}\n          </span>\n        </div>\n      </div>\n\n      {/* Production Stats */}\n      <StatsSection\n        title=\"Production\"\n        icon={<BarChart3 className=\"w-4 h-4 text-blue-400\" />}\n        isExpanded={expandedSections.has('Production')}\n        onToggle={() => toggleSection('Production')}\n      >\n        <div className=\"space-y-2\">\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-sm text-gray-300\">Efficiency</span>\n            <span className=\"text-white font-mono text-sm\">\n              {(statistics.efficiency * 100).toFixed(1)}%\n            </span>\n          </div>\n          \n          {Array.from(statistics.totalProduction.entries()).map(([resource, amount]) => (\n            <ResourceDisplay key={resource} type={resource} amount={amount} />\n          ))}\n          \n          {statistics.totalProduction.size === 0 && (\n            <p className=\"text-gray-400 text-sm\">No production data yet</p>\n          )}\n        </div>\n      </StatsSection>\n\n      {/* Resources */}\n      <StatsSection\n        title=\"Resources\"\n        icon={<Package className=\"w-4 h-4 text-green-400\" />}\n        isExpanded={expandedSections.has('Resources')}\n        onToggle={() => toggleSection('Resources')}\n      >\n        <div className=\"space-y-1\">\n          {Array.from(resources.entries()).map(([resource, amount]) => (\n            <ResourceDisplay key={resource} type={resource} amount={amount} />\n          ))}\n        </div>\n      </StatsSection>\n\n      {/* Components */}\n      <StatsSection\n        title=\"Components\"\n        icon={<Cog className=\"w-4 h-4 text-purple-400\" />}\n        isExpanded={expandedSections.has('Components')}\n        onToggle={() => toggleSection('Components')}\n      >\n        <div className=\"space-y-1\">\n          {Object.entries(componentCounts).map(([type, count]) => (\n            <div key={type} className=\"flex justify-between items-center py-1\">\n              <span className=\"text-sm text-gray-300 capitalize\">\n                {type.replace('_', ' ')}\n              </span>\n              <span className=\"text-white font-mono text-sm\">{count}</span>\n            </div>\n          ))}\n          {Object.keys(componentCounts).length === 0 && (\n            <p className=\"text-gray-400 text-sm\">No components placed</p>\n          )}\n        </div>\n      </StatsSection>\n\n      {/* Bottlenecks */}\n      {statistics.bottlenecks.length > 0 && (\n        <StatsSection\n          title=\"Bottlenecks\"\n          icon={<AlertTriangle className=\"w-4 h-4 text-red-400\" />}\n          isExpanded={expandedSections.has('Bottlenecks')}\n          onToggle={() => toggleSection('Bottlenecks')}\n        >\n          <div className=\"space-y-1\">\n            {statistics.bottlenecks.map((componentId, index) => (\n              <div key={componentId} className=\"text-sm text-red-300\">\n                Component {index + 1}: {componentId.slice(0, 8)}...\n              </div>\n            ))}\n          </div>\n        </StatsSection>\n      )}\n\n      {/* AI Assistant */}\n      <StatsSection\n        title=\"AI Assistant\"\n        icon={<Brain className=\"w-4 h-4 text-purple-400\" />}\n        isExpanded={expandedSections.has('AI Assistant')}\n        onToggle={() => toggleSection('AI Assistant')}\n      >\n        <AIAssistant />\n      </StatsSection>\n\n      {/* Actions */}\n      <div className=\"mt-8 space-y-2\">\n        <h3 className=\"font-semibold text-white mb-3\">Actions</h3>\n        \n        <button\n          onClick={handleExport}\n          className=\"w-full flex items-center gap-2 p-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors\"\n        >\n          <Download className=\"w-4 h-4\" />\n          Export Factory\n        </button>\n        \n        <button\n          onClick={handleImport}\n          className=\"w-full flex items-center gap-2 p-2 bg-green-600 hover:bg-green-700 text-white rounded transition-colors\"\n        >\n          <Upload className=\"w-4 h-4\" />\n          Import Factory\n        </button>\n        \n        <button\n          onClick={() => {\n            if (confirm('Are you sure you want to reset the factory? This cannot be undone.')) {\n              resetGame();\n            }\n          }}\n          className=\"w-full flex items-center gap-2 p-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors\"\n        >\n          <RotateCcw className=\"w-4 h-4\" />\n          Reset Factory\n        </button>\n      </div>\n\n      {/* Performance Metrics */}\n      <div className=\"mt-6 p-3 bg-gray-800 rounded-lg\">\n        <h3 className=\"font-semibold text-white mb-2\">Performance</h3>\n        <div className=\"text-xs text-gray-300 space-y-1\">\n          <div>Components: {components.size}</div>\n          <div>Active: {Array.from(components.values()).filter(c => c.isActive).length}</div>\n          <div>Efficiency: {(metrics.efficiency * 100).toFixed(1)}%</div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default StatsPanel;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;AArBA;;;;;;;;AA+BA,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,QAAQ,EACT;IACC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;4BACZ;0CACD,6LAAC;gCAAK,WAAU;0CAA4B;;;;;;;;;;;;oBAE7C,2BACC,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;6CAEvB,6LAAC,yNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;YAI3B,4BACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAKX;KA/BM;AAiCN,MAAM,kBAAoE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;IACzF,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK,uHAAA,CAAA,eAAY,CAAC,QAAQ;gBACxB,OAAO;YACT,KAAK,uHAAA,CAAA,eAAY,CAAC,UAAU;gBAC1B,OAAO;YACT,KAAK,uHAAA,CAAA,eAAY,CAAC,IAAI;gBACpB,OAAO;YACT,KAAK,uHAAA,CAAA,eAAY,CAAC,UAAU;gBAC1B,OAAO;YACT,KAAK,uHAAA,CAAA,eAAY,CAAC,YAAY;gBAC5B,OAAO;YACT,KAAK,uHAAA,CAAA,eAAY,CAAC,IAAI;gBACpB,OAAO;YACT,KAAK,uHAAA,CAAA,eAAY,CAAC,OAAO;gBACvB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,KAAK,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;IACnE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAK,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,WAAW,iBAAiB;0BAC/C,gBAAgB;;;;;;0BAEnB,6LAAC;gBAAK,WAAU;0BACb,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE;;;;;;;;;;;;AAItB;MApCM;AAsCN,MAAM,aAAuB;;IAC3B,MAAM,EACJ,SAAS,EACT,UAAU,EACV,UAAU,EACV,QAAQ,EACR,SAAS,EACT,qBAAqB,EACrB,eAAe,EACf,eAAe,EACf,SAAS,EACV,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEf,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACrD,IAAI,IAAI;QAAC;QAAc;QAAa;KAAe;IAGrD,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,UAAU;YAC5B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,oBAAoB;IACtB;IAEA,MAAM,UAAU;IAEhB,MAAM,eAAe;QACnB,MAAM,OAAO;QACb,MAAM,OAAO,IAAI,KAAK;YAAC;SAAK,EAAE;YAAE,MAAM;QAAmB;QACzD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,KAAK,CAAC;QACzC,EAAE,KAAK;QACP,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,eAAe;QACnB,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,IAAI,GAAG;QACb,MAAM,MAAM,GAAG;QACf,MAAM,QAAQ,GAAG,CAAC;YAChB,MAAM,OAAO,AAAC,EAAE,MAAM,CAAsB,KAAK,EAAE,CAAC,EAAE;YACtD,IAAI,MAAM;gBACR,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM,GAAG,CAAC;oBACf,MAAM,UAAU,EAAE,MAAM,EAAE;oBAC1B,IAAI,gBAAgB,UAAU;wBAC5B,MAAM;oBACR,OAAO;wBACL,MAAM;oBACR;gBACF;gBACA,OAAO,UAAU,CAAC;YACpB;QACF;QACA,MAAM,KAAK;IACb;IAEA,MAAM,kBAAkB,MAAM,IAAI,CAAC,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,KAAK;QACnE,GAAG,CAAC,UAAU,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI;QACnD,OAAO;IACT,GAAG,CAAC;IAEJ,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;;oCAAK;oCAAU,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE;;;;;;;;;;;;;kCAE7B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAW,YAAY,mBAAmB;0CAC7C,YAAY,YAAY;;;;;;;;;;;;;;;;;;0BAM/B,6LAAC;gBACC,OAAM;gBACN,oBAAM,6LAAC,qNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;gBAC3B,YAAY,iBAAiB,GAAG,CAAC;gBACjC,UAAU,IAAM,cAAc;0BAE9B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,6LAAC;oCAAK,WAAU;;wCACb,CAAC,WAAW,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC;wCAAG;;;;;;;;;;;;;wBAI7C,MAAM,IAAI,CAAC,WAAW,eAAe,CAAC,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,UAAU,OAAO,iBACvE,6LAAC;gCAA+B,MAAM;gCAAU,QAAQ;+BAAlC;;;;;wBAGvB,WAAW,eAAe,CAAC,IAAI,KAAK,mBACnC,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAM3C,6LAAC;gBACC,OAAM;gBACN,oBAAM,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBACzB,YAAY,iBAAiB,GAAG,CAAC;gBACjC,UAAU,IAAM,cAAc;0BAE9B,cAAA,6LAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC,UAAU,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,UAAU,OAAO,iBACtD,6LAAC;4BAA+B,MAAM;4BAAU,QAAQ;2BAAlC;;;;;;;;;;;;;;;0BAM5B,6LAAC;gBACC,OAAM;gBACN,oBAAM,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;gBACrB,YAAY,iBAAiB,GAAG,CAAC;gBACjC,UAAU,IAAM,cAAc;0BAE9B,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,OAAO,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,iBACjD,6LAAC;gCAAe,WAAU;;kDACxB,6LAAC;wCAAK,WAAU;kDACb,KAAK,OAAO,CAAC,KAAK;;;;;;kDAErB,6LAAC;wCAAK,WAAU;kDAAgC;;;;;;;+BAJxC;;;;;wBAOX,OAAO,IAAI,CAAC,iBAAiB,MAAM,KAAK,mBACvC,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;YAM1C,WAAW,WAAW,CAAC,MAAM,GAAG,mBAC/B,6LAAC;gBACC,OAAM;gBACN,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;gBAC/B,YAAY,iBAAiB,GAAG,CAAC;gBACjC,UAAU,IAAM,cAAc;0BAE9B,cAAA,6LAAC;oBAAI,WAAU;8BACZ,WAAW,WAAW,CAAC,GAAG,CAAC,CAAC,aAAa,sBACxC,6LAAC;4BAAsB,WAAU;;gCAAuB;gCAC3C,QAAQ;gCAAE;gCAAG,YAAY,KAAK,CAAC,GAAG;gCAAG;;2BADxC;;;;;;;;;;;;;;;0BASlB,6LAAC;gBACC,OAAM;gBACN,oBAAM,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBACvB,YAAY,iBAAiB,GAAG,CAAC;gBACjC,UAAU,IAAM,cAAc;0BAE9B,cAAA,6LAAC,oIAAA,CAAA,UAAW;;;;;;;;;;0BAId,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAgC;;;;;;kCAE9C,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAIlC,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAIhC,6LAAC;wBACC,SAAS;4BACP,IAAI,QAAQ,uEAAuE;gCACjF;4BACF;wBACF;wBACA,WAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAgC;;;;;;kCAC9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAI;oCAAa,WAAW,IAAI;;;;;;;0CACjC,6LAAC;;oCAAI;oCAAS,MAAM,IAAI,CAAC,WAAW,MAAM,IAAI,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;;;;;;;0CAC5E,6LAAC;;oCAAI;oCAAa,CAAC,QAAQ,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC;oCAAG;;;;;;;;;;;;;;;;;;;;;;;;;AAKlE;GAxNM;;QAWA,4HAAA,CAAA,eAAY;;;MAXZ;uCA0NS", "debugId": null}}, {"offset": {"line": 3443, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/src/components/FactoryGame.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { DndProvider } from 'react-dnd';\nimport { HTML5Backend } from 'react-dnd-html5-backend';\nimport GameBoard from './GameBoard';\nimport ComponentPalette from './ComponentPalette';\nimport StatsPanel from './StatsPanel';\nimport { useGameStore } from '@/store/gameStore';\n\nconst FactoryGame: React.FC = () => {\n  const { isRunning, toggleSimulation } = useGameStore();\n\n  return (\n    <DndProvider backend={HTML5Backend}>\n      <div className=\"h-screen flex flex-col bg-gray-900 text-white\">\n        {/* Header */}\n        <header className=\"bg-gray-800 p-4 border-b border-gray-700\">\n          <div className=\"flex justify-between items-center\">\n            <h1 className=\"text-2xl font-bold text-blue-400\">Factory Builder</h1>\n            <div className=\"flex gap-4 items-center\">\n              <button\n                onClick={toggleSimulation}\n                className={`px-4 py-2 rounded font-medium transition-colors ${\n                  isRunning\n                    ? 'bg-red-600 hover:bg-red-700 text-white'\n                    : 'bg-green-600 hover:bg-green-700 text-white'\n                }`}\n              >\n                {isRunning ? 'Pause' : 'Start'} Simulation\n              </button>\n            </div>\n          </div>\n        </header>\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex\">\n          {/* Component Palette */}\n          <div className=\"w-80 bg-gray-800 border-r border-gray-700\">\n            <ComponentPalette />\n          </div>\n\n          {/* Game Board */}\n          <div className=\"flex-1 relative\">\n            <GameBoard />\n          </div>\n\n          {/* Stats Panel */}\n          <div className=\"w-80 bg-gray-800 border-l border-gray-700\">\n            <StatsPanel />\n          </div>\n        </div>\n      </div>\n    </DndProvider>\n  );\n};\n\nexport default FactoryGame;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;AAUA,MAAM,cAAwB;;IAC5B,MAAM,EAAE,SAAS,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEnD,qBACE,6LAAC,8JAAA,CAAA,cAAW;QAAC,SAAS,oLAAA,CAAA,eAAY;kBAChC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,WAAW,CAAC,gDAAgD,EAC1D,YACI,2CACA,8CACJ;;wCAED,YAAY,UAAU;wCAAQ;;;;;;;;;;;;;;;;;;;;;;;8BAOvC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,yIAAA,CAAA,UAAgB;;;;;;;;;;sCAInB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,kIAAA,CAAA,UAAS;;;;;;;;;;sCAIZ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mIAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMvB;GA7CM;;QACoC,4HAAA,CAAA,eAAY;;;KADhD;uCA+CS", "debugId": null}}]}