{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/transformers/Transformer.js"], "sourcesContent": ["export default  class Transformer {\n  // Return true if anything was processed, false otherwise.\n  \n\n  getPrefixCode() {\n    return \"\";\n  }\n\n  getHoistedCode() {\n    return \"\";\n  }\n\n  getSuffixCode() {\n    return \"\";\n  }\n}\n"], "names": [], "mappings": ";;;AAAgB,MAAM;IACpB,0DAA0D;IAG1D,gBAAgB;QACd,OAAO;IACT;IAEA,iBAAiB;QACf,OAAO;IACT;IAEA,gBAAgB;QACd,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/transformers/JSXTransformer.js"], "sourcesContent": ["\n\n\nimport XHTMLEntities from \"../parser/plugins/jsx/xhtml\";\nimport {JSXRole} from \"../parser/tokenizer\";\nimport {TokenType as tt} from \"../parser/tokenizer/types\";\nimport {charCodes} from \"../parser/util/charcodes\";\n\nimport getJSXPragmaInfo, {} from \"../util/getJSXPragmaInfo\";\n\nimport Transformer from \"./Transformer\";\n\nexport default class JSXTransformer extends Transformer {\n  \n  \n  \n\n  // State for calculating the line number of each JSX tag in development.\n  __init() {this.lastLineNumber = 1}\n  __init2() {this.lastIndex = 0}\n\n  // In development, variable name holding the name of the current file.\n  __init3() {this.filenameVarName = null}\n  // Mapping of claimed names for imports in the automatic transform, e,g.\n  // {jsx: \"_jsx\"}. This determines which imports to generate in the prefix.\n  __init4() {this.esmAutomaticImportNameResolutions = {}}\n  // When automatically adding imports in CJS mode, we store the variable name\n  // holding the imported CJS module so we can require it in the prefix.\n  __init5() {this.cjsAutomaticModuleNameResolutions = {}}\n\n  constructor(\n     rootTransformer,\n     tokens,\n     importProcessor,\n     nameManager,\n     options,\n  ) {\n    super();this.rootTransformer = rootTransformer;this.tokens = tokens;this.importProcessor = importProcessor;this.nameManager = nameManager;this.options = options;JSXTransformer.prototype.__init.call(this);JSXTransformer.prototype.__init2.call(this);JSXTransformer.prototype.__init3.call(this);JSXTransformer.prototype.__init4.call(this);JSXTransformer.prototype.__init5.call(this);;\n    this.jsxPragmaInfo = getJSXPragmaInfo(options);\n    this.isAutomaticRuntime = options.jsxRuntime === \"automatic\";\n    this.jsxImportSource = options.jsxImportSource || \"react\";\n  }\n\n  process() {\n    if (this.tokens.matches1(tt.jsxTagStart)) {\n      this.processJSXTag();\n      return true;\n    }\n    return false;\n  }\n\n  getPrefixCode() {\n    let prefix = \"\";\n    if (this.filenameVarName) {\n      prefix += `const ${this.filenameVarName} = ${JSON.stringify(this.options.filePath || \"\")};`;\n    }\n    if (this.isAutomaticRuntime) {\n      if (this.importProcessor) {\n        // CJS mode: emit require statements for all modules that were referenced.\n        for (const [path, resolvedName] of Object.entries(this.cjsAutomaticModuleNameResolutions)) {\n          prefix += `var ${resolvedName} = require(\"${path}\");`;\n        }\n      } else {\n        // ESM mode: consolidate and emit import statements for referenced names.\n        const {createElement: createElementResolution, ...otherResolutions} =\n          this.esmAutomaticImportNameResolutions;\n        if (createElementResolution) {\n          prefix += `import {createElement as ${createElementResolution}} from \"${this.jsxImportSource}\";`;\n        }\n        const importSpecifiers = Object.entries(otherResolutions)\n          .map(([name, resolvedName]) => `${name} as ${resolvedName}`)\n          .join(\", \");\n        if (importSpecifiers) {\n          const importPath =\n            this.jsxImportSource + (this.options.production ? \"/jsx-runtime\" : \"/jsx-dev-runtime\");\n          prefix += `import {${importSpecifiers}} from \"${importPath}\";`;\n        }\n      }\n    }\n    return prefix;\n  }\n\n  processJSXTag() {\n    const {jsxRole, start} = this.tokens.currentToken();\n    // Calculate line number information at the very start (if in development\n    // mode) so that the information is guaranteed to be queried in token order.\n    const elementLocationCode = this.options.production ? null : this.getElementLocationCode(start);\n    if (this.isAutomaticRuntime && jsxRole !== JSXRole.KeyAfterPropSpread) {\n      this.transformTagToJSXFunc(elementLocationCode, jsxRole);\n    } else {\n      this.transformTagToCreateElement(elementLocationCode);\n    }\n  }\n\n  getElementLocationCode(firstTokenStart) {\n    const lineNumber = this.getLineNumberForIndex(firstTokenStart);\n    return `lineNumber: ${lineNumber}`;\n  }\n\n  /**\n   * Get the line number for this source position. This is calculated lazily and\n   * must be called in increasing order by index.\n   */\n  getLineNumberForIndex(index) {\n    const code = this.tokens.code;\n    while (this.lastIndex < index && this.lastIndex < code.length) {\n      if (code[this.lastIndex] === \"\\n\") {\n        this.lastLineNumber++;\n      }\n      this.lastIndex++;\n    }\n    return this.lastLineNumber;\n  }\n\n  /**\n   * Convert the current JSX element to a call to jsx, jsxs, or jsxDEV. This is\n   * the primary transformation for the automatic transform.\n   *\n   * Example:\n   * <div a={1} key={2}>Hello{x}</div>\n   * becomes\n   * jsxs('div', {a: 1, children: [\"Hello\", x]}, 2)\n   */\n  transformTagToJSXFunc(elementLocationCode, jsxRole) {\n    const isStatic = jsxRole === JSXRole.StaticChildren;\n    // First tag is always jsxTagStart.\n    this.tokens.replaceToken(this.getJSXFuncInvocationCode(isStatic));\n\n    let keyCode = null;\n    if (this.tokens.matches1(tt.jsxTagEnd)) {\n      // Fragment syntax.\n      this.tokens.replaceToken(`${this.getFragmentCode()}, {`);\n      this.processAutomaticChildrenAndEndProps(jsxRole);\n    } else {\n      // Normal open tag or self-closing tag.\n      this.processTagIntro();\n      this.tokens.appendCode(\", {\");\n      keyCode = this.processProps(true);\n\n      if (this.tokens.matches2(tt.slash, tt.jsxTagEnd)) {\n        // Self-closing tag, no children to add, so close the props.\n        this.tokens.appendCode(\"}\");\n      } else if (this.tokens.matches1(tt.jsxTagEnd)) {\n        // Tag with children.\n        this.tokens.removeToken();\n        this.processAutomaticChildrenAndEndProps(jsxRole);\n      } else {\n        throw new Error(\"Expected either /> or > at the end of the tag.\");\n      }\n      // If a key was present, move it to its own arg. Note that moving code\n      // like this will cause line numbers to get out of sync within the JSX\n      // element if the key expression has a newline in it. This is unfortunate,\n      // but hopefully should be rare.\n      if (keyCode) {\n        this.tokens.appendCode(`, ${keyCode}`);\n      }\n    }\n    if (!this.options.production) {\n      // If the key wasn't already added, add it now so we can correctly set\n      // positional args for jsxDEV.\n      if (keyCode === null) {\n        this.tokens.appendCode(\", void 0\");\n      }\n      this.tokens.appendCode(`, ${isStatic}, ${this.getDevSource(elementLocationCode)}, this`);\n    }\n    // We're at the close-tag or the end of a self-closing tag, so remove\n    // everything else and close the function call.\n    this.tokens.removeInitialToken();\n    while (!this.tokens.matches1(tt.jsxTagEnd)) {\n      this.tokens.removeToken();\n    }\n    this.tokens.replaceToken(\")\");\n  }\n\n  /**\n   * Convert the current JSX element to a createElement call. In the classic\n   * runtime, this is the only case. In the automatic runtime, this is called\n   * as a fallback in some situations.\n   *\n   * Example:\n   * <div a={1} key={2}>Hello{x}</div>\n   * becomes\n   * React.createElement('div', {a: 1, key: 2}, \"Hello\", x)\n   */\n  transformTagToCreateElement(elementLocationCode) {\n    // First tag is always jsxTagStart.\n    this.tokens.replaceToken(this.getCreateElementInvocationCode());\n\n    if (this.tokens.matches1(tt.jsxTagEnd)) {\n      // Fragment syntax.\n      this.tokens.replaceToken(`${this.getFragmentCode()}, null`);\n      this.processChildren(true);\n    } else {\n      // Normal open tag or self-closing tag.\n      this.processTagIntro();\n      this.processPropsObjectWithDevInfo(elementLocationCode);\n\n      if (this.tokens.matches2(tt.slash, tt.jsxTagEnd)) {\n        // Self-closing tag; no children to process.\n      } else if (this.tokens.matches1(tt.jsxTagEnd)) {\n        // Tag with children and a close-tag; process the children as args.\n        this.tokens.removeToken();\n        this.processChildren(true);\n      } else {\n        throw new Error(\"Expected either /> or > at the end of the tag.\");\n      }\n    }\n    // We're at the close-tag or the end of a self-closing tag, so remove\n    // everything else and close the function call.\n    this.tokens.removeInitialToken();\n    while (!this.tokens.matches1(tt.jsxTagEnd)) {\n      this.tokens.removeToken();\n    }\n    this.tokens.replaceToken(\")\");\n  }\n\n  /**\n   * Get the code for the relevant function for this context: jsx, jsxs,\n   * or jsxDEV. The following open-paren is included as well.\n   *\n   * These functions are only used for the automatic runtime, so they are always\n   * auto-imported, but the auto-import will be either CJS or ESM based on the\n   * target module format.\n   */\n  getJSXFuncInvocationCode(isStatic) {\n    if (this.options.production) {\n      if (isStatic) {\n        return this.claimAutoImportedFuncInvocation(\"jsxs\", \"/jsx-runtime\");\n      } else {\n        return this.claimAutoImportedFuncInvocation(\"jsx\", \"/jsx-runtime\");\n      }\n    } else {\n      return this.claimAutoImportedFuncInvocation(\"jsxDEV\", \"/jsx-dev-runtime\");\n    }\n  }\n\n  /**\n   * Return the code to use for the createElement function, e.g.\n   * `React.createElement`, including the following open-paren.\n   *\n   * This is the main function to use for the classic runtime. For the\n   * automatic runtime, this function is used as a fallback function to\n   * preserve behavior when there is a prop spread followed by an explicit\n   * key. In that automatic runtime case, the function should be automatically\n   * imported.\n   */\n  getCreateElementInvocationCode() {\n    if (this.isAutomaticRuntime) {\n      return this.claimAutoImportedFuncInvocation(\"createElement\", \"\");\n    } else {\n      const {jsxPragmaInfo} = this;\n      const resolvedPragmaBaseName = this.importProcessor\n        ? this.importProcessor.getIdentifierReplacement(jsxPragmaInfo.base) || jsxPragmaInfo.base\n        : jsxPragmaInfo.base;\n      return `${resolvedPragmaBaseName}${jsxPragmaInfo.suffix}(`;\n    }\n  }\n\n  /**\n   * Return the code to use as the component when compiling a shorthand\n   * fragment, e.g. `React.Fragment`.\n   *\n   * This may be called from either the classic or automatic runtime, and\n   * the value should be auto-imported for the automatic runtime.\n   */\n  getFragmentCode() {\n    if (this.isAutomaticRuntime) {\n      return this.claimAutoImportedName(\n        \"Fragment\",\n        this.options.production ? \"/jsx-runtime\" : \"/jsx-dev-runtime\",\n      );\n    } else {\n      const {jsxPragmaInfo} = this;\n      const resolvedFragmentPragmaBaseName = this.importProcessor\n        ? this.importProcessor.getIdentifierReplacement(jsxPragmaInfo.fragmentBase) ||\n          jsxPragmaInfo.fragmentBase\n        : jsxPragmaInfo.fragmentBase;\n      return resolvedFragmentPragmaBaseName + jsxPragmaInfo.fragmentSuffix;\n    }\n  }\n\n  /**\n   * Return code that invokes the given function.\n   *\n   * When the imports transform is enabled, use the CJSImportTransformer\n   * strategy of using `.call(void 0, ...` to avoid passing a `this` value in a\n   * situation that would otherwise look like a method call.\n   */\n  claimAutoImportedFuncInvocation(funcName, importPathSuffix) {\n    const funcCode = this.claimAutoImportedName(funcName, importPathSuffix);\n    if (this.importProcessor) {\n      return `${funcCode}.call(void 0, `;\n    } else {\n      return `${funcCode}(`;\n    }\n  }\n\n  claimAutoImportedName(funcName, importPathSuffix) {\n    if (this.importProcessor) {\n      // CJS mode: claim a name for the module and mark it for import.\n      const path = this.jsxImportSource + importPathSuffix;\n      if (!this.cjsAutomaticModuleNameResolutions[path]) {\n        this.cjsAutomaticModuleNameResolutions[path] =\n          this.importProcessor.getFreeIdentifierForPath(path);\n      }\n      return `${this.cjsAutomaticModuleNameResolutions[path]}.${funcName}`;\n    } else {\n      // ESM mode: claim a name for this function and add it to the names that\n      // should be auto-imported when the prefix is generated.\n      if (!this.esmAutomaticImportNameResolutions[funcName]) {\n        this.esmAutomaticImportNameResolutions[funcName] = this.nameManager.claimFreeName(\n          `_${funcName}`,\n        );\n      }\n      return this.esmAutomaticImportNameResolutions[funcName];\n    }\n  }\n\n  /**\n   * Process the first part of a tag, before any props.\n   */\n  processTagIntro() {\n    // Walk forward until we see one of these patterns:\n    // jsxName to start the first prop, preceded by another jsxName to end the tag name.\n    // jsxName to start the first prop, preceded by greaterThan to end the type argument.\n    // [open brace] to start the first prop.\n    // [jsxTagEnd] to end the open-tag.\n    // [slash, jsxTagEnd] to end the self-closing tag.\n    let introEnd = this.tokens.currentIndex() + 1;\n    while (\n      this.tokens.tokens[introEnd].isType ||\n      (!this.tokens.matches2AtIndex(introEnd - 1, tt.jsxName, tt.jsxName) &&\n        !this.tokens.matches2AtIndex(introEnd - 1, tt.greaterThan, tt.jsxName) &&\n        !this.tokens.matches1AtIndex(introEnd, tt.braceL) &&\n        !this.tokens.matches1AtIndex(introEnd, tt.jsxTagEnd) &&\n        !this.tokens.matches2AtIndex(introEnd, tt.slash, tt.jsxTagEnd))\n    ) {\n      introEnd++;\n    }\n    if (introEnd === this.tokens.currentIndex() + 1) {\n      const tagName = this.tokens.identifierName();\n      if (startsWithLowerCase(tagName)) {\n        this.tokens.replaceToken(`'${tagName}'`);\n      }\n    }\n    while (this.tokens.currentIndex() < introEnd) {\n      this.rootTransformer.processToken();\n    }\n  }\n\n  /**\n   * Starting at the beginning of the props, add the props argument to\n   * React.createElement, including the comma before it.\n   */\n  processPropsObjectWithDevInfo(elementLocationCode) {\n    const devProps = this.options.production\n      ? \"\"\n      : `__self: this, __source: ${this.getDevSource(elementLocationCode)}`;\n    if (!this.tokens.matches1(tt.jsxName) && !this.tokens.matches1(tt.braceL)) {\n      if (devProps) {\n        this.tokens.appendCode(`, {${devProps}}`);\n      } else {\n        this.tokens.appendCode(`, null`);\n      }\n      return;\n    }\n    this.tokens.appendCode(`, {`);\n    this.processProps(false);\n    if (devProps) {\n      this.tokens.appendCode(` ${devProps}}`);\n    } else {\n      this.tokens.appendCode(\"}\");\n    }\n  }\n\n  /**\n   * Transform the core part of the props, assuming that a { has already been\n   * inserted before us and that a } will be inserted after us.\n   *\n   * If extractKeyCode is true (i.e. when using any jsx... function), any prop\n   * named \"key\" has its code captured and returned rather than being emitted to\n   * the output code. This shifts line numbers, and emitting the code later will\n   * correct line numbers again. If no key is found or if extractKeyCode is\n   * false, this function returns null.\n   */\n  processProps(extractKeyCode) {\n    let keyCode = null;\n    while (true) {\n      if (this.tokens.matches2(tt.jsxName, tt.eq)) {\n        // This is a regular key={value} or key=\"value\" prop.\n        const propName = this.tokens.identifierName();\n        if (extractKeyCode && propName === \"key\") {\n          if (keyCode !== null) {\n            // The props list has multiple keys. Different implementations are\n            // inconsistent about what to do here: as of this writing, Babel and\n            // swc keep the *last* key and completely remove the rest, while\n            // TypeScript uses the *first* key and leaves the others as regular\n            // props. The React team collaborated with Babel on the\n            // implementation of this behavior, so presumably the Babel behavior\n            // is the one to use.\n            // Since we won't ever be emitting the previous key code, we need to\n            // at least emit its newlines here so that the line numbers match up\n            // in the long run.\n            this.tokens.appendCode(keyCode.replace(/[^\\n]/g, \"\"));\n          }\n          // key\n          this.tokens.removeToken();\n          // =\n          this.tokens.removeToken();\n          const snapshot = this.tokens.snapshot();\n          this.processPropValue();\n          keyCode = this.tokens.dangerouslyGetAndRemoveCodeSinceSnapshot(snapshot);\n          // Don't add a comma\n          continue;\n        } else {\n          this.processPropName(propName);\n          this.tokens.replaceToken(\": \");\n          this.processPropValue();\n        }\n      } else if (this.tokens.matches1(tt.jsxName)) {\n        // This is a shorthand prop like <input disabled />.\n        const propName = this.tokens.identifierName();\n        this.processPropName(propName);\n        this.tokens.appendCode(\": true\");\n      } else if (this.tokens.matches1(tt.braceL)) {\n        // This is prop spread, like <div {...getProps()}>, which we can pass\n        // through fairly directly as an object spread.\n        this.tokens.replaceToken(\"\");\n        this.rootTransformer.processBalancedCode();\n        this.tokens.replaceToken(\"\");\n      } else {\n        break;\n      }\n      this.tokens.appendCode(\",\");\n    }\n    return keyCode;\n  }\n\n  processPropName(propName) {\n    if (propName.includes(\"-\")) {\n      this.tokens.replaceToken(`'${propName}'`);\n    } else {\n      this.tokens.copyToken();\n    }\n  }\n\n  processPropValue() {\n    if (this.tokens.matches1(tt.braceL)) {\n      this.tokens.replaceToken(\"\");\n      this.rootTransformer.processBalancedCode();\n      this.tokens.replaceToken(\"\");\n    } else if (this.tokens.matches1(tt.jsxTagStart)) {\n      this.processJSXTag();\n    } else {\n      this.processStringPropValue();\n    }\n  }\n\n  processStringPropValue() {\n    const token = this.tokens.currentToken();\n    const valueCode = this.tokens.code.slice(token.start + 1, token.end - 1);\n    const replacementCode = formatJSXTextReplacement(valueCode);\n    const literalCode = formatJSXStringValueLiteral(valueCode);\n    this.tokens.replaceToken(literalCode + replacementCode);\n  }\n\n  /**\n   * Starting in the middle of the props object literal, produce an additional\n   * prop for the children and close the object literal.\n   */\n  processAutomaticChildrenAndEndProps(jsxRole) {\n    if (jsxRole === JSXRole.StaticChildren) {\n      this.tokens.appendCode(\" children: [\");\n      this.processChildren(false);\n      this.tokens.appendCode(\"]}\");\n    } else {\n      // The parser information tells us whether we will see a real child or if\n      // all remaining children (if any) will resolve to empty. If there are no\n      // non-empty children, don't emit a children prop at all, but still\n      // process children so that we properly transform the code into nothing.\n      if (jsxRole === JSXRole.OneChild) {\n        this.tokens.appendCode(\" children: \");\n      }\n      this.processChildren(false);\n      this.tokens.appendCode(\"}\");\n    }\n  }\n\n  /**\n   * Transform children into a comma-separated list, which will be either\n   * arguments to createElement or array elements of a children prop.\n   */\n  processChildren(needsInitialComma) {\n    let needsComma = needsInitialComma;\n    while (true) {\n      if (this.tokens.matches2(tt.jsxTagStart, tt.slash)) {\n        // Closing tag, so no more children.\n        return;\n      }\n      let didEmitElement = false;\n      if (this.tokens.matches1(tt.braceL)) {\n        if (this.tokens.matches2(tt.braceL, tt.braceR)) {\n          // Empty interpolations and comment-only interpolations are allowed\n          // and don't create an extra child arg.\n          this.tokens.replaceToken(\"\");\n          this.tokens.replaceToken(\"\");\n        } else {\n          // Interpolated expression.\n          this.tokens.replaceToken(needsComma ? \", \" : \"\");\n          this.rootTransformer.processBalancedCode();\n          this.tokens.replaceToken(\"\");\n          didEmitElement = true;\n        }\n      } else if (this.tokens.matches1(tt.jsxTagStart)) {\n        // Child JSX element\n        this.tokens.appendCode(needsComma ? \", \" : \"\");\n        this.processJSXTag();\n        didEmitElement = true;\n      } else if (this.tokens.matches1(tt.jsxText) || this.tokens.matches1(tt.jsxEmptyText)) {\n        didEmitElement = this.processChildTextElement(needsComma);\n      } else {\n        throw new Error(\"Unexpected token when processing JSX children.\");\n      }\n      if (didEmitElement) {\n        needsComma = true;\n      }\n    }\n  }\n\n  /**\n   * Turn a JSX text element into a string literal, or nothing at all if the JSX\n   * text resolves to the empty string.\n   *\n   * Returns true if a string literal is emitted, false otherwise.\n   */\n  processChildTextElement(needsComma) {\n    const token = this.tokens.currentToken();\n    const valueCode = this.tokens.code.slice(token.start, token.end);\n    const replacementCode = formatJSXTextReplacement(valueCode);\n    const literalCode = formatJSXTextLiteral(valueCode);\n    if (literalCode === '\"\"') {\n      this.tokens.replaceToken(replacementCode);\n      return false;\n    } else {\n      this.tokens.replaceToken(`${needsComma ? \", \" : \"\"}${literalCode}${replacementCode}`);\n      return true;\n    }\n  }\n\n  getDevSource(elementLocationCode) {\n    return `{fileName: ${this.getFilenameVarName()}, ${elementLocationCode}}`;\n  }\n\n  getFilenameVarName() {\n    if (!this.filenameVarName) {\n      this.filenameVarName = this.nameManager.claimFreeName(\"_jsxFileName\");\n    }\n    return this.filenameVarName;\n  }\n}\n\n/**\n * Spec for identifiers: https://tc39.github.io/ecma262/#prod-IdentifierStart.\n *\n * Really only treat anything starting with a-z as tag names.  `_`, `$`, `é`\n * should be treated as component names\n */\nexport function startsWithLowerCase(s) {\n  const firstChar = s.charCodeAt(0);\n  return firstChar >= charCodes.lowercaseA && firstChar <= charCodes.lowercaseZ;\n}\n\n/**\n * Turn the given jsxText string into a JS string literal. Leading and trailing\n * whitespace on lines is removed, except immediately after the open-tag and\n * before the close-tag. Empty lines are completely removed, and spaces are\n * added between lines after that.\n *\n * We use JSON.stringify to introduce escape characters as necessary, and trim\n * the start and end of each line and remove blank lines.\n */\nfunction formatJSXTextLiteral(text) {\n  let result = \"\";\n  let whitespace = \"\";\n\n  let isInInitialLineWhitespace = false;\n  let seenNonWhitespace = false;\n  for (let i = 0; i < text.length; i++) {\n    const c = text[i];\n    if (c === \" \" || c === \"\\t\" || c === \"\\r\") {\n      if (!isInInitialLineWhitespace) {\n        whitespace += c;\n      }\n    } else if (c === \"\\n\") {\n      whitespace = \"\";\n      isInInitialLineWhitespace = true;\n    } else {\n      if (seenNonWhitespace && isInInitialLineWhitespace) {\n        result += \" \";\n      }\n      result += whitespace;\n      whitespace = \"\";\n      if (c === \"&\") {\n        const {entity, newI} = processEntity(text, i + 1);\n        i = newI - 1;\n        result += entity;\n      } else {\n        result += c;\n      }\n      seenNonWhitespace = true;\n      isInInitialLineWhitespace = false;\n    }\n  }\n  if (!isInInitialLineWhitespace) {\n    result += whitespace;\n  }\n  return JSON.stringify(result);\n}\n\n/**\n * Produce the code that should be printed after the JSX text string literal,\n * with most content removed, but all newlines preserved and all spacing at the\n * end preserved.\n */\nfunction formatJSXTextReplacement(text) {\n  let numNewlines = 0;\n  let numSpaces = 0;\n  for (const c of text) {\n    if (c === \"\\n\") {\n      numNewlines++;\n      numSpaces = 0;\n    } else if (c === \" \") {\n      numSpaces++;\n    }\n  }\n  return \"\\n\".repeat(numNewlines) + \" \".repeat(numSpaces);\n}\n\n/**\n * Format a string in the value position of a JSX prop.\n *\n * Use the same implementation as convertAttribute from\n * babel-helper-builder-react-jsx.\n */\nfunction formatJSXStringValueLiteral(text) {\n  let result = \"\";\n  for (let i = 0; i < text.length; i++) {\n    const c = text[i];\n    if (c === \"\\n\") {\n      if (/\\s/.test(text[i + 1])) {\n        result += \" \";\n        while (i < text.length && /\\s/.test(text[i + 1])) {\n          i++;\n        }\n      } else {\n        result += \"\\n\";\n      }\n    } else if (c === \"&\") {\n      const {entity, newI} = processEntity(text, i + 1);\n      result += entity;\n      i = newI - 1;\n    } else {\n      result += c;\n    }\n  }\n  return JSON.stringify(result);\n}\n\n/**\n * Starting at a &, see if there's an HTML entity (specified by name, decimal\n * char code, or hex char code) and return it if so.\n *\n * Modified from jsxReadString in babel-parser.\n */\nfunction processEntity(text, indexAfterAmpersand) {\n  let str = \"\";\n  let count = 0;\n  let entity;\n  let i = indexAfterAmpersand;\n\n  if (text[i] === \"#\") {\n    let radix = 10;\n    i++;\n    let numStart;\n    if (text[i] === \"x\") {\n      radix = 16;\n      i++;\n      numStart = i;\n      while (i < text.length && isHexDigit(text.charCodeAt(i))) {\n        i++;\n      }\n    } else {\n      numStart = i;\n      while (i < text.length && isDecimalDigit(text.charCodeAt(i))) {\n        i++;\n      }\n    }\n    if (text[i] === \";\") {\n      const numStr = text.slice(numStart, i);\n      if (numStr) {\n        i++;\n        entity = String.fromCodePoint(parseInt(numStr, radix));\n      }\n    }\n  } else {\n    while (i < text.length && count++ < 10) {\n      const ch = text[i];\n      i++;\n      if (ch === \";\") {\n        entity = XHTMLEntities.get(str);\n        break;\n      }\n      str += ch;\n    }\n  }\n\n  if (!entity) {\n    return {entity: \"&\", newI: indexAfterAmpersand};\n  }\n  return {entity, newI: i};\n}\n\nfunction isDecimalDigit(code) {\n  return code >= charCodes.digit0 && code <= charCodes.digit9;\n}\n\nfunction isHexDigit(code) {\n  return (\n    (code >= charCodes.digit0 && code <= charCodes.digit9) ||\n    (code >= charCodes.lowercaseA && code <= charCodes.lowercaseF) ||\n    (code >= charCodes.uppercaseA && code <= charCodes.uppercaseF)\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAEA;AAEA;;;;;;;AAEe,MAAM,uBAAuB,wKAAA,CAAA,UAAW;IAKrD,wEAAwE;IACxE,SAAS;QAAC,IAAI,CAAC,cAAc,GAAG;IAAC;IACjC,UAAU;QAAC,IAAI,CAAC,SAAS,GAAG;IAAC;IAE7B,sEAAsE;IACtE,UAAU;QAAC,IAAI,CAAC,eAAe,GAAG;IAAI;IACtC,wEAAwE;IACxE,0EAA0E;IAC1E,UAAU;QAAC,IAAI,CAAC,iCAAiC,GAAG,CAAC;IAAC;IACtD,4EAA4E;IAC5E,sEAAsE;IACtE,UAAU;QAAC,IAAI,CAAC,iCAAiC,GAAG,CAAC;IAAC;IAEtD,YACG,eAAe,EACf,MAAM,EACN,eAAe,EACf,WAAW,EACX,OAAO,CACR;QACA,KAAK;QAAG,IAAI,CAAC,eAAe,GAAG;QAAgB,IAAI,CAAC,MAAM,GAAG;QAAO,IAAI,CAAC,eAAe,GAAG;QAAgB,IAAI,CAAC,WAAW,GAAG;QAAY,IAAI,CAAC,OAAO,GAAG;QAAQ,eAAe,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;QAAE,eAAe,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;QAAE,eAAe,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;QAAE,eAAe,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;QAAE,eAAe,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;;QAC1X,IAAI,CAAC,aAAa,GAAG,CAAA,GAAA,qKAAA,CAAA,UAAgB,AAAD,EAAE;QACtC,IAAI,CAAC,kBAAkB,GAAG,QAAQ,UAAU,KAAK;QACjD,IAAI,CAAC,eAAe,GAAG,QAAQ,eAAe,IAAI;IACpD;IAEA,UAAU;QACR,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,WAAW,GAAG;YACxC,IAAI,CAAC,aAAa;YAClB,OAAO;QACT;QACA,OAAO;IACT;IAEA,gBAAgB;QACd,IAAI,SAAS;QACb,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC;QAC7F;QACA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,IAAI,IAAI,CAAC,eAAe,EAAE;gBACxB,0EAA0E;gBAC1E,KAAK,MAAM,CAAC,MAAM,aAAa,IAAI,OAAO,OAAO,CAAC,IAAI,CAAC,iCAAiC,EAAG;oBACzF,UAAU,CAAC,IAAI,EAAE,aAAa,YAAY,EAAE,KAAK,GAAG,CAAC;gBACvD;YACF,OAAO;gBACL,yEAAyE;gBACzE,MAAM,EAAC,eAAe,uBAAuB,EAAE,GAAG,kBAAiB,GACjE,IAAI,CAAC,iCAAiC;gBACxC,IAAI,yBAAyB;oBAC3B,UAAU,CAAC,yBAAyB,EAAE,wBAAwB,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;gBAClG;gBACA,MAAM,mBAAmB,OAAO,OAAO,CAAC,kBACrC,GAAG,CAAC,CAAC,CAAC,MAAM,aAAa,GAAK,GAAG,KAAK,IAAI,EAAE,cAAc,EAC1D,IAAI,CAAC;gBACR,IAAI,kBAAkB;oBACpB,MAAM,aACJ,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,iBAAiB,kBAAkB;oBACvF,UAAU,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,EAAE,WAAW,EAAE,CAAC;gBAChE;YACF;QACF;QACA,OAAO;IACT;IAEA,gBAAgB;QACd,MAAM,EAAC,OAAO,EAAE,KAAK,EAAC,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY;QACjD,yEAAyE;QACzE,4EAA4E;QAC5E,MAAM,sBAAsB,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,OAAO,IAAI,CAAC,sBAAsB,CAAC;QACzF,IAAI,IAAI,CAAC,kBAAkB,IAAI,YAAY,yKAAA,CAAA,UAAO,CAAC,kBAAkB,EAAE;YACrE,IAAI,CAAC,qBAAqB,CAAC,qBAAqB;QAClD,OAAO;YACL,IAAI,CAAC,2BAA2B,CAAC;QACnC;IACF;IAEA,uBAAuB,eAAe,EAAE;QACtC,MAAM,aAAa,IAAI,CAAC,qBAAqB,CAAC;QAC9C,OAAO,CAAC,YAAY,EAAE,YAAY;IACpC;IAEA;;;GAGC,GACD,sBAAsB,KAAK,EAAE;QAC3B,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;QAC7B,MAAO,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,CAAC,SAAS,GAAG,KAAK,MAAM,CAAE;YAC7D,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;gBACjC,IAAI,CAAC,cAAc;YACrB;YACA,IAAI,CAAC,SAAS;QAChB;QACA,OAAO,IAAI,CAAC,cAAc;IAC5B;IAEA;;;;;;;;GAQC,GACD,sBAAsB,mBAAmB,EAAE,OAAO,EAAE;QAClD,MAAM,WAAW,YAAY,yKAAA,CAAA,UAAO,CAAC,cAAc;QACnD,mCAAmC;QACnC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,wBAAwB,CAAC;QAEvD,IAAI,UAAU;QACd,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,SAAS,GAAG;YACtC,mBAAmB;YACnB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC;YACvD,IAAI,CAAC,mCAAmC,CAAC;QAC3C,OAAO;YACL,uCAAuC;YACvC,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACvB,UAAU,IAAI,CAAC,YAAY,CAAC;YAE5B,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,EAAE,yKAAA,CAAA,YAAE,CAAC,SAAS,GAAG;gBAChD,4DAA4D;gBAC5D,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACzB,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,SAAS,GAAG;gBAC7C,qBAAqB;gBACrB,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB,IAAI,CAAC,mCAAmC,CAAC;YAC3C,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YACA,sEAAsE;YACtE,sEAAsE;YACtE,0EAA0E;YAC1E,gCAAgC;YAChC,IAAI,SAAS;gBACX,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,SAAS;YACvC;QACF;QACA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;YAC5B,sEAAsE;YACtE,8BAA8B;YAC9B,IAAI,YAAY,MAAM;gBACpB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACzB;YACA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,SAAS,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,qBAAqB,MAAM,CAAC;QACzF;QACA,qEAAqE;QACrE,+CAA+C;QAC/C,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC9B,MAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,SAAS,EAAG;YAC1C,IAAI,CAAC,MAAM,CAAC,WAAW;QACzB;QACA,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;IAC3B;IAEA;;;;;;;;;GASC,GACD,4BAA4B,mBAAmB,EAAE;QAC/C,mCAAmC;QACnC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,8BAA8B;QAE5D,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,SAAS,GAAG;YACtC,mBAAmB;YACnB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;YAC1D,IAAI,CAAC,eAAe,CAAC;QACvB,OAAO;YACL,uCAAuC;YACvC,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,6BAA6B,CAAC;YAEnC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,EAAE,yKAAA,CAAA,YAAE,CAAC,SAAS,GAAG;YAChD,4CAA4C;YAC9C,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,SAAS,GAAG;gBAC7C,mEAAmE;gBACnE,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB,IAAI,CAAC,eAAe,CAAC;YACvB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF;QACA,qEAAqE;QACrE,+CAA+C;QAC/C,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC9B,MAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,SAAS,EAAG;YAC1C,IAAI,CAAC,MAAM,CAAC,WAAW;QACzB;QACA,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;IAC3B;IAEA;;;;;;;GAOC,GACD,yBAAyB,QAAQ,EAAE;QACjC,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;YAC3B,IAAI,UAAU;gBACZ,OAAO,IAAI,CAAC,+BAA+B,CAAC,QAAQ;YACtD,OAAO;gBACL,OAAO,IAAI,CAAC,+BAA+B,CAAC,OAAO;YACrD;QACF,OAAO;YACL,OAAO,IAAI,CAAC,+BAA+B,CAAC,UAAU;QACxD;IACF;IAEA;;;;;;;;;GASC,GACD,iCAAiC;QAC/B,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,OAAO,IAAI,CAAC,+BAA+B,CAAC,iBAAiB;QAC/D,OAAO;YACL,MAAM,EAAC,aAAa,EAAC,GAAG,IAAI;YAC5B,MAAM,yBAAyB,IAAI,CAAC,eAAe,GAC/C,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC,cAAc,IAAI,KAAK,cAAc,IAAI,GACvF,cAAc,IAAI;YACtB,OAAO,GAAG,yBAAyB,cAAc,MAAM,CAAC,CAAC,CAAC;QAC5D;IACF;IAEA;;;;;;GAMC,GACD,kBAAkB;QAChB,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,OAAO,IAAI,CAAC,qBAAqB,CAC/B,YACA,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,iBAAiB;QAE/C,OAAO;YACL,MAAM,EAAC,aAAa,EAAC,GAAG,IAAI;YAC5B,MAAM,iCAAiC,IAAI,CAAC,eAAe,GACvD,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC,cAAc,YAAY,KACxE,cAAc,YAAY,GAC1B,cAAc,YAAY;YAC9B,OAAO,iCAAiC,cAAc,cAAc;QACtE;IACF;IAEA;;;;;;GAMC,GACD,gCAAgC,QAAQ,EAAE,gBAAgB,EAAE;QAC1D,MAAM,WAAW,IAAI,CAAC,qBAAqB,CAAC,UAAU;QACtD,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,OAAO,GAAG,SAAS,cAAc,CAAC;QACpC,OAAO;YACL,OAAO,GAAG,SAAS,CAAC,CAAC;QACvB;IACF;IAEA,sBAAsB,QAAQ,EAAE,gBAAgB,EAAE;QAChD,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,gEAAgE;YAChE,MAAM,OAAO,IAAI,CAAC,eAAe,GAAG;YACpC,IAAI,CAAC,IAAI,CAAC,iCAAiC,CAAC,KAAK,EAAE;gBACjD,IAAI,CAAC,iCAAiC,CAAC,KAAK,GAC1C,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC;YAClD;YACA,OAAO,GAAG,IAAI,CAAC,iCAAiC,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU;QACtE,OAAO;YACL,wEAAwE;YACxE,wDAAwD;YACxD,IAAI,CAAC,IAAI,CAAC,iCAAiC,CAAC,SAAS,EAAE;gBACrD,IAAI,CAAC,iCAAiC,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAC/E,CAAC,CAAC,EAAE,UAAU;YAElB;YACA,OAAO,IAAI,CAAC,iCAAiC,CAAC,SAAS;QACzD;IACF;IAEA;;GAEC,GACD,kBAAkB;QAChB,mDAAmD;QACnD,oFAAoF;QACpF,qFAAqF;QACrF,wCAAwC;QACxC,mCAAmC;QACnC,kDAAkD;QAClD,IAAI,WAAW,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK;QAC5C,MACE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,IAClC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,WAAW,GAAG,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,OAAO,KAChE,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,WAAW,GAAG,yKAAA,CAAA,YAAE,CAAC,WAAW,EAAE,yKAAA,CAAA,YAAE,CAAC,OAAO,KACrE,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,yKAAA,CAAA,YAAE,CAAC,MAAM,KAChD,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,yKAAA,CAAA,YAAE,CAAC,SAAS,KACnD,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,yKAAA,CAAA,YAAE,CAAC,KAAK,EAAE,yKAAA,CAAA,YAAE,CAAC,SAAS,EAC/D;YACA;QACF;QACA,IAAI,aAAa,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,GAAG;YAC/C,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC,cAAc;YAC1C,IAAI,oBAAoB,UAAU;gBAChC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;YACzC;QACF;QACA,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,SAAU;YAC5C,IAAI,CAAC,eAAe,CAAC,YAAY;QACnC;IACF;IAEA;;;GAGC,GACD,8BAA8B,mBAAmB,EAAE;QACjD,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,UAAU,GACpC,KACA,CAAC,wBAAwB,EAAE,IAAI,CAAC,YAAY,CAAC,sBAAsB;QACvE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YACzE,IAAI,UAAU;gBACZ,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC1C,OAAO;gBACL,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC;YACjC;YACA;QACF;QACA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC;QAC5B,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,UAAU;YACZ,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QACxC,OAAO;YACL,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACzB;IACF;IAEA;;;;;;;;;GASC,GACD,aAAa,cAAc,EAAE;QAC3B,IAAI,UAAU;QACd,MAAO,KAAM;YACX,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,EAAE,GAAG;gBAC3C,qDAAqD;gBACrD,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,cAAc;gBAC3C,IAAI,kBAAkB,aAAa,OAAO;oBACxC,IAAI,YAAY,MAAM;wBACpB,kEAAkE;wBAClE,oEAAoE;wBACpE,gEAAgE;wBAChE,mEAAmE;wBACnE,uDAAuD;wBACvD,oEAAoE;wBACpE,qBAAqB;wBACrB,oEAAoE;wBACpE,oEAAoE;wBACpE,mBAAmB;wBACnB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,OAAO,CAAC,UAAU;oBACnD;oBACA,MAAM;oBACN,IAAI,CAAC,MAAM,CAAC,WAAW;oBACvB,IAAI;oBACJ,IAAI,CAAC,MAAM,CAAC,WAAW;oBACvB,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ;oBACrC,IAAI,CAAC,gBAAgB;oBACrB,UAAU,IAAI,CAAC,MAAM,CAAC,wCAAwC,CAAC;oBAE/D;gBACF,OAAO;oBACL,IAAI,CAAC,eAAe,CAAC;oBACrB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;oBACzB,IAAI,CAAC,gBAAgB;gBACvB;YACF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,GAAG;gBAC3C,oDAAoD;gBACpD,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,cAAc;gBAC3C,IAAI,CAAC,eAAe,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACzB,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBAC1C,qEAAqE;gBACrE,+CAA+C;gBAC/C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;gBACzB,IAAI,CAAC,eAAe,CAAC,mBAAmB;gBACxC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YAC3B,OAAO;gBACL;YACF;YACA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACzB;QACA,OAAO;IACT;IAEA,gBAAgB,QAAQ,EAAE;QACxB,IAAI,SAAS,QAAQ,CAAC,MAAM;YAC1B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QAC1C,OAAO;YACL,IAAI,CAAC,MAAM,CAAC,SAAS;QACvB;IACF;IAEA,mBAAmB;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YACnC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YACzB,IAAI,CAAC,eAAe,CAAC,mBAAmB;YACxC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAC3B,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,WAAW,GAAG;YAC/C,IAAI,CAAC,aAAa;QACpB,OAAO;YACL,IAAI,CAAC,sBAAsB;QAC7B;IACF;IAEA,yBAAyB;QACvB,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,YAAY;QACtC,MAAM,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG,GAAG,MAAM,GAAG,GAAG;QACtE,MAAM,kBAAkB,yBAAyB;QACjD,MAAM,cAAc,4BAA4B;QAChD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,cAAc;IACzC;IAEA;;;GAGC,GACD,oCAAoC,OAAO,EAAE;QAC3C,IAAI,YAAY,yKAAA,CAAA,UAAO,CAAC,cAAc,EAAE;YACtC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACvB,IAAI,CAAC,eAAe,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACzB,OAAO;YACL,yEAAyE;YACzE,yEAAyE;YACzE,mEAAmE;YACnE,wEAAwE;YACxE,IAAI,YAAY,yKAAA,CAAA,UAAO,CAAC,QAAQ,EAAE;gBAChC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACzB;YACA,IAAI,CAAC,eAAe,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACzB;IACF;IAEA;;;GAGC,GACD,gBAAgB,iBAAiB,EAAE;QACjC,IAAI,aAAa;QACjB,MAAO,KAAM;YACX,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,WAAW,EAAE,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;gBAClD,oCAAoC;gBACpC;YACF;YACA,IAAI,iBAAiB;YACrB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBACnC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;oBAC9C,mEAAmE;oBACnE,uCAAuC;oBACvC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;oBACzB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC3B,OAAO;oBACL,2BAA2B;oBAC3B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,OAAO;oBAC7C,IAAI,CAAC,eAAe,CAAC,mBAAmB;oBACxC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;oBACzB,iBAAiB;gBACnB;YACF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,WAAW,GAAG;gBAC/C,oBAAoB;gBACpB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,aAAa,OAAO;gBAC3C,IAAI,CAAC,aAAa;gBAClB,iBAAiB;YACnB,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,YAAY,GAAG;gBACpF,iBAAiB,IAAI,CAAC,uBAAuB,CAAC;YAChD,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,gBAAgB;gBAClB,aAAa;YACf;QACF;IACF;IAEA;;;;;GAKC,GACD,wBAAwB,UAAU,EAAE;QAClC,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,YAAY;QACtC,MAAM,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,EAAE,MAAM,GAAG;QAC/D,MAAM,kBAAkB,yBAAyB;QACjD,MAAM,cAAc,qBAAqB;QACzC,IAAI,gBAAgB,MAAM;YACxB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YACzB,OAAO;QACT,OAAO;YACL,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,aAAa,OAAO,KAAK,cAAc,iBAAiB;YACpF,OAAO;QACT;IACF;IAEA,aAAa,mBAAmB,EAAE;QAChC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,kBAAkB,GAAG,EAAE,EAAE,oBAAoB,CAAC,CAAC;IAC3E;IAEA,qBAAqB;QACnB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;QACxD;QACA,OAAO,IAAI,CAAC,eAAe;IAC7B;AACF;AAQO,SAAS,oBAAoB,CAAC;IACnC,MAAM,YAAY,EAAE,UAAU,CAAC;IAC/B,OAAO,aAAa,wKAAA,CAAA,YAAS,CAAC,UAAU,IAAI,aAAa,wKAAA,CAAA,YAAS,CAAC,UAAU;AAC/E;AAEA;;;;;;;;CAQC,GACD,SAAS,qBAAqB,IAAI;IAChC,IAAI,SAAS;IACb,IAAI,aAAa;IAEjB,IAAI,4BAA4B;IAChC,IAAI,oBAAoB;IACxB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,MAAM,IAAI,IAAI,CAAC,EAAE;QACjB,IAAI,MAAM,OAAO,MAAM,QAAQ,MAAM,MAAM;YACzC,IAAI,CAAC,2BAA2B;gBAC9B,cAAc;YAChB;QACF,OAAO,IAAI,MAAM,MAAM;YACrB,aAAa;YACb,4BAA4B;QAC9B,OAAO;YACL,IAAI,qBAAqB,2BAA2B;gBAClD,UAAU;YACZ;YACA,UAAU;YACV,aAAa;YACb,IAAI,MAAM,KAAK;gBACb,MAAM,EAAC,MAAM,EAAE,IAAI,EAAC,GAAG,cAAc,MAAM,IAAI;gBAC/C,IAAI,OAAO;gBACX,UAAU;YACZ,OAAO;gBACL,UAAU;YACZ;YACA,oBAAoB;YACpB,4BAA4B;QAC9B;IACF;IACA,IAAI,CAAC,2BAA2B;QAC9B,UAAU;IACZ;IACA,OAAO,KAAK,SAAS,CAAC;AACxB;AAEA;;;;CAIC,GACD,SAAS,yBAAyB,IAAI;IACpC,IAAI,cAAc;IAClB,IAAI,YAAY;IAChB,KAAK,MAAM,KAAK,KAAM;QACpB,IAAI,MAAM,MAAM;YACd;YACA,YAAY;QACd,OAAO,IAAI,MAAM,KAAK;YACpB;QACF;IACF;IACA,OAAO,KAAK,MAAM,CAAC,eAAe,IAAI,MAAM,CAAC;AAC/C;AAEA;;;;;CAKC,GACD,SAAS,4BAA4B,IAAI;IACvC,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,MAAM,IAAI,IAAI,CAAC,EAAE;QACjB,IAAI,MAAM,MAAM;YACd,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG;gBAC1B,UAAU;gBACV,MAAO,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAG;oBAChD;gBACF;YACF,OAAO;gBACL,UAAU;YACZ;QACF,OAAO,IAAI,MAAM,KAAK;YACpB,MAAM,EAAC,MAAM,EAAE,IAAI,EAAC,GAAG,cAAc,MAAM,IAAI;YAC/C,UAAU;YACV,IAAI,OAAO;QACb,OAAO;YACL,UAAU;QACZ;IACF;IACA,OAAO,KAAK,SAAS,CAAC;AACxB;AAEA;;;;;CAKC,GACD,SAAS,cAAc,IAAI,EAAE,mBAAmB;IAC9C,IAAI,MAAM;IACV,IAAI,QAAQ;IACZ,IAAI;IACJ,IAAI,IAAI;IAER,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK;QACnB,IAAI,QAAQ;QACZ;QACA,IAAI;QACJ,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK;YACnB,QAAQ;YACR;YACA,WAAW;YACX,MAAO,IAAI,KAAK,MAAM,IAAI,WAAW,KAAK,UAAU,CAAC,IAAK;gBACxD;YACF;QACF,OAAO;YACL,WAAW;YACX,MAAO,IAAI,KAAK,MAAM,IAAI,eAAe,KAAK,UAAU,CAAC,IAAK;gBAC5D;YACF;QACF;QACA,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK;YACnB,MAAM,SAAS,KAAK,KAAK,CAAC,UAAU;YACpC,IAAI,QAAQ;gBACV;gBACA,SAAS,OAAO,aAAa,CAAC,SAAS,QAAQ;YACjD;QACF;IACF,OAAO;QACL,MAAO,IAAI,KAAK,MAAM,IAAI,UAAU,GAAI;YACtC,MAAM,KAAK,IAAI,CAAC,EAAE;YAClB;YACA,IAAI,OAAO,KAAK;gBACd,SAAS,8KAAA,CAAA,UAAa,CAAC,GAAG,CAAC;gBAC3B;YACF;YACA,OAAO;QACT;IACF;IAEA,IAAI,CAAC,QAAQ;QACX,OAAO;YAAC,QAAQ;YAAK,MAAM;QAAmB;IAChD;IACA,OAAO;QAAC;QAAQ,MAAM;IAAC;AACzB;AAEA,SAAS,eAAe,IAAI;IAC1B,OAAO,QAAQ,wKAAA,CAAA,YAAS,CAAC,MAAM,IAAI,QAAQ,wKAAA,CAAA,YAAS,CAAC,MAAM;AAC7D;AAEA,SAAS,WAAW,IAAI;IACtB,OACE,AAAC,QAAQ,wKAAA,CAAA,YAAS,CAAC,MAAM,IAAI,QAAQ,wKAAA,CAAA,YAAS,CAAC,MAAM,IACpD,QAAQ,wKAAA,CAAA,YAAS,CAAC,UAAU,IAAI,QAAQ,wKAAA,CAAA,YAAS,CAAC,UAAU,IAC5D,QAAQ,wKAAA,CAAA,YAAS,CAAC,UAAU,IAAI,QAAQ,wKAAA,CAAA,YAAS,CAAC,UAAU;AAEjE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/transformers/CJSImportTransformer.js"], "sourcesContent": ["\n\n\nimport {IdentifierRole, isDeclaration, isObjectShorthandDeclaration} from \"../parser/tokenizer\";\nimport {ContextualKeyword} from \"../parser/tokenizer/keywords\";\nimport {TokenType as tt} from \"../parser/tokenizer/types\";\n\nimport elideImportEquals from \"../util/elideImportEquals\";\nimport getDeclarationInfo, {\n\n  EMPTY_DECLARATION_INFO,\n} from \"../util/getDeclarationInfo\";\nimport getImportExportSpecifierInfo from \"../util/getImportExportSpecifierInfo\";\nimport isExportFrom from \"../util/isExportFrom\";\nimport {removeMaybeImportAttributes} from \"../util/removeMaybeImportAttributes\";\nimport shouldElideDefaultExport from \"../util/shouldElideDefaultExport\";\n\n\nimport Transformer from \"./Transformer\";\n\n/**\n * Class for editing import statements when we are transforming to commonjs.\n */\nexport default class CJSImportTransformer extends Transformer {\n   __init() {this.hadExport = false}\n   __init2() {this.hadNamedExport = false}\n   __init3() {this.hadDefaultExport = false}\n  \n\n  constructor(\n     rootTransformer,\n     tokens,\n     importProcessor,\n     nameManager,\n     helperManager,\n     reactHotLoaderTransformer,\n     enableLegacyBabel5ModuleInterop,\n     enableLegacyTypeScriptModuleInterop,\n     isTypeScriptTransformEnabled,\n     isFlowTransformEnabled,\n     preserveDynamicImport,\n     keepUnusedImports,\n  ) {\n    super();this.rootTransformer = rootTransformer;this.tokens = tokens;this.importProcessor = importProcessor;this.nameManager = nameManager;this.helperManager = helperManager;this.reactHotLoaderTransformer = reactHotLoaderTransformer;this.enableLegacyBabel5ModuleInterop = enableLegacyBabel5ModuleInterop;this.enableLegacyTypeScriptModuleInterop = enableLegacyTypeScriptModuleInterop;this.isTypeScriptTransformEnabled = isTypeScriptTransformEnabled;this.isFlowTransformEnabled = isFlowTransformEnabled;this.preserveDynamicImport = preserveDynamicImport;this.keepUnusedImports = keepUnusedImports;CJSImportTransformer.prototype.__init.call(this);CJSImportTransformer.prototype.__init2.call(this);CJSImportTransformer.prototype.__init3.call(this);;\n    this.declarationInfo = isTypeScriptTransformEnabled\n      ? getDeclarationInfo(tokens)\n      : EMPTY_DECLARATION_INFO;\n  }\n\n  getPrefixCode() {\n    let prefix = \"\";\n    if (this.hadExport) {\n      prefix += 'Object.defineProperty(exports, \"__esModule\", {value: true});';\n    }\n    return prefix;\n  }\n\n  getSuffixCode() {\n    if (this.enableLegacyBabel5ModuleInterop && this.hadDefaultExport && !this.hadNamedExport) {\n      return \"\\nmodule.exports = exports.default;\\n\";\n    }\n    return \"\";\n  }\n\n  process() {\n    // TypeScript `import foo = require('foo');` should always just be translated to plain require.\n    if (this.tokens.matches3(tt._import, tt.name, tt.eq)) {\n      return this.processImportEquals();\n    }\n    if (this.tokens.matches1(tt._import)) {\n      this.processImport();\n      return true;\n    }\n    if (this.tokens.matches2(tt._export, tt.eq)) {\n      this.tokens.replaceToken(\"module.exports\");\n      return true;\n    }\n    if (this.tokens.matches1(tt._export) && !this.tokens.currentToken().isType) {\n      this.hadExport = true;\n      return this.processExport();\n    }\n    if (this.tokens.matches2(tt.name, tt.postIncDec)) {\n      // Fall through to normal identifier matching if this doesn't apply.\n      if (this.processPostIncDec()) {\n        return true;\n      }\n    }\n    if (this.tokens.matches1(tt.name) || this.tokens.matches1(tt.jsxName)) {\n      return this.processIdentifier();\n    }\n    if (this.tokens.matches1(tt.eq)) {\n      return this.processAssignment();\n    }\n    if (this.tokens.matches1(tt.assign)) {\n      return this.processComplexAssignment();\n    }\n    if (this.tokens.matches1(tt.preIncDec)) {\n      return this.processPreIncDec();\n    }\n    return false;\n  }\n\n   processImportEquals() {\n    const importName = this.tokens.identifierNameAtIndex(this.tokens.currentIndex() + 1);\n    if (this.importProcessor.shouldAutomaticallyElideImportedName(importName)) {\n      // If this name is only used as a type, elide the whole import.\n      elideImportEquals(this.tokens);\n    } else {\n      // Otherwise, switch `import` to `const`.\n      this.tokens.replaceToken(\"const\");\n    }\n    return true;\n  }\n\n  /**\n   * Transform this:\n   * import foo, {bar} from 'baz';\n   * into\n   * var _baz = require('baz'); var _baz2 = _interopRequireDefault(_baz);\n   *\n   * The import code was already generated in the import preprocessing step, so\n   * we just need to look it up.\n   */\n   processImport() {\n    if (this.tokens.matches2(tt._import, tt.parenL)) {\n      if (this.preserveDynamicImport) {\n        // Bail out, only making progress for this one token.\n        this.tokens.copyToken();\n        return;\n      }\n      const requireWrapper = this.enableLegacyTypeScriptModuleInterop\n        ? \"\"\n        : `${this.helperManager.getHelperName(\"interopRequireWildcard\")}(`;\n      this.tokens.replaceToken(`Promise.resolve().then(() => ${requireWrapper}require`);\n      const contextId = this.tokens.currentToken().contextId;\n      if (contextId == null) {\n        throw new Error(\"Expected context ID on dynamic import invocation.\");\n      }\n      this.tokens.copyToken();\n      while (!this.tokens.matchesContextIdAndLabel(tt.parenR, contextId)) {\n        this.rootTransformer.processToken();\n      }\n      this.tokens.replaceToken(requireWrapper ? \")))\" : \"))\");\n      return;\n    }\n\n    const shouldElideImport = this.removeImportAndDetectIfShouldElide();\n    if (shouldElideImport) {\n      this.tokens.removeToken();\n    } else {\n      const path = this.tokens.stringValue();\n      this.tokens.replaceTokenTrimmingLeftWhitespace(this.importProcessor.claimImportCode(path));\n      this.tokens.appendCode(this.importProcessor.claimImportCode(path));\n    }\n    removeMaybeImportAttributes(this.tokens);\n    if (this.tokens.matches1(tt.semi)) {\n      this.tokens.removeToken();\n    }\n  }\n\n  /**\n   * Erase this import (since any CJS output would be completely different), and\n   * return true if this import is should be elided due to being a type-only\n   * import. Such imports will not be emitted at all to avoid side effects.\n   *\n   * Import elision only happens with the TypeScript or Flow transforms enabled.\n   *\n   * TODO: This function has some awkward overlap with\n   *  CJSImportProcessor.pruneTypeOnlyImports , and the two should be unified.\n   *  That function handles TypeScript implicit import name elision, and removes\n   *  an import if all typical imported names (without `type`) are removed due\n   *  to being type-only imports. This function handles Flow import removal and\n   *  properly distinguishes `import 'foo'` from `import {} from 'foo'` for TS\n   *  purposes.\n   *\n   * The position should end at the import string.\n   */\n   removeImportAndDetectIfShouldElide() {\n    this.tokens.removeInitialToken();\n    if (\n      this.tokens.matchesContextual(ContextualKeyword._type) &&\n      !this.tokens.matches1AtIndex(this.tokens.currentIndex() + 1, tt.comma) &&\n      !this.tokens.matchesContextualAtIndex(this.tokens.currentIndex() + 1, ContextualKeyword._from)\n    ) {\n      // This is an \"import type\" statement, so exit early.\n      this.removeRemainingImport();\n      return true;\n    }\n\n    if (this.tokens.matches1(tt.name) || this.tokens.matches1(tt.star)) {\n      // We have a default import or namespace import, so there must be some\n      // non-type import.\n      this.removeRemainingImport();\n      return false;\n    }\n\n    if (this.tokens.matches1(tt.string)) {\n      // This is a bare import, so we should proceed with the import.\n      return false;\n    }\n\n    let foundNonTypeImport = false;\n    let foundAnyNamedImport = false;\n    while (!this.tokens.matches1(tt.string)) {\n      // Check if any named imports are of the form \"foo\" or \"foo as bar\", with\n      // no leading \"type\".\n      if (\n        (!foundNonTypeImport && this.tokens.matches1(tt.braceL)) ||\n        this.tokens.matches1(tt.comma)\n      ) {\n        this.tokens.removeToken();\n        if (!this.tokens.matches1(tt.braceR)) {\n          foundAnyNamedImport = true;\n        }\n        if (\n          this.tokens.matches2(tt.name, tt.comma) ||\n          this.tokens.matches2(tt.name, tt.braceR) ||\n          this.tokens.matches4(tt.name, tt.name, tt.name, tt.comma) ||\n          this.tokens.matches4(tt.name, tt.name, tt.name, tt.braceR)\n        ) {\n          foundNonTypeImport = true;\n        }\n      }\n      this.tokens.removeToken();\n    }\n    if (this.keepUnusedImports) {\n      return false;\n    }\n    if (this.isTypeScriptTransformEnabled) {\n      return !foundNonTypeImport;\n    } else if (this.isFlowTransformEnabled) {\n      // In Flow, unlike TS, `import {} from 'foo';` preserves the import.\n      return foundAnyNamedImport && !foundNonTypeImport;\n    } else {\n      return false;\n    }\n  }\n\n   removeRemainingImport() {\n    while (!this.tokens.matches1(tt.string)) {\n      this.tokens.removeToken();\n    }\n  }\n\n   processIdentifier() {\n    const token = this.tokens.currentToken();\n    if (token.shadowsGlobal) {\n      return false;\n    }\n\n    if (token.identifierRole === IdentifierRole.ObjectShorthand) {\n      return this.processObjectShorthand();\n    }\n\n    if (token.identifierRole !== IdentifierRole.Access) {\n      return false;\n    }\n    const replacement = this.importProcessor.getIdentifierReplacement(\n      this.tokens.identifierNameForToken(token),\n    );\n    if (!replacement) {\n      return false;\n    }\n    // Tolerate any number of closing parens while looking for an opening paren\n    // that indicates a function call.\n    let possibleOpenParenIndex = this.tokens.currentIndex() + 1;\n    while (\n      possibleOpenParenIndex < this.tokens.tokens.length &&\n      this.tokens.tokens[possibleOpenParenIndex].type === tt.parenR\n    ) {\n      possibleOpenParenIndex++;\n    }\n    // Avoid treating imported functions as methods of their `exports` object\n    // by using `(0, f)` when the identifier is in a paren expression. Else\n    // use `Function.prototype.call` when the identifier is a guaranteed\n    // function call. When using `call`, pass undefined as the context.\n    if (this.tokens.tokens[possibleOpenParenIndex].type === tt.parenL) {\n      if (\n        this.tokens.tokenAtRelativeIndex(1).type === tt.parenL &&\n        this.tokens.tokenAtRelativeIndex(-1).type !== tt._new\n      ) {\n        this.tokens.replaceToken(`${replacement}.call(void 0, `);\n        // Remove the old paren.\n        this.tokens.removeToken();\n        // Balance out the new paren.\n        this.rootTransformer.processBalancedCode();\n        this.tokens.copyExpectedToken(tt.parenR);\n      } else {\n        // See here: http://2ality.com/2015/12/references.html\n        this.tokens.replaceToken(`(0, ${replacement})`);\n      }\n    } else {\n      this.tokens.replaceToken(replacement);\n    }\n    return true;\n  }\n\n  processObjectShorthand() {\n    const identifier = this.tokens.identifierName();\n    const replacement = this.importProcessor.getIdentifierReplacement(identifier);\n    if (!replacement) {\n      return false;\n    }\n    this.tokens.replaceToken(`${identifier}: ${replacement}`);\n    return true;\n  }\n\n  processExport() {\n    if (\n      this.tokens.matches2(tt._export, tt._enum) ||\n      this.tokens.matches3(tt._export, tt._const, tt._enum)\n    ) {\n      this.hadNamedExport = true;\n      // Let the TypeScript transform handle it.\n      return false;\n    }\n    if (this.tokens.matches2(tt._export, tt._default)) {\n      if (this.tokens.matches3(tt._export, tt._default, tt._enum)) {\n        this.hadDefaultExport = true;\n        // Flow export default enums need some special handling, so handle them\n        // in that tranform rather than this one.\n        return false;\n      }\n      this.processExportDefault();\n      return true;\n    } else if (this.tokens.matches2(tt._export, tt.braceL)) {\n      this.processExportBindings();\n      return true;\n    } else if (\n      this.tokens.matches2(tt._export, tt.name) &&\n      this.tokens.matchesContextualAtIndex(this.tokens.currentIndex() + 1, ContextualKeyword._type)\n    ) {\n      // export type {a};\n      // export type {a as b};\n      // export type {a} from './b';\n      // export type * from './b';\n      // export type * as ns from './b';\n      this.tokens.removeInitialToken();\n      this.tokens.removeToken();\n      if (this.tokens.matches1(tt.braceL)) {\n        while (!this.tokens.matches1(tt.braceR)) {\n          this.tokens.removeToken();\n        }\n        this.tokens.removeToken();\n      } else {\n        // *\n        this.tokens.removeToken();\n        if (this.tokens.matches1(tt._as)) {\n          // as\n          this.tokens.removeToken();\n          // ns\n          this.tokens.removeToken();\n        }\n      }\n      // Remove type re-export `... } from './T'`\n      if (\n        this.tokens.matchesContextual(ContextualKeyword._from) &&\n        this.tokens.matches1AtIndex(this.tokens.currentIndex() + 1, tt.string)\n      ) {\n        this.tokens.removeToken();\n        this.tokens.removeToken();\n        removeMaybeImportAttributes(this.tokens);\n      }\n      return true;\n    }\n    this.hadNamedExport = true;\n    if (\n      this.tokens.matches2(tt._export, tt._var) ||\n      this.tokens.matches2(tt._export, tt._let) ||\n      this.tokens.matches2(tt._export, tt._const)\n    ) {\n      this.processExportVar();\n      return true;\n    } else if (\n      this.tokens.matches2(tt._export, tt._function) ||\n      // export async function\n      this.tokens.matches3(tt._export, tt.name, tt._function)\n    ) {\n      this.processExportFunction();\n      return true;\n    } else if (\n      this.tokens.matches2(tt._export, tt._class) ||\n      this.tokens.matches3(tt._export, tt._abstract, tt._class) ||\n      this.tokens.matches2(tt._export, tt.at)\n    ) {\n      this.processExportClass();\n      return true;\n    } else if (this.tokens.matches2(tt._export, tt.star)) {\n      this.processExportStar();\n      return true;\n    } else {\n      throw new Error(\"Unrecognized export syntax.\");\n    }\n  }\n\n   processAssignment() {\n    const index = this.tokens.currentIndex();\n    const identifierToken = this.tokens.tokens[index - 1];\n    // If the LHS is a type identifier, this must be a declaration like `let a: b = c;`,\n    // with `b` as the identifier, so nothing needs to be done in that case.\n    if (identifierToken.isType || identifierToken.type !== tt.name) {\n      return false;\n    }\n    if (identifierToken.shadowsGlobal) {\n      return false;\n    }\n    if (index >= 2 && this.tokens.matches1AtIndex(index - 2, tt.dot)) {\n      return false;\n    }\n    if (index >= 2 && [tt._var, tt._let, tt._const].includes(this.tokens.tokens[index - 2].type)) {\n      // Declarations don't need an extra assignment. This doesn't avoid the\n      // assignment for comma-separated declarations, but it's still correct\n      // since the assignment is just redundant.\n      return false;\n    }\n    const assignmentSnippet = this.importProcessor.resolveExportBinding(\n      this.tokens.identifierNameForToken(identifierToken),\n    );\n    if (!assignmentSnippet) {\n      return false;\n    }\n    this.tokens.copyToken();\n    this.tokens.appendCode(` ${assignmentSnippet} =`);\n    return true;\n  }\n\n  /**\n   * Process something like `a += 3`, where `a` might be an exported value.\n   */\n   processComplexAssignment() {\n    const index = this.tokens.currentIndex();\n    const identifierToken = this.tokens.tokens[index - 1];\n    if (identifierToken.type !== tt.name) {\n      return false;\n    }\n    if (identifierToken.shadowsGlobal) {\n      return false;\n    }\n    if (index >= 2 && this.tokens.matches1AtIndex(index - 2, tt.dot)) {\n      return false;\n    }\n    const assignmentSnippet = this.importProcessor.resolveExportBinding(\n      this.tokens.identifierNameForToken(identifierToken),\n    );\n    if (!assignmentSnippet) {\n      return false;\n    }\n    this.tokens.appendCode(` = ${assignmentSnippet}`);\n    this.tokens.copyToken();\n    return true;\n  }\n\n  /**\n   * Process something like `++a`, where `a` might be an exported value.\n   */\n   processPreIncDec() {\n    const index = this.tokens.currentIndex();\n    const identifierToken = this.tokens.tokens[index + 1];\n    if (identifierToken.type !== tt.name) {\n      return false;\n    }\n    if (identifierToken.shadowsGlobal) {\n      return false;\n    }\n    // Ignore things like ++a.b and ++a[b] and ++a().b.\n    if (\n      index + 2 < this.tokens.tokens.length &&\n      (this.tokens.matches1AtIndex(index + 2, tt.dot) ||\n        this.tokens.matches1AtIndex(index + 2, tt.bracketL) ||\n        this.tokens.matches1AtIndex(index + 2, tt.parenL))\n    ) {\n      return false;\n    }\n    const identifierName = this.tokens.identifierNameForToken(identifierToken);\n    const assignmentSnippet = this.importProcessor.resolveExportBinding(identifierName);\n    if (!assignmentSnippet) {\n      return false;\n    }\n    this.tokens.appendCode(`${assignmentSnippet} = `);\n    this.tokens.copyToken();\n    return true;\n  }\n\n  /**\n   * Process something like `a++`, where `a` might be an exported value.\n   * This starts at the `a`, not at the `++`.\n   */\n   processPostIncDec() {\n    const index = this.tokens.currentIndex();\n    const identifierToken = this.tokens.tokens[index];\n    const operatorToken = this.tokens.tokens[index + 1];\n    if (identifierToken.type !== tt.name) {\n      return false;\n    }\n    if (identifierToken.shadowsGlobal) {\n      return false;\n    }\n    if (index >= 1 && this.tokens.matches1AtIndex(index - 1, tt.dot)) {\n      return false;\n    }\n    const identifierName = this.tokens.identifierNameForToken(identifierToken);\n    const assignmentSnippet = this.importProcessor.resolveExportBinding(identifierName);\n    if (!assignmentSnippet) {\n      return false;\n    }\n    const operatorCode = this.tokens.rawCodeForToken(operatorToken);\n    // We might also replace the identifier with something like exports.x, so\n    // do that replacement here as well.\n    const base = this.importProcessor.getIdentifierReplacement(identifierName) || identifierName;\n    if (operatorCode === \"++\") {\n      this.tokens.replaceToken(`(${base} = ${assignmentSnippet} = ${base} + 1, ${base} - 1)`);\n    } else if (operatorCode === \"--\") {\n      this.tokens.replaceToken(`(${base} = ${assignmentSnippet} = ${base} - 1, ${base} + 1)`);\n    } else {\n      throw new Error(`Unexpected operator: ${operatorCode}`);\n    }\n    this.tokens.removeToken();\n    return true;\n  }\n\n   processExportDefault() {\n    let exportedRuntimeValue = true;\n    if (\n      this.tokens.matches4(tt._export, tt._default, tt._function, tt.name) ||\n      // export default async function\n      (this.tokens.matches5(tt._export, tt._default, tt.name, tt._function, tt.name) &&\n        this.tokens.matchesContextualAtIndex(\n          this.tokens.currentIndex() + 2,\n          ContextualKeyword._async,\n        ))\n    ) {\n      this.tokens.removeInitialToken();\n      this.tokens.removeToken();\n      // Named function export case: change it to a top-level function\n      // declaration followed by exports statement.\n      const name = this.processNamedFunction();\n      this.tokens.appendCode(` exports.default = ${name};`);\n    } else if (\n      this.tokens.matches4(tt._export, tt._default, tt._class, tt.name) ||\n      this.tokens.matches5(tt._export, tt._default, tt._abstract, tt._class, tt.name) ||\n      this.tokens.matches3(tt._export, tt._default, tt.at)\n    ) {\n      this.tokens.removeInitialToken();\n      this.tokens.removeToken();\n      this.copyDecorators();\n      if (this.tokens.matches1(tt._abstract)) {\n        this.tokens.removeToken();\n      }\n      const name = this.rootTransformer.processNamedClass();\n      this.tokens.appendCode(` exports.default = ${name};`);\n      // After this point, this is a plain \"export default E\" statement.\n    } else if (\n      shouldElideDefaultExport(\n        this.isTypeScriptTransformEnabled,\n        this.keepUnusedImports,\n        this.tokens,\n        this.declarationInfo,\n      )\n    ) {\n      // If the exported value is just an identifier and should be elided by TypeScript\n      // rules, then remove it entirely. It will always have the form `export default e`,\n      // where `e` is an identifier.\n      exportedRuntimeValue = false;\n      this.tokens.removeInitialToken();\n      this.tokens.removeToken();\n      this.tokens.removeToken();\n    } else if (this.reactHotLoaderTransformer) {\n      // We need to assign E to a variable. Change \"export default E\" to\n      // \"let _default; exports.default = _default = E\"\n      const defaultVarName = this.nameManager.claimFreeName(\"_default\");\n      this.tokens.replaceToken(`let ${defaultVarName}; exports.`);\n      this.tokens.copyToken();\n      this.tokens.appendCode(` = ${defaultVarName} =`);\n      this.reactHotLoaderTransformer.setExtractedDefaultExportName(defaultVarName);\n    } else {\n      // Change \"export default E\" to \"exports.default = E\"\n      this.tokens.replaceToken(\"exports.\");\n      this.tokens.copyToken();\n      this.tokens.appendCode(\" =\");\n    }\n    if (exportedRuntimeValue) {\n      this.hadDefaultExport = true;\n    }\n  }\n\n   copyDecorators() {\n    while (this.tokens.matches1(tt.at)) {\n      this.tokens.copyToken();\n      if (this.tokens.matches1(tt.parenL)) {\n        this.tokens.copyExpectedToken(tt.parenL);\n        this.rootTransformer.processBalancedCode();\n        this.tokens.copyExpectedToken(tt.parenR);\n      } else {\n        this.tokens.copyExpectedToken(tt.name);\n        while (this.tokens.matches1(tt.dot)) {\n          this.tokens.copyExpectedToken(tt.dot);\n          this.tokens.copyExpectedToken(tt.name);\n        }\n        if (this.tokens.matches1(tt.parenL)) {\n          this.tokens.copyExpectedToken(tt.parenL);\n          this.rootTransformer.processBalancedCode();\n          this.tokens.copyExpectedToken(tt.parenR);\n        }\n      }\n    }\n  }\n\n  /**\n   * Transform a declaration like `export var`, `export let`, or `export const`.\n   */\n   processExportVar() {\n    if (this.isSimpleExportVar()) {\n      this.processSimpleExportVar();\n    } else {\n      this.processComplexExportVar();\n    }\n  }\n\n  /**\n   * Determine if the export is of the form:\n   * export var/let/const [varName] = [expr];\n   * In other words, determine if function name inference might apply.\n   */\n   isSimpleExportVar() {\n    let tokenIndex = this.tokens.currentIndex();\n    // export\n    tokenIndex++;\n    // var/let/const\n    tokenIndex++;\n    if (!this.tokens.matches1AtIndex(tokenIndex, tt.name)) {\n      return false;\n    }\n    tokenIndex++;\n    while (tokenIndex < this.tokens.tokens.length && this.tokens.tokens[tokenIndex].isType) {\n      tokenIndex++;\n    }\n    if (!this.tokens.matches1AtIndex(tokenIndex, tt.eq)) {\n      return false;\n    }\n    return true;\n  }\n\n  /**\n   * Transform an `export var` declaration initializing a single variable.\n   *\n   * For example, this:\n   * export const f = () => {};\n   * becomes this:\n   * const f = () => {}; exports.f = f;\n   *\n   * The variable is unused (e.g. exports.f has the true value of the export).\n   * We need to produce an assignment of this form so that the function will\n   * have an inferred name of \"f\", which wouldn't happen in the more general\n   * case below.\n   */\n   processSimpleExportVar() {\n    // export\n    this.tokens.removeInitialToken();\n    // var/let/const\n    this.tokens.copyToken();\n    const varName = this.tokens.identifierName();\n    // x: number  ->  x\n    while (!this.tokens.matches1(tt.eq)) {\n      this.rootTransformer.processToken();\n    }\n    const endIndex = this.tokens.currentToken().rhsEndIndex;\n    if (endIndex == null) {\n      throw new Error(\"Expected = token with an end index.\");\n    }\n    while (this.tokens.currentIndex() < endIndex) {\n      this.rootTransformer.processToken();\n    }\n    this.tokens.appendCode(`; exports.${varName} = ${varName}`);\n  }\n\n  /**\n   * Transform normal declaration exports, including handling destructuring.\n   * For example, this:\n   * export const {x: [a = 2, b], c} = d;\n   * becomes this:\n   * ({x: [exports.a = 2, exports.b], c: exports.c} = d;)\n   */\n   processComplexExportVar() {\n    this.tokens.removeInitialToken();\n    this.tokens.removeToken();\n    const needsParens = this.tokens.matches1(tt.braceL);\n    if (needsParens) {\n      this.tokens.appendCode(\"(\");\n    }\n\n    let depth = 0;\n    while (true) {\n      if (\n        this.tokens.matches1(tt.braceL) ||\n        this.tokens.matches1(tt.dollarBraceL) ||\n        this.tokens.matches1(tt.bracketL)\n      ) {\n        depth++;\n        this.tokens.copyToken();\n      } else if (this.tokens.matches1(tt.braceR) || this.tokens.matches1(tt.bracketR)) {\n        depth--;\n        this.tokens.copyToken();\n      } else if (\n        depth === 0 &&\n        !this.tokens.matches1(tt.name) &&\n        !this.tokens.currentToken().isType\n      ) {\n        break;\n      } else if (this.tokens.matches1(tt.eq)) {\n        // Default values might have assignments in the RHS that we want to ignore, so skip past\n        // them.\n        const endIndex = this.tokens.currentToken().rhsEndIndex;\n        if (endIndex == null) {\n          throw new Error(\"Expected = token with an end index.\");\n        }\n        while (this.tokens.currentIndex() < endIndex) {\n          this.rootTransformer.processToken();\n        }\n      } else {\n        const token = this.tokens.currentToken();\n        if (isDeclaration(token)) {\n          const name = this.tokens.identifierName();\n          let replacement = this.importProcessor.getIdentifierReplacement(name);\n          if (replacement === null) {\n            throw new Error(`Expected a replacement for ${name} in \\`export var\\` syntax.`);\n          }\n          if (isObjectShorthandDeclaration(token)) {\n            replacement = `${name}: ${replacement}`;\n          }\n          this.tokens.replaceToken(replacement);\n        } else {\n          this.rootTransformer.processToken();\n        }\n      }\n    }\n\n    if (needsParens) {\n      // Seek to the end of the RHS.\n      const endIndex = this.tokens.currentToken().rhsEndIndex;\n      if (endIndex == null) {\n        throw new Error(\"Expected = token with an end index.\");\n      }\n      while (this.tokens.currentIndex() < endIndex) {\n        this.rootTransformer.processToken();\n      }\n      this.tokens.appendCode(\")\");\n    }\n  }\n\n  /**\n   * Transform this:\n   * export function foo() {}\n   * into this:\n   * function foo() {} exports.foo = foo;\n   */\n   processExportFunction() {\n    this.tokens.replaceToken(\"\");\n    const name = this.processNamedFunction();\n    this.tokens.appendCode(` exports.${name} = ${name};`);\n  }\n\n  /**\n   * Skip past a function with a name and return that name.\n   */\n   processNamedFunction() {\n    if (this.tokens.matches1(tt._function)) {\n      this.tokens.copyToken();\n    } else if (this.tokens.matches2(tt.name, tt._function)) {\n      if (!this.tokens.matchesContextual(ContextualKeyword._async)) {\n        throw new Error(\"Expected async keyword in function export.\");\n      }\n      this.tokens.copyToken();\n      this.tokens.copyToken();\n    }\n    if (this.tokens.matches1(tt.star)) {\n      this.tokens.copyToken();\n    }\n    if (!this.tokens.matches1(tt.name)) {\n      throw new Error(\"Expected identifier for exported function name.\");\n    }\n    const name = this.tokens.identifierName();\n    this.tokens.copyToken();\n    if (this.tokens.currentToken().isType) {\n      this.tokens.removeInitialToken();\n      while (this.tokens.currentToken().isType) {\n        this.tokens.removeToken();\n      }\n    }\n    this.tokens.copyExpectedToken(tt.parenL);\n    this.rootTransformer.processBalancedCode();\n    this.tokens.copyExpectedToken(tt.parenR);\n    this.rootTransformer.processPossibleTypeRange();\n    this.tokens.copyExpectedToken(tt.braceL);\n    this.rootTransformer.processBalancedCode();\n    this.tokens.copyExpectedToken(tt.braceR);\n    return name;\n  }\n\n  /**\n   * Transform this:\n   * export class A {}\n   * into this:\n   * class A {} exports.A = A;\n   */\n   processExportClass() {\n    this.tokens.removeInitialToken();\n    this.copyDecorators();\n    if (this.tokens.matches1(tt._abstract)) {\n      this.tokens.removeToken();\n    }\n    const name = this.rootTransformer.processNamedClass();\n    this.tokens.appendCode(` exports.${name} = ${name};`);\n  }\n\n  /**\n   * Transform this:\n   * export {a, b as c};\n   * into this:\n   * exports.a = a; exports.c = b;\n   *\n   * OR\n   *\n   * Transform this:\n   * export {a, b as c} from './foo';\n   * into the pre-generated Object.defineProperty code from the ImportProcessor.\n   *\n   * For the first case, if the TypeScript transform is enabled, we need to skip\n   * exports that are only defined as types.\n   */\n   processExportBindings() {\n    this.tokens.removeInitialToken();\n    this.tokens.removeToken();\n\n    const isReExport = isExportFrom(this.tokens);\n\n    const exportStatements = [];\n    while (true) {\n      if (this.tokens.matches1(tt.braceR)) {\n        this.tokens.removeToken();\n        break;\n      }\n\n      const specifierInfo = getImportExportSpecifierInfo(this.tokens);\n\n      while (this.tokens.currentIndex() < specifierInfo.endIndex) {\n        this.tokens.removeToken();\n      }\n\n      const shouldRemoveExport =\n        specifierInfo.isType ||\n        (!isReExport && this.shouldElideExportedIdentifier(specifierInfo.leftName));\n      if (!shouldRemoveExport) {\n        const exportedName = specifierInfo.rightName;\n        if (exportedName === \"default\") {\n          this.hadDefaultExport = true;\n        } else {\n          this.hadNamedExport = true;\n        }\n        const localName = specifierInfo.leftName;\n        const newLocalName = this.importProcessor.getIdentifierReplacement(localName);\n        exportStatements.push(`exports.${exportedName} = ${newLocalName || localName};`);\n      }\n\n      if (this.tokens.matches1(tt.braceR)) {\n        this.tokens.removeToken();\n        break;\n      }\n      if (this.tokens.matches2(tt.comma, tt.braceR)) {\n        this.tokens.removeToken();\n        this.tokens.removeToken();\n        break;\n      } else if (this.tokens.matches1(tt.comma)) {\n        this.tokens.removeToken();\n      } else {\n        throw new Error(`Unexpected token: ${JSON.stringify(this.tokens.currentToken())}`);\n      }\n    }\n\n    if (this.tokens.matchesContextual(ContextualKeyword._from)) {\n      // This is an export...from, so throw away the normal named export code\n      // and use the Object.defineProperty code from ImportProcessor.\n      this.tokens.removeToken();\n      const path = this.tokens.stringValue();\n      this.tokens.replaceTokenTrimmingLeftWhitespace(this.importProcessor.claimImportCode(path));\n      removeMaybeImportAttributes(this.tokens);\n    } else {\n      // This is a normal named export, so use that.\n      this.tokens.appendCode(exportStatements.join(\" \"));\n    }\n\n    if (this.tokens.matches1(tt.semi)) {\n      this.tokens.removeToken();\n    }\n  }\n\n   processExportStar() {\n    this.tokens.removeInitialToken();\n    while (!this.tokens.matches1(tt.string)) {\n      this.tokens.removeToken();\n    }\n    const path = this.tokens.stringValue();\n    this.tokens.replaceTokenTrimmingLeftWhitespace(this.importProcessor.claimImportCode(path));\n    removeMaybeImportAttributes(this.tokens);\n    if (this.tokens.matches1(tt.semi)) {\n      this.tokens.removeToken();\n    }\n  }\n\n   shouldElideExportedIdentifier(name) {\n    return (\n      this.isTypeScriptTransformEnabled &&\n      !this.keepUnusedImports &&\n      !this.declarationInfo.valueDeclarations.has(name)\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAGA;AACA;AACA;AAEA;AACA;AAIA;AACA;AACA;AACA;AAGA;;;;;;;;;;;AAKe,MAAM,6BAA6B,wKAAA,CAAA,UAAW;IAC1D,SAAS;QAAC,IAAI,CAAC,SAAS,GAAG;IAAK;IAChC,UAAU;QAAC,IAAI,CAAC,cAAc,GAAG;IAAK;IACtC,UAAU;QAAC,IAAI,CAAC,gBAAgB,GAAG;IAAK;IAGzC,YACG,eAAe,EACf,MAAM,EACN,eAAe,EACf,WAAW,EACX,aAAa,EACb,yBAAyB,EACzB,+BAA+B,EAC/B,mCAAmC,EACnC,4BAA4B,EAC5B,sBAAsB,EACtB,qBAAqB,EACrB,iBAAiB,CAClB;QACA,KAAK;QAAG,IAAI,CAAC,eAAe,GAAG;QAAgB,IAAI,CAAC,MAAM,GAAG;QAAO,IAAI,CAAC,eAAe,GAAG;QAAgB,IAAI,CAAC,WAAW,GAAG;QAAY,IAAI,CAAC,aAAa,GAAG;QAAc,IAAI,CAAC,yBAAyB,GAAG;QAA0B,IAAI,CAAC,+BAA+B,GAAG;QAAgC,IAAI,CAAC,mCAAmC,GAAG;QAAoC,IAAI,CAAC,4BAA4B,GAAG;QAA6B,IAAI,CAAC,sBAAsB,GAAG;QAAuB,IAAI,CAAC,qBAAqB,GAAG;QAAsB,IAAI,CAAC,iBAAiB,GAAG;QAAkB,qBAAqB,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;QAAE,qBAAqB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;QAAE,qBAAqB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;;QACruB,IAAI,CAAC,eAAe,GAAG,+BACnB,CAAA,GAAA,uKAAA,CAAA,UAAkB,AAAD,EAAE,UACnB,uKAAA,CAAA,yBAAsB;IAC5B;IAEA,gBAAgB;QACd,IAAI,SAAS;QACb,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,UAAU;QACZ;QACA,OAAO;IACT;IAEA,gBAAgB;QACd,IAAI,IAAI,CAAC,+BAA+B,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACzF,OAAO;QACT;QACA,OAAO;IACT;IAEA,UAAU;QACR,+FAA+F;QAC/F,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,EAAE,GAAG;YACpD,OAAO,IAAI,CAAC,mBAAmB;QACjC;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,GAAG;YACpC,IAAI,CAAC,aAAa;YAClB,OAAO;QACT;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,EAAE,GAAG;YAC3C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YACzB,OAAO;QACT;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,MAAM,EAAE;YAC1E,IAAI,CAAC,SAAS,GAAG;YACjB,OAAO,IAAI,CAAC,aAAa;QAC3B;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,UAAU,GAAG;YAChD,oEAAoE;YACpE,IAAI,IAAI,CAAC,iBAAiB,IAAI;gBAC5B,OAAO;YACT;QACF;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,GAAG;YACrE,OAAO,IAAI,CAAC,iBAAiB;QAC/B;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,EAAE,GAAG;YAC/B,OAAO,IAAI,CAAC,iBAAiB;QAC/B;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YACnC,OAAO,IAAI,CAAC,wBAAwB;QACtC;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,SAAS,GAAG;YACtC,OAAO,IAAI,CAAC,gBAAgB;QAC9B;QACA,OAAO;IACT;IAEC,sBAAsB;QACrB,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK;QAClF,IAAI,IAAI,CAAC,eAAe,CAAC,oCAAoC,CAAC,aAAa;YACzE,+DAA+D;YAC/D,CAAA,GAAA,sKAAA,CAAA,UAAiB,AAAD,EAAE,IAAI,CAAC,MAAM;QAC/B,OAAO;YACL,yCAAyC;YACzC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAC3B;QACA,OAAO;IACT;IAEA;;;;;;;;GAQC,GACA,gBAAgB;QACf,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YAC/C,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC9B,qDAAqD;gBACrD,IAAI,CAAC,MAAM,CAAC,SAAS;gBACrB;YACF;YACA,MAAM,iBAAiB,IAAI,CAAC,mCAAmC,GAC3D,KACA,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC;YACpE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,6BAA6B,EAAE,eAAe,OAAO,CAAC;YAChF,MAAM,YAAY,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,SAAS;YACtD,IAAI,aAAa,MAAM;gBACrB,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,MAAO,CAAC,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,WAAY;gBAClE,IAAI,CAAC,eAAe,CAAC,YAAY;YACnC;YACA,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,iBAAiB,QAAQ;YAClD;QACF;QAEA,MAAM,oBAAoB,IAAI,CAAC,kCAAkC;QACjE,IAAI,mBAAmB;YACrB,IAAI,CAAC,MAAM,CAAC,WAAW;QACzB,OAAO;YACL,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW;YACpC,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC;YACpF,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC;QAC9D;QACA,CAAA,GAAA,gLAAA,CAAA,8BAA2B,AAAD,EAAE,IAAI,CAAC,MAAM;QACvC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;YACjC,IAAI,CAAC,MAAM,CAAC,WAAW;QACzB;IACF;IAEA;;;;;;;;;;;;;;;;GAgBC,GACA,qCAAqC;QACpC,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC9B,IACE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,4KAAA,CAAA,oBAAiB,CAAC,KAAK,KACrD,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,GAAG,yKAAA,CAAA,YAAE,CAAC,KAAK,KACrE,CAAC,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,GAAG,4KAAA,CAAA,oBAAiB,CAAC,KAAK,GAC7F;YACA,qDAAqD;YACrD,IAAI,CAAC,qBAAqB;YAC1B,OAAO;QACT;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;YAClE,sEAAsE;YACtE,mBAAmB;YACnB,IAAI,CAAC,qBAAqB;YAC1B,OAAO;QACT;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YACnC,+DAA+D;YAC/D,OAAO;QACT;QAEA,IAAI,qBAAqB;QACzB,IAAI,sBAAsB;QAC1B,MAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAG;YACvC,yEAAyE;YACzE,qBAAqB;YACrB,IACE,AAAC,CAAC,sBAAsB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,KACtD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,GAC7B;gBACA,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;oBACpC,sBAAsB;gBACxB;gBACA,IACE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,KAAK,KACtC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,KACvC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,KAAK,KACxD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GACzD;oBACA,qBAAqB;gBACvB;YACF;YACA,IAAI,CAAC,MAAM,CAAC,WAAW;QACzB;QACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,OAAO;QACT;QACA,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACrC,OAAO,CAAC;QACV,OAAO,IAAI,IAAI,CAAC,sBAAsB,EAAE;YACtC,oEAAoE;YACpE,OAAO,uBAAuB,CAAC;QACjC,OAAO;YACL,OAAO;QACT;IACF;IAEC,wBAAwB;QACvB,MAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAG;YACvC,IAAI,CAAC,MAAM,CAAC,WAAW;QACzB;IACF;IAEC,oBAAoB;QACnB,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,YAAY;QACtC,IAAI,MAAM,aAAa,EAAE;YACvB,OAAO;QACT;QAEA,IAAI,MAAM,cAAc,KAAK,yKAAA,CAAA,iBAAc,CAAC,eAAe,EAAE;YAC3D,OAAO,IAAI,CAAC,sBAAsB;QACpC;QAEA,IAAI,MAAM,cAAc,KAAK,yKAAA,CAAA,iBAAc,CAAC,MAAM,EAAE;YAClD,OAAO;QACT;QACA,MAAM,cAAc,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAC/D,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;QAErC,IAAI,CAAC,aAAa;YAChB,OAAO;QACT;QACA,2EAA2E;QAC3E,kCAAkC;QAClC,IAAI,yBAAyB,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK;QAC1D,MACE,yBAAyB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,IAClD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,uBAAuB,CAAC,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,MAAM,CAC7D;YACA;QACF;QACA,yEAAyE;QACzE,uEAAuE;QACvE,oEAAoE;QACpE,mEAAmE;QACnE,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,uBAAuB,CAAC,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE;YACjE,IACE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,GAAG,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,MAAM,IACtD,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,GAAG,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,IAAI,EACrD;gBACA,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,YAAY,cAAc,CAAC;gBACvD,wBAAwB;gBACxB,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB,6BAA6B;gBAC7B,IAAI,CAAC,eAAe,CAAC,mBAAmB;gBACxC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;YACzC,OAAO;gBACL,sDAAsD;gBACtD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAChD;QACF,OAAO;YACL,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAC3B;QACA,OAAO;IACT;IAEA,yBAAyB;QACvB,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,cAAc;QAC7C,MAAM,cAAc,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC;QAClE,IAAI,CAAC,aAAa;YAChB,OAAO;QACT;QACA,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,WAAW,EAAE,EAAE,aAAa;QACxD,OAAO;IACT;IAEA,gBAAgB;QACd,IACE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,KAAK,KACzC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,yKAAA,CAAA,YAAE,CAAC,KAAK,GACpD;YACA,IAAI,CAAC,cAAc,GAAG;YACtB,0CAA0C;YAC1C,OAAO;QACT;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,QAAQ,GAAG;YACjD,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,QAAQ,EAAE,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;gBAC3D,IAAI,CAAC,gBAAgB,GAAG;gBACxB,uEAAuE;gBACvE,yCAAyC;gBACzC,OAAO;YACT;YACA,IAAI,CAAC,oBAAoB;YACzB,OAAO;QACT,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YACtD,IAAI,CAAC,qBAAqB;YAC1B,OAAO;QACT,OAAO,IACL,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,KACxC,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,GAAG,4KAAA,CAAA,oBAAiB,CAAC,KAAK,GAC5F;YACA,mBAAmB;YACnB,wBAAwB;YACxB,8BAA8B;YAC9B,4BAA4B;YAC5B,kCAAkC;YAClC,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,IAAI,CAAC,MAAM,CAAC,WAAW;YACvB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBACnC,MAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAG;oBACvC,IAAI,CAAC,MAAM,CAAC,WAAW;gBACzB;gBACA,IAAI,CAAC,MAAM,CAAC,WAAW;YACzB,OAAO;gBACL,IAAI;gBACJ,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,GAAG,GAAG;oBAChC,KAAK;oBACL,IAAI,CAAC,MAAM,CAAC,WAAW;oBACvB,KAAK;oBACL,IAAI,CAAC,MAAM,CAAC,WAAW;gBACzB;YACF;YACA,2CAA2C;YAC3C,IACE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,4KAAA,CAAA,oBAAiB,CAAC,KAAK,KACrD,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,GAAG,yKAAA,CAAA,YAAE,CAAC,MAAM,GACrE;gBACA,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB,CAAA,GAAA,gLAAA,CAAA,8BAA2B,AAAD,EAAE,IAAI,CAAC,MAAM;YACzC;YACA,OAAO;QACT;QACA,IAAI,CAAC,cAAc,GAAG;QACtB,IACE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,KACxC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,KACxC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GAC1C;YACA,IAAI,CAAC,gBAAgB;YACrB,OAAO;QACT,OAAO,IACL,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,SAAS,KAC7C,wBAAwB;QACxB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,SAAS,GACtD;YACA,IAAI,CAAC,qBAAqB;YAC1B,OAAO;QACT,OAAO,IACL,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,KAC1C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,SAAS,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,KACxD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,EAAE,GACtC;YACA,IAAI,CAAC,kBAAkB;YACvB,OAAO;QACT,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;YACpD,IAAI,CAAC,iBAAiB;YACtB,OAAO;QACT,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;IAEC,oBAAoB;QACnB,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,YAAY;QACtC,MAAM,kBAAkB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE;QACrD,oFAAoF;QACpF,wEAAwE;QACxE,IAAI,gBAAgB,MAAM,IAAI,gBAAgB,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE;YAC9D,OAAO;QACT;QACA,IAAI,gBAAgB,aAAa,EAAE;YACjC,OAAO;QACT;QACA,IAAI,SAAS,KAAK,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,GAAG,yKAAA,CAAA,YAAE,CAAC,GAAG,GAAG;YAChE,OAAO;QACT;QACA,IAAI,SAAS,KAAK;YAAC,yKAAA,CAAA,YAAE,CAAC,IAAI;YAAE,yKAAA,CAAA,YAAE,CAAC,IAAI;YAAE,yKAAA,CAAA,YAAE,CAAC,MAAM;SAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,GAAG;YAC5F,sEAAsE;YACtE,sEAAsE;YACtE,0CAA0C;YAC1C,OAAO;QACT;QACA,MAAM,oBAAoB,IAAI,CAAC,eAAe,CAAC,oBAAoB,CACjE,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;QAErC,IAAI,CAAC,mBAAmB;YACtB,OAAO;QACT;QACA,IAAI,CAAC,MAAM,CAAC,SAAS;QACrB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,kBAAkB,EAAE,CAAC;QAChD,OAAO;IACT;IAEA;;GAEC,GACA,2BAA2B;QAC1B,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,YAAY;QACtC,MAAM,kBAAkB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE;QACrD,IAAI,gBAAgB,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE;YACpC,OAAO;QACT;QACA,IAAI,gBAAgB,aAAa,EAAE;YACjC,OAAO;QACT;QACA,IAAI,SAAS,KAAK,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,GAAG,yKAAA,CAAA,YAAE,CAAC,GAAG,GAAG;YAChE,OAAO;QACT;QACA,MAAM,oBAAoB,IAAI,CAAC,eAAe,CAAC,oBAAoB,CACjE,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;QAErC,IAAI,CAAC,mBAAmB;YACtB,OAAO;QACT;QACA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE,mBAAmB;QAChD,IAAI,CAAC,MAAM,CAAC,SAAS;QACrB,OAAO;IACT;IAEA;;GAEC,GACA,mBAAmB;QAClB,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,YAAY;QACtC,MAAM,kBAAkB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE;QACrD,IAAI,gBAAgB,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE;YACpC,OAAO;QACT;QACA,IAAI,gBAAgB,aAAa,EAAE;YACjC,OAAO;QACT;QACA,mDAAmD;QACnD,IACE,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,IACrC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,GAAG,yKAAA,CAAA,YAAE,CAAC,GAAG,KAC5C,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,GAAG,yKAAA,CAAA,YAAE,CAAC,QAAQ,KAClD,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,GAAG,yKAAA,CAAA,YAAE,CAAC,MAAM,CAAC,GACnD;YACA,OAAO;QACT;QACA,MAAM,iBAAiB,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;QAC1D,MAAM,oBAAoB,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC;QACpE,IAAI,CAAC,mBAAmB;YACtB,OAAO;QACT;QACA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,kBAAkB,GAAG,CAAC;QAChD,IAAI,CAAC,MAAM,CAAC,SAAS;QACrB,OAAO;IACT;IAEA;;;GAGC,GACA,oBAAoB;QACnB,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,YAAY;QACtC,MAAM,kBAAkB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM;QACjD,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE;QACnD,IAAI,gBAAgB,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE;YACpC,OAAO;QACT;QACA,IAAI,gBAAgB,aAAa,EAAE;YACjC,OAAO;QACT;QACA,IAAI,SAAS,KAAK,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,GAAG,yKAAA,CAAA,YAAE,CAAC,GAAG,GAAG;YAChE,OAAO;QACT;QACA,MAAM,iBAAiB,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;QAC1D,MAAM,oBAAoB,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC;QACpE,IAAI,CAAC,mBAAmB;YACtB,OAAO;QACT;QACA,MAAM,eAAe,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;QACjD,yEAAyE;QACzE,oCAAoC;QACpC,MAAM,OAAO,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC,mBAAmB;QAC9E,IAAI,iBAAiB,MAAM;YACzB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE,kBAAkB,GAAG,EAAE,KAAK,MAAM,EAAE,KAAK,KAAK,CAAC;QACxF,OAAO,IAAI,iBAAiB,MAAM;YAChC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE,kBAAkB,GAAG,EAAE,KAAK,MAAM,EAAE,KAAK,KAAK,CAAC;QACxF,OAAO;YACL,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,cAAc;QACxD;QACA,IAAI,CAAC,MAAM,CAAC,WAAW;QACvB,OAAO;IACT;IAEC,uBAAuB;QACtB,IAAI,uBAAuB;QAC3B,IACE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,QAAQ,EAAE,yKAAA,CAAA,YAAE,CAAC,SAAS,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,KAElE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,QAAQ,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,SAAS,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,KAC3E,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAClC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,GAC7B,4KAAA,CAAA,oBAAiB,CAAC,MAAM,GAE5B;YACA,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,IAAI,CAAC,MAAM,CAAC,WAAW;YACvB,gEAAgE;YAChE,6CAA6C;YAC7C,MAAM,OAAO,IAAI,CAAC,oBAAoB;YACtC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;QACtD,OAAO,IACL,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,QAAQ,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,KAChE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,QAAQ,EAAE,yKAAA,CAAA,YAAE,CAAC,SAAS,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,KAC9E,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,QAAQ,EAAE,yKAAA,CAAA,YAAE,CAAC,EAAE,GACnD;YACA,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,IAAI,CAAC,MAAM,CAAC,WAAW;YACvB,IAAI,CAAC,cAAc;YACnB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,SAAS,GAAG;gBACtC,IAAI,CAAC,MAAM,CAAC,WAAW;YACzB;YACA,MAAM,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB;YACnD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;QACpD,kEAAkE;QACpE,OAAO,IACL,CAAA,GAAA,6KAAA,CAAA,UAAwB,AAAD,EACrB,IAAI,CAAC,4BAA4B,EACjC,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,eAAe,GAEtB;YACA,iFAAiF;YACjF,mFAAmF;YACnF,8BAA8B;YAC9B,uBAAuB;YACvB,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,IAAI,CAAC,MAAM,CAAC,WAAW;YACvB,IAAI,CAAC,MAAM,CAAC,WAAW;QACzB,OAAO,IAAI,IAAI,CAAC,yBAAyB,EAAE;YACzC,kEAAkE;YAClE,iDAAiD;YACjD,MAAM,iBAAiB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;YACtD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,eAAe,UAAU,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE,eAAe,EAAE,CAAC;YAC/C,IAAI,CAAC,yBAAyB,CAAC,6BAA6B,CAAC;QAC/D,OAAO;YACL,qDAAqD;YACrD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACzB;QACA,IAAI,sBAAsB;YACxB,IAAI,CAAC,gBAAgB,GAAG;QAC1B;IACF;IAEC,iBAAiB;QAChB,MAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,EAAE,EAAG;YAClC,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBACnC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;gBACvC,IAAI,CAAC,eAAe,CAAC,mBAAmB;gBACxC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;YACzC,OAAO;gBACL,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI;gBACrC,MAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,GAAG,EAAG;oBACnC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,GAAG;oBACpC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI;gBACvC;gBACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;oBACnC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;oBACvC,IAAI,CAAC,eAAe,CAAC,mBAAmB;oBACxC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;gBACzC;YACF;QACF;IACF;IAEA;;GAEC,GACA,mBAAmB;QAClB,IAAI,IAAI,CAAC,iBAAiB,IAAI;YAC5B,IAAI,CAAC,sBAAsB;QAC7B,OAAO;YACL,IAAI,CAAC,uBAAuB;QAC9B;IACF;IAEA;;;;GAIC,GACA,oBAAoB;QACnB,IAAI,aAAa,IAAI,CAAC,MAAM,CAAC,YAAY;QACzC,SAAS;QACT;QACA,gBAAgB;QAChB;QACA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,YAAY,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;YACrD,OAAO;QACT;QACA;QACA,MAAO,aAAa,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAE;YACtF;QACF;QACA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,YAAY,yKAAA,CAAA,YAAE,CAAC,EAAE,GAAG;YACnD,OAAO;QACT;QACA,OAAO;IACT;IAEA;;;;;;;;;;;;GAYC,GACA,yBAAyB;QACxB,SAAS;QACT,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC9B,gBAAgB;QAChB,IAAI,CAAC,MAAM,CAAC,SAAS;QACrB,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC,cAAc;QAC1C,mBAAmB;QACnB,MAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,EAAE,EAAG;YACnC,IAAI,CAAC,eAAe,CAAC,YAAY;QACnC;QACA,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,WAAW;QACvD,IAAI,YAAY,MAAM;YACpB,MAAM,IAAI,MAAM;QAClB;QACA,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,SAAU;YAC5C,IAAI,CAAC,eAAe,CAAC,YAAY;QACnC;QACA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,UAAU,EAAE,QAAQ,GAAG,EAAE,SAAS;IAC5D;IAEA;;;;;;GAMC,GACA,0BAA0B;QACzB,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC9B,IAAI,CAAC,MAAM,CAAC,WAAW;QACvB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;QAClD,IAAI,aAAa;YACf,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACzB;QAEA,IAAI,QAAQ;QACZ,MAAO,KAAM;YACX,IACE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,KAC9B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,YAAY,KACpC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,QAAQ,GAChC;gBACA;gBACA,IAAI,CAAC,MAAM,CAAC,SAAS;YACvB,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,QAAQ,GAAG;gBAC/E;gBACA,IAAI,CAAC,MAAM,CAAC,SAAS;YACvB,OAAO,IACL,UAAU,KACV,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,KAC7B,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,MAAM,EAClC;gBACA;YACF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,EAAE,GAAG;gBACtC,wFAAwF;gBACxF,QAAQ;gBACR,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,WAAW;gBACvD,IAAI,YAAY,MAAM;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBACA,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,SAAU;oBAC5C,IAAI,CAAC,eAAe,CAAC,YAAY;gBACnC;YACF,OAAO;gBACL,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,YAAY;gBACtC,IAAI,CAAA,GAAA,yKAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;oBACxB,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc;oBACvC,IAAI,cAAc,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC;oBAChE,IAAI,gBAAgB,MAAM;wBACxB,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,KAAK,0BAA0B,CAAC;oBAChF;oBACA,IAAI,CAAA,GAAA,yKAAA,CAAA,+BAA4B,AAAD,EAAE,QAAQ;wBACvC,cAAc,GAAG,KAAK,EAAE,EAAE,aAAa;oBACzC;oBACA,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC3B,OAAO;oBACL,IAAI,CAAC,eAAe,CAAC,YAAY;gBACnC;YACF;QACF;QAEA,IAAI,aAAa;YACf,8BAA8B;YAC9B,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,WAAW;YACvD,IAAI,YAAY,MAAM;gBACpB,MAAM,IAAI,MAAM;YAClB;YACA,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,SAAU;gBAC5C,IAAI,CAAC,eAAe,CAAC,YAAY;YACnC;YACA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACzB;IACF;IAEA;;;;;GAKC,GACA,wBAAwB;QACvB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QACzB,MAAM,OAAO,IAAI,CAAC,oBAAoB;QACtC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,EAAE,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC;IACtD;IAEA;;GAEC,GACA,uBAAuB;QACtB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,SAAS,GAAG;YACtC,IAAI,CAAC,MAAM,CAAC,SAAS;QACvB,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,SAAS,GAAG;YACtD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,4KAAA,CAAA,oBAAiB,CAAC,MAAM,GAAG;gBAC5D,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,IAAI,CAAC,MAAM,CAAC,SAAS;QACvB;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;YACjC,IAAI,CAAC,MAAM,CAAC,SAAS;QACvB;QACA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;YAClC,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc;QACvC,IAAI,CAAC,MAAM,CAAC,SAAS;QACrB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,MAAM,EAAE;YACrC,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,MAAM,CAAE;gBACxC,IAAI,CAAC,MAAM,CAAC,WAAW;YACzB;QACF;QACA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;QACvC,IAAI,CAAC,eAAe,CAAC,mBAAmB;QACxC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;QACvC,IAAI,CAAC,eAAe,CAAC,wBAAwB;QAC7C,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;QACvC,IAAI,CAAC,eAAe,CAAC,mBAAmB;QACxC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;QACvC,OAAO;IACT;IAEA;;;;;GAKC,GACA,qBAAqB;QACpB,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC9B,IAAI,CAAC,cAAc;QACnB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,SAAS,GAAG;YACtC,IAAI,CAAC,MAAM,CAAC,WAAW;QACzB;QACA,MAAM,OAAO,IAAI,CAAC,eAAe,CAAC,iBAAiB;QACnD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,EAAE,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC;IACtD;IAEA;;;;;;;;;;;;;;GAcC,GACA,wBAAwB;QACvB,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC9B,IAAI,CAAC,MAAM,CAAC,WAAW;QAEvB,MAAM,aAAa,CAAA,GAAA,iKAAA,CAAA,UAAY,AAAD,EAAE,IAAI,CAAC,MAAM;QAE3C,MAAM,mBAAmB,EAAE;QAC3B,MAAO,KAAM;YACX,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBACnC,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB;YACF;YAEA,MAAM,gBAAgB,CAAA,GAAA,iLAAA,CAAA,UAA4B,AAAD,EAAE,IAAI,CAAC,MAAM;YAE9D,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,cAAc,QAAQ,CAAE;gBAC1D,IAAI,CAAC,MAAM,CAAC,WAAW;YACzB;YAEA,MAAM,qBACJ,cAAc,MAAM,IACnB,CAAC,cAAc,IAAI,CAAC,6BAA6B,CAAC,cAAc,QAAQ;YAC3E,IAAI,CAAC,oBAAoB;gBACvB,MAAM,eAAe,cAAc,SAAS;gBAC5C,IAAI,iBAAiB,WAAW;oBAC9B,IAAI,CAAC,gBAAgB,GAAG;gBAC1B,OAAO;oBACL,IAAI,CAAC,cAAc,GAAG;gBACxB;gBACA,MAAM,YAAY,cAAc,QAAQ;gBACxC,MAAM,eAAe,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC;gBACnE,iBAAiB,IAAI,CAAC,CAAC,QAAQ,EAAE,aAAa,GAAG,EAAE,gBAAgB,UAAU,CAAC,CAAC;YACjF;YAEA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBACnC,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB;YACF;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBAC7C,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB;YACF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;gBACzC,IAAI,CAAC,MAAM,CAAC,WAAW;YACzB,OAAO;gBACL,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,KAAK,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK;YACnF;QACF;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,4KAAA,CAAA,oBAAiB,CAAC,KAAK,GAAG;YAC1D,uEAAuE;YACvE,+DAA+D;YAC/D,IAAI,CAAC,MAAM,CAAC,WAAW;YACvB,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW;YACpC,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC;YACpF,CAAA,GAAA,gLAAA,CAAA,8BAA2B,AAAD,EAAE,IAAI,CAAC,MAAM;QACzC,OAAO;YACL,8CAA8C;YAC9C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,iBAAiB,IAAI,CAAC;QAC/C;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;YACjC,IAAI,CAAC,MAAM,CAAC,WAAW;QACzB;IACF;IAEC,oBAAoB;QACnB,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC9B,MAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAG;YACvC,IAAI,CAAC,MAAM,CAAC,WAAW;QACzB;QACA,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW;QACpC,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC;QACpF,CAAA,GAAA,gLAAA,CAAA,8BAA2B,AAAD,EAAE,IAAI,CAAC,MAAM;QACvC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;YACjC,IAAI,CAAC,MAAM,CAAC,WAAW;QACzB;IACF;IAEC,8BAA8B,IAAI,EAAE;QACnC,OACE,IAAI,CAAC,4BAA4B,IACjC,CAAC,IAAI,CAAC,iBAAiB,IACvB,CAAC,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAC;IAEhD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1489, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/transformers/ESMImportTransformer.js"], "sourcesContent": ["\n\n\nimport {ContextualKeyword} from \"../parser/tokenizer/keywords\";\nimport {TokenType as tt} from \"../parser/tokenizer/types\";\n\nimport elideImportEquals from \"../util/elideImportEquals\";\nimport getDeclarationInfo, {\n\n  EMPTY_DECLARATION_INFO,\n} from \"../util/getDeclarationInfo\";\nimport getImportExportSpecifierInfo from \"../util/getImportExportSpecifierInfo\";\nimport {getNonTypeIdentifiers} from \"../util/getNonTypeIdentifiers\";\nimport isExportFrom from \"../util/isExportFrom\";\nimport {removeMaybeImportAttributes} from \"../util/removeMaybeImportAttributes\";\nimport shouldElideDefaultExport from \"../util/shouldElideDefaultExport\";\n\nimport Transformer from \"./Transformer\";\n\n/**\n * Class for editing import statements when we are keeping the code as ESM. We still need to remove\n * type-only imports in TypeScript and Flow.\n */\nexport default class ESMImportTransformer extends Transformer {\n  \n  \n  \n\n  constructor(\n     tokens,\n     nameManager,\n     helperManager,\n     reactHotLoaderTransformer,\n     isTypeScriptTransformEnabled,\n     isFlowTransformEnabled,\n     keepUnusedImports,\n    options,\n  ) {\n    super();this.tokens = tokens;this.nameManager = nameManager;this.helperManager = helperManager;this.reactHotLoaderTransformer = reactHotLoaderTransformer;this.isTypeScriptTransformEnabled = isTypeScriptTransformEnabled;this.isFlowTransformEnabled = isFlowTransformEnabled;this.keepUnusedImports = keepUnusedImports;;\n    this.nonTypeIdentifiers =\n      isTypeScriptTransformEnabled && !keepUnusedImports\n        ? getNonTypeIdentifiers(tokens, options)\n        : new Set();\n    this.declarationInfo =\n      isTypeScriptTransformEnabled && !keepUnusedImports\n        ? getDeclarationInfo(tokens)\n        : EMPTY_DECLARATION_INFO;\n    this.injectCreateRequireForImportRequire = Boolean(options.injectCreateRequireForImportRequire);\n  }\n\n  process() {\n    // TypeScript `import foo = require('foo');` should always just be translated to plain require.\n    if (this.tokens.matches3(tt._import, tt.name, tt.eq)) {\n      return this.processImportEquals();\n    }\n    if (\n      this.tokens.matches4(tt._import, tt.name, tt.name, tt.eq) &&\n      this.tokens.matchesContextualAtIndex(this.tokens.currentIndex() + 1, ContextualKeyword._type)\n    ) {\n      // import type T = require('T')\n      this.tokens.removeInitialToken();\n      // This construct is always exactly 8 tokens long, so remove the 7 remaining tokens.\n      for (let i = 0; i < 7; i++) {\n        this.tokens.removeToken();\n      }\n      return true;\n    }\n    if (this.tokens.matches2(tt._export, tt.eq)) {\n      this.tokens.replaceToken(\"module.exports\");\n      return true;\n    }\n    if (\n      this.tokens.matches5(tt._export, tt._import, tt.name, tt.name, tt.eq) &&\n      this.tokens.matchesContextualAtIndex(this.tokens.currentIndex() + 2, ContextualKeyword._type)\n    ) {\n      // export import type T = require('T')\n      this.tokens.removeInitialToken();\n      // This construct is always exactly 9 tokens long, so remove the 8 remaining tokens.\n      for (let i = 0; i < 8; i++) {\n        this.tokens.removeToken();\n      }\n      return true;\n    }\n    if (this.tokens.matches1(tt._import)) {\n      return this.processImport();\n    }\n    if (this.tokens.matches2(tt._export, tt._default)) {\n      return this.processExportDefault();\n    }\n    if (this.tokens.matches2(tt._export, tt.braceL)) {\n      return this.processNamedExports();\n    }\n    if (\n      this.tokens.matches2(tt._export, tt.name) &&\n      this.tokens.matchesContextualAtIndex(this.tokens.currentIndex() + 1, ContextualKeyword._type)\n    ) {\n      // export type {a};\n      // export type {a as b};\n      // export type {a} from './b';\n      // export type * from './b';\n      // export type * as ns from './b';\n      this.tokens.removeInitialToken();\n      this.tokens.removeToken();\n      if (this.tokens.matches1(tt.braceL)) {\n        while (!this.tokens.matches1(tt.braceR)) {\n          this.tokens.removeToken();\n        }\n        this.tokens.removeToken();\n      } else {\n        // *\n        this.tokens.removeToken();\n        if (this.tokens.matches1(tt._as)) {\n          // as\n          this.tokens.removeToken();\n          // ns\n          this.tokens.removeToken();\n        }\n      }\n      // Remove type re-export `... } from './T'`\n      if (\n        this.tokens.matchesContextual(ContextualKeyword._from) &&\n        this.tokens.matches1AtIndex(this.tokens.currentIndex() + 1, tt.string)\n      ) {\n        this.tokens.removeToken();\n        this.tokens.removeToken();\n        removeMaybeImportAttributes(this.tokens);\n      }\n      return true;\n    }\n    return false;\n  }\n\n   processImportEquals() {\n    const importName = this.tokens.identifierNameAtIndex(this.tokens.currentIndex() + 1);\n    if (this.shouldAutomaticallyElideImportedName(importName)) {\n      // If this name is only used as a type, elide the whole import.\n      elideImportEquals(this.tokens);\n    } else if (this.injectCreateRequireForImportRequire) {\n      // We're using require in an environment (Node ESM) that doesn't provide\n      // it as a global, so generate a helper to import it.\n      // import -> const\n      this.tokens.replaceToken(\"const\");\n      // Foo\n      this.tokens.copyToken();\n      // =\n      this.tokens.copyToken();\n      // require\n      this.tokens.replaceToken(this.helperManager.getHelperName(\"require\"));\n    } else {\n      // Otherwise, just switch `import` to `const`.\n      this.tokens.replaceToken(\"const\");\n    }\n    return true;\n  }\n\n   processImport() {\n    if (this.tokens.matches2(tt._import, tt.parenL)) {\n      // Dynamic imports don't need to be transformed.\n      return false;\n    }\n\n    const snapshot = this.tokens.snapshot();\n    const allImportsRemoved = this.removeImportTypeBindings();\n    if (allImportsRemoved) {\n      this.tokens.restoreToSnapshot(snapshot);\n      while (!this.tokens.matches1(tt.string)) {\n        this.tokens.removeToken();\n      }\n      this.tokens.removeToken();\n      removeMaybeImportAttributes(this.tokens);\n      if (this.tokens.matches1(tt.semi)) {\n        this.tokens.removeToken();\n      }\n    }\n    return true;\n  }\n\n  /**\n   * Remove type bindings from this import, leaving the rest of the import intact.\n   *\n   * Return true if this import was ONLY types, and thus is eligible for removal. This will bail out\n   * of the replacement operation, so we can return early here.\n   */\n   removeImportTypeBindings() {\n    this.tokens.copyExpectedToken(tt._import);\n    if (\n      this.tokens.matchesContextual(ContextualKeyword._type) &&\n      !this.tokens.matches1AtIndex(this.tokens.currentIndex() + 1, tt.comma) &&\n      !this.tokens.matchesContextualAtIndex(this.tokens.currentIndex() + 1, ContextualKeyword._from)\n    ) {\n      // This is an \"import type\" statement, so exit early.\n      return true;\n    }\n\n    if (this.tokens.matches1(tt.string)) {\n      // This is a bare import, so we should proceed with the import.\n      this.tokens.copyToken();\n      return false;\n    }\n\n    // Skip the \"module\" token in import reflection.\n    if (\n      this.tokens.matchesContextual(ContextualKeyword._module) &&\n      this.tokens.matchesContextualAtIndex(this.tokens.currentIndex() + 2, ContextualKeyword._from)\n    ) {\n      this.tokens.copyToken();\n    }\n\n    let foundNonTypeImport = false;\n    let foundAnyNamedImport = false;\n    let needsComma = false;\n\n    // Handle default import.\n    if (this.tokens.matches1(tt.name)) {\n      if (this.shouldAutomaticallyElideImportedName(this.tokens.identifierName())) {\n        this.tokens.removeToken();\n        if (this.tokens.matches1(tt.comma)) {\n          this.tokens.removeToken();\n        }\n      } else {\n        foundNonTypeImport = true;\n        this.tokens.copyToken();\n        if (this.tokens.matches1(tt.comma)) {\n          // We're in a statement like:\n          // import A, * as B from './A';\n          // or\n          // import A, {foo} from './A';\n          // where the `A` is being kept. The comma should be removed if an only\n          // if the next part of the import statement is elided, but that's hard\n          // to determine at this point in the code. Instead, always remove it\n          // and set a flag to add it back if necessary.\n          needsComma = true;\n          this.tokens.removeToken();\n        }\n      }\n    }\n\n    if (this.tokens.matches1(tt.star)) {\n      if (this.shouldAutomaticallyElideImportedName(this.tokens.identifierNameAtRelativeIndex(2))) {\n        this.tokens.removeToken();\n        this.tokens.removeToken();\n        this.tokens.removeToken();\n      } else {\n        if (needsComma) {\n          this.tokens.appendCode(\",\");\n        }\n        foundNonTypeImport = true;\n        this.tokens.copyExpectedToken(tt.star);\n        this.tokens.copyExpectedToken(tt.name);\n        this.tokens.copyExpectedToken(tt.name);\n      }\n    } else if (this.tokens.matches1(tt.braceL)) {\n      if (needsComma) {\n        this.tokens.appendCode(\",\");\n      }\n      this.tokens.copyToken();\n      while (!this.tokens.matches1(tt.braceR)) {\n        foundAnyNamedImport = true;\n        const specifierInfo = getImportExportSpecifierInfo(this.tokens);\n        if (\n          specifierInfo.isType ||\n          this.shouldAutomaticallyElideImportedName(specifierInfo.rightName)\n        ) {\n          while (this.tokens.currentIndex() < specifierInfo.endIndex) {\n            this.tokens.removeToken();\n          }\n          if (this.tokens.matches1(tt.comma)) {\n            this.tokens.removeToken();\n          }\n        } else {\n          foundNonTypeImport = true;\n          while (this.tokens.currentIndex() < specifierInfo.endIndex) {\n            this.tokens.copyToken();\n          }\n          if (this.tokens.matches1(tt.comma)) {\n            this.tokens.copyToken();\n          }\n        }\n      }\n      this.tokens.copyExpectedToken(tt.braceR);\n    }\n\n    if (this.keepUnusedImports) {\n      return false;\n    }\n    if (this.isTypeScriptTransformEnabled) {\n      return !foundNonTypeImport;\n    } else if (this.isFlowTransformEnabled) {\n      // In Flow, unlike TS, `import {} from 'foo';` preserves the import.\n      return foundAnyNamedImport && !foundNonTypeImport;\n    } else {\n      return false;\n    }\n  }\n\n   shouldAutomaticallyElideImportedName(name) {\n    return (\n      this.isTypeScriptTransformEnabled &&\n      !this.keepUnusedImports &&\n      !this.nonTypeIdentifiers.has(name)\n    );\n  }\n\n   processExportDefault() {\n    if (\n      shouldElideDefaultExport(\n        this.isTypeScriptTransformEnabled,\n        this.keepUnusedImports,\n        this.tokens,\n        this.declarationInfo,\n      )\n    ) {\n      // If the exported value is just an identifier and should be elided by TypeScript\n      // rules, then remove it entirely. It will always have the form `export default e`,\n      // where `e` is an identifier.\n      this.tokens.removeInitialToken();\n      this.tokens.removeToken();\n      this.tokens.removeToken();\n      return true;\n    }\n\n    const alreadyHasName =\n      this.tokens.matches4(tt._export, tt._default, tt._function, tt.name) ||\n      // export default async function\n      (this.tokens.matches5(tt._export, tt._default, tt.name, tt._function, tt.name) &&\n        this.tokens.matchesContextualAtIndex(\n          this.tokens.currentIndex() + 2,\n          ContextualKeyword._async,\n        )) ||\n      this.tokens.matches4(tt._export, tt._default, tt._class, tt.name) ||\n      this.tokens.matches5(tt._export, tt._default, tt._abstract, tt._class, tt.name);\n\n    if (!alreadyHasName && this.reactHotLoaderTransformer) {\n      // This is a plain \"export default E\" statement and we need to assign E to a variable.\n      // Change \"export default E\" to \"let _default; export default _default = E\"\n      const defaultVarName = this.nameManager.claimFreeName(\"_default\");\n      this.tokens.replaceToken(`let ${defaultVarName}; export`);\n      this.tokens.copyToken();\n      this.tokens.appendCode(` ${defaultVarName} =`);\n      this.reactHotLoaderTransformer.setExtractedDefaultExportName(defaultVarName);\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Handle a statement with one of these forms:\n   * export {a, type b};\n   * export {c, type d} from 'foo';\n   *\n   * In both cases, any explicit type exports should be removed. In the first\n   * case, we also need to handle implicit export elision for names declared as\n   * types. In the second case, we must NOT do implicit named export elision,\n   * but we must remove the runtime import if all exports are type exports.\n   */\n   processNamedExports() {\n    if (!this.isTypeScriptTransformEnabled) {\n      return false;\n    }\n    this.tokens.copyExpectedToken(tt._export);\n    this.tokens.copyExpectedToken(tt.braceL);\n\n    const isReExport = isExportFrom(this.tokens);\n    let foundNonTypeExport = false;\n    while (!this.tokens.matches1(tt.braceR)) {\n      const specifierInfo = getImportExportSpecifierInfo(this.tokens);\n      if (\n        specifierInfo.isType ||\n        (!isReExport && this.shouldElideExportedName(specifierInfo.leftName))\n      ) {\n        // Type export, so remove all tokens, including any comma.\n        while (this.tokens.currentIndex() < specifierInfo.endIndex) {\n          this.tokens.removeToken();\n        }\n        if (this.tokens.matches1(tt.comma)) {\n          this.tokens.removeToken();\n        }\n      } else {\n        // Non-type export, so copy all tokens, including any comma.\n        foundNonTypeExport = true;\n        while (this.tokens.currentIndex() < specifierInfo.endIndex) {\n          this.tokens.copyToken();\n        }\n        if (this.tokens.matches1(tt.comma)) {\n          this.tokens.copyToken();\n        }\n      }\n    }\n    this.tokens.copyExpectedToken(tt.braceR);\n\n    if (!this.keepUnusedImports && isReExport && !foundNonTypeExport) {\n      // This is a type-only re-export, so skip evaluating the other module. Technically this\n      // leaves the statement as `export {}`, but that's ok since that's a no-op.\n      this.tokens.removeToken();\n      this.tokens.removeToken();\n      removeMaybeImportAttributes(this.tokens);\n    }\n\n    return true;\n  }\n\n  /**\n   * ESM elides all imports with the rule that we only elide if we see that it's\n   * a type and never see it as a value. This is in contrast to CJS, which\n   * elides imports that are completely unknown.\n   */\n   shouldElideExportedName(name) {\n    return (\n      this.isTypeScriptTransformEnabled &&\n      !this.keepUnusedImports &&\n      this.declarationInfo.typeDeclarations.has(name) &&\n      !this.declarationInfo.valueDeclarations.has(name)\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAGA;AACA;AAEA;AACA;AAIA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;AAMe,MAAM,6BAA6B,wKAAA,CAAA,UAAW;IAK3D,YACG,MAAM,EACN,WAAW,EACX,aAAa,EACb,yBAAyB,EACzB,4BAA4B,EAC5B,sBAAsB,EACtB,iBAAiB,EAClB,OAAO,CACP;QACA,KAAK;QAAG,IAAI,CAAC,MAAM,GAAG;QAAO,IAAI,CAAC,WAAW,GAAG;QAAY,IAAI,CAAC,aAAa,GAAG;QAAc,IAAI,CAAC,yBAAyB,GAAG;QAA0B,IAAI,CAAC,4BAA4B,GAAG;QAA6B,IAAI,CAAC,sBAAsB,GAAG;QAAuB,IAAI,CAAC,iBAAiB,GAAG;;QACzS,IAAI,CAAC,kBAAkB,GACrB,gCAAgC,CAAC,oBAC7B,CAAA,GAAA,0KAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ,WAC9B,IAAI;QACV,IAAI,CAAC,eAAe,GAClB,gCAAgC,CAAC,oBAC7B,CAAA,GAAA,uKAAA,CAAA,UAAkB,AAAD,EAAE,UACnB,uKAAA,CAAA,yBAAsB;QAC5B,IAAI,CAAC,mCAAmC,GAAG,QAAQ,QAAQ,mCAAmC;IAChG;IAEA,UAAU;QACR,+FAA+F;QAC/F,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,EAAE,GAAG;YACpD,OAAO,IAAI,CAAC,mBAAmB;QACjC;QACA,IACE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,EAAE,KACxD,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,GAAG,4KAAA,CAAA,oBAAiB,CAAC,KAAK,GAC5F;YACA,+BAA+B;YAC/B,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,oFAAoF;YACpF,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBAC1B,IAAI,CAAC,MAAM,CAAC,WAAW;YACzB;YACA,OAAO;QACT;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,EAAE,GAAG;YAC3C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YACzB,OAAO;QACT;QACA,IACE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,EAAE,KACpE,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,GAAG,4KAAA,CAAA,oBAAiB,CAAC,KAAK,GAC5F;YACA,sCAAsC;YACtC,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,oFAAoF;YACpF,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBAC1B,IAAI,CAAC,MAAM,CAAC,WAAW;YACzB;YACA,OAAO;QACT;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,GAAG;YACpC,OAAO,IAAI,CAAC,aAAa;QAC3B;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,QAAQ,GAAG;YACjD,OAAO,IAAI,CAAC,oBAAoB;QAClC;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YAC/C,OAAO,IAAI,CAAC,mBAAmB;QACjC;QACA,IACE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,KACxC,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,GAAG,4KAAA,CAAA,oBAAiB,CAAC,KAAK,GAC5F;YACA,mBAAmB;YACnB,wBAAwB;YACxB,8BAA8B;YAC9B,4BAA4B;YAC5B,kCAAkC;YAClC,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,IAAI,CAAC,MAAM,CAAC,WAAW;YACvB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBACnC,MAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAG;oBACvC,IAAI,CAAC,MAAM,CAAC,WAAW;gBACzB;gBACA,IAAI,CAAC,MAAM,CAAC,WAAW;YACzB,OAAO;gBACL,IAAI;gBACJ,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,GAAG,GAAG;oBAChC,KAAK;oBACL,IAAI,CAAC,MAAM,CAAC,WAAW;oBACvB,KAAK;oBACL,IAAI,CAAC,MAAM,CAAC,WAAW;gBACzB;YACF;YACA,2CAA2C;YAC3C,IACE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,4KAAA,CAAA,oBAAiB,CAAC,KAAK,KACrD,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,GAAG,yKAAA,CAAA,YAAE,CAAC,MAAM,GACrE;gBACA,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB,CAAA,GAAA,gLAAA,CAAA,8BAA2B,AAAD,EAAE,IAAI,CAAC,MAAM;YACzC;YACA,OAAO;QACT;QACA,OAAO;IACT;IAEC,sBAAsB;QACrB,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK;QAClF,IAAI,IAAI,CAAC,oCAAoC,CAAC,aAAa;YACzD,+DAA+D;YAC/D,CAAA,GAAA,sKAAA,CAAA,UAAiB,AAAD,EAAE,IAAI,CAAC,MAAM;QAC/B,OAAO,IAAI,IAAI,CAAC,mCAAmC,EAAE;YACnD,wEAAwE;YACxE,qDAAqD;YACrD,kBAAkB;YAClB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YACzB,MAAM;YACN,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,IAAI;YACJ,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,UAAU;YACV,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;QAC5D,OAAO;YACL,8CAA8C;YAC9C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAC3B;QACA,OAAO;IACT;IAEC,gBAAgB;QACf,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YAC/C,gDAAgD;YAChD,OAAO;QACT;QAEA,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ;QACrC,MAAM,oBAAoB,IAAI,CAAC,wBAAwB;QACvD,IAAI,mBAAmB;YACrB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;YAC9B,MAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAG;gBACvC,IAAI,CAAC,MAAM,CAAC,WAAW;YACzB;YACA,IAAI,CAAC,MAAM,CAAC,WAAW;YACvB,CAAA,GAAA,gLAAA,CAAA,8BAA2B,AAAD,EAAE,IAAI,CAAC,MAAM;YACvC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;gBACjC,IAAI,CAAC,MAAM,CAAC,WAAW;YACzB;QACF;QACA,OAAO;IACT;IAEA;;;;;GAKC,GACA,2BAA2B;QAC1B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO;QACxC,IACE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,4KAAA,CAAA,oBAAiB,CAAC,KAAK,KACrD,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,GAAG,yKAAA,CAAA,YAAE,CAAC,KAAK,KACrE,CAAC,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,GAAG,4KAAA,CAAA,oBAAiB,CAAC,KAAK,GAC7F;YACA,qDAAqD;YACrD,OAAO;QACT;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YACnC,+DAA+D;YAC/D,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,OAAO;QACT;QAEA,gDAAgD;QAChD,IACE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,4KAAA,CAAA,oBAAiB,CAAC,OAAO,KACvD,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,GAAG,4KAAA,CAAA,oBAAiB,CAAC,KAAK,GAC5F;YACA,IAAI,CAAC,MAAM,CAAC,SAAS;QACvB;QAEA,IAAI,qBAAqB;QACzB,IAAI,sBAAsB;QAC1B,IAAI,aAAa;QAEjB,yBAAyB;QACzB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;YACjC,IAAI,IAAI,CAAC,oCAAoC,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,KAAK;gBAC3E,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;oBAClC,IAAI,CAAC,MAAM,CAAC,WAAW;gBACzB;YACF,OAAO;gBACL,qBAAqB;gBACrB,IAAI,CAAC,MAAM,CAAC,SAAS;gBACrB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;oBAClC,6BAA6B;oBAC7B,+BAA+B;oBAC/B,KAAK;oBACL,8BAA8B;oBAC9B,sEAAsE;oBACtE,sEAAsE;oBACtE,oEAAoE;oBACpE,8CAA8C;oBAC9C,aAAa;oBACb,IAAI,CAAC,MAAM,CAAC,WAAW;gBACzB;YACF;QACF;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;YACjC,IAAI,IAAI,CAAC,oCAAoC,CAAC,IAAI,CAAC,MAAM,CAAC,6BAA6B,CAAC,KAAK;gBAC3F,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB,IAAI,CAAC,MAAM,CAAC,WAAW;YACzB,OAAO;gBACL,IAAI,YAAY;oBACd,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;gBACzB;gBACA,qBAAqB;gBACrB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI;gBACrC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI;gBACrC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI;YACvC;QACF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YAC1C,IAAI,YAAY;gBACd,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACzB;YACA,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,MAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAG;gBACvC,sBAAsB;gBACtB,MAAM,gBAAgB,CAAA,GAAA,iLAAA,CAAA,UAA4B,AAAD,EAAE,IAAI,CAAC,MAAM;gBAC9D,IACE,cAAc,MAAM,IACpB,IAAI,CAAC,oCAAoC,CAAC,cAAc,SAAS,GACjE;oBACA,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,cAAc,QAAQ,CAAE;wBAC1D,IAAI,CAAC,MAAM,CAAC,WAAW;oBACzB;oBACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;wBAClC,IAAI,CAAC,MAAM,CAAC,WAAW;oBACzB;gBACF,OAAO;oBACL,qBAAqB;oBACrB,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,cAAc,QAAQ,CAAE;wBAC1D,IAAI,CAAC,MAAM,CAAC,SAAS;oBACvB;oBACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;wBAClC,IAAI,CAAC,MAAM,CAAC,SAAS;oBACvB;gBACF;YACF;YACA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;QACzC;QAEA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,OAAO;QACT;QACA,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACrC,OAAO,CAAC;QACV,OAAO,IAAI,IAAI,CAAC,sBAAsB,EAAE;YACtC,oEAAoE;YACpE,OAAO,uBAAuB,CAAC;QACjC,OAAO;YACL,OAAO;QACT;IACF;IAEC,qCAAqC,IAAI,EAAE;QAC1C,OACE,IAAI,CAAC,4BAA4B,IACjC,CAAC,IAAI,CAAC,iBAAiB,IACvB,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;IAEjC;IAEC,uBAAuB;QACtB,IACE,CAAA,GAAA,6KAAA,CAAA,UAAwB,AAAD,EACrB,IAAI,CAAC,4BAA4B,EACjC,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,eAAe,GAEtB;YACA,iFAAiF;YACjF,mFAAmF;YACnF,8BAA8B;YAC9B,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,IAAI,CAAC,MAAM,CAAC,WAAW;YACvB,IAAI,CAAC,MAAM,CAAC,WAAW;YACvB,OAAO;QACT;QAEA,MAAM,iBACJ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,QAAQ,EAAE,yKAAA,CAAA,YAAE,CAAC,SAAS,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,KAElE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,QAAQ,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,SAAS,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,KAC3E,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAClC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,GAC7B,4KAAA,CAAA,oBAAiB,CAAC,MAAM,KAE5B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,QAAQ,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,KAChE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,QAAQ,EAAE,yKAAA,CAAA,YAAE,CAAC,SAAS,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI;QAEhF,IAAI,CAAC,kBAAkB,IAAI,CAAC,yBAAyB,EAAE;YACrD,sFAAsF;YACtF,2EAA2E;YAC3E,MAAM,iBAAiB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;YACtD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,eAAe,QAAQ,CAAC;YACxD,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,eAAe,EAAE,CAAC;YAC7C,IAAI,CAAC,yBAAyB,CAAC,6BAA6B,CAAC;YAC7D,OAAO;QACT;QACA,OAAO;IACT;IAEA;;;;;;;;;GASC,GACA,sBAAsB;QACrB,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE;YACtC,OAAO;QACT;QACA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO;QACxC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;QAEvC,MAAM,aAAa,CAAA,GAAA,iKAAA,CAAA,UAAY,AAAD,EAAE,IAAI,CAAC,MAAM;QAC3C,IAAI,qBAAqB;QACzB,MAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAG;YACvC,MAAM,gBAAgB,CAAA,GAAA,iLAAA,CAAA,UAA4B,AAAD,EAAE,IAAI,CAAC,MAAM;YAC9D,IACE,cAAc,MAAM,IACnB,CAAC,cAAc,IAAI,CAAC,uBAAuB,CAAC,cAAc,QAAQ,GACnE;gBACA,0DAA0D;gBAC1D,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,cAAc,QAAQ,CAAE;oBAC1D,IAAI,CAAC,MAAM,CAAC,WAAW;gBACzB;gBACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;oBAClC,IAAI,CAAC,MAAM,CAAC,WAAW;gBACzB;YACF,OAAO;gBACL,4DAA4D;gBAC5D,qBAAqB;gBACrB,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,cAAc,QAAQ,CAAE;oBAC1D,IAAI,CAAC,MAAM,CAAC,SAAS;gBACvB;gBACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;oBAClC,IAAI,CAAC,MAAM,CAAC,SAAS;gBACvB;YACF;QACF;QACA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;QAEvC,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,cAAc,CAAC,oBAAoB;YAChE,uFAAuF;YACvF,2EAA2E;YAC3E,IAAI,CAAC,MAAM,CAAC,WAAW;YACvB,IAAI,CAAC,MAAM,CAAC,WAAW;YACvB,CAAA,GAAA,gLAAA,CAAA,8BAA2B,AAAD,EAAE,IAAI,CAAC,MAAM;QACzC;QAEA,OAAO;IACT;IAEA;;;;GAIC,GACA,wBAAwB,IAAI,EAAE;QAC7B,OACE,IAAI,CAAC,4BAA4B,IACjC,CAAC,IAAI,CAAC,iBAAiB,IACvB,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAC1C,CAAC,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAC;IAEhD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1826, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/transformers/FlowTransformer.js"], "sourcesContent": ["import {ContextualKeyword} from \"../parser/tokenizer/keywords\";\nimport {TokenType as tt} from \"../parser/tokenizer/types\";\n\n\nimport Transformer from \"./Transformer\";\n\nexport default class FlowTransformer extends Transformer {\n  constructor(\n     rootTransformer,\n     tokens,\n     isImportsTransformEnabled,\n  ) {\n    super();this.rootTransformer = rootTransformer;this.tokens = tokens;this.isImportsTransformEnabled = isImportsTransformEnabled;;\n  }\n\n  process() {\n    if (\n      this.rootTransformer.processPossibleArrowParamEnd() ||\n      this.rootTransformer.processPossibleAsyncArrowWithTypeParams() ||\n      this.rootTransformer.processPossibleTypeRange()\n    ) {\n      return true;\n    }\n    if (this.tokens.matches1(tt._enum)) {\n      this.processEnum();\n      return true;\n    }\n    if (this.tokens.matches2(tt._export, tt._enum)) {\n      this.processNamedExportEnum();\n      return true;\n    }\n    if (this.tokens.matches3(tt._export, tt._default, tt._enum)) {\n      this.processDefaultExportEnum();\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Handle a declaration like:\n   * export enum E ...\n   *\n   * With this imports transform, this becomes:\n   * const E = [[enum]]; exports.E = E;\n   *\n   * otherwise, it becomes:\n   * export const E = [[enum]];\n   */\n  processNamedExportEnum() {\n    if (this.isImportsTransformEnabled) {\n      // export\n      this.tokens.removeInitialToken();\n      const enumName = this.tokens.identifierNameAtRelativeIndex(1);\n      this.processEnum();\n      this.tokens.appendCode(` exports.${enumName} = ${enumName};`);\n    } else {\n      this.tokens.copyToken();\n      this.processEnum();\n    }\n  }\n\n  /**\n   * Handle a declaration like:\n   * export default enum E\n   *\n   * With the imports transform, this becomes:\n   * const E = [[enum]]; exports.default = E;\n   *\n   * otherwise, it becomes:\n   * const E = [[enum]]; export default E;\n   */\n  processDefaultExportEnum() {\n    // export\n    this.tokens.removeInitialToken();\n    // default\n    this.tokens.removeToken();\n    const enumName = this.tokens.identifierNameAtRelativeIndex(1);\n    this.processEnum();\n    if (this.isImportsTransformEnabled) {\n      this.tokens.appendCode(` exports.default = ${enumName};`);\n    } else {\n      this.tokens.appendCode(` export default ${enumName};`);\n    }\n  }\n\n  /**\n   * Transpile flow enums to invoke the \"flow-enums-runtime\" library.\n   *\n   * Currently, the transpiled code always uses `require(\"flow-enums-runtime\")`,\n   * but if future flexibility is needed, we could expose a config option for\n   * this string (similar to configurable JSX). Even when targeting ESM, the\n   * default behavior of babel-plugin-transform-flow-enums is to use require\n   * rather than injecting an import.\n   *\n   * Flow enums are quite a bit simpler than TS enums and have some convenient\n   * constraints:\n   * - Element initializers must be either always present or always absent. That\n   *   means that we can use fixed lookahead on the first element (if any) and\n   *   assume that all elements are like that.\n   * - The right-hand side of an element initializer must be a literal value,\n   *   not a complex expression and not referencing other elements. That means\n   *   we can simply copy a single token.\n   *\n   * Enums can be broken up into three basic cases:\n   *\n   * Mirrored enums:\n   * enum E {A, B}\n   *   ->\n   * const E = require(\"flow-enums-runtime\").Mirrored([\"A\", \"B\"]);\n   *\n   * Initializer enums:\n   * enum E {A = 1, B = 2}\n   *   ->\n   * const E = require(\"flow-enums-runtime\")({A: 1, B: 2});\n   *\n   * Symbol enums:\n   * enum E of symbol {A, B}\n   *   ->\n   * const E = require(\"flow-enums-runtime\")({A: Symbol(\"A\"), B: Symbol(\"B\")});\n   *\n   * We can statically detect which of the three cases this is by looking at the\n   * \"of\" declaration (if any) and seeing if the first element has an initializer.\n   * Since the other transform details are so similar between the three cases, we\n   * use a single implementation and vary the transform within processEnumElement\n   * based on case.\n   */\n  processEnum() {\n    // enum E -> const E\n    this.tokens.replaceToken(\"const\");\n    this.tokens.copyExpectedToken(tt.name);\n\n    let isSymbolEnum = false;\n    if (this.tokens.matchesContextual(ContextualKeyword._of)) {\n      this.tokens.removeToken();\n      isSymbolEnum = this.tokens.matchesContextual(ContextualKeyword._symbol);\n      this.tokens.removeToken();\n    }\n    const hasInitializers = this.tokens.matches3(tt.braceL, tt.name, tt.eq);\n    this.tokens.appendCode(' = require(\"flow-enums-runtime\")');\n\n    const isMirrored = !isSymbolEnum && !hasInitializers;\n    this.tokens.replaceTokenTrimmingLeftWhitespace(isMirrored ? \".Mirrored([\" : \"({\");\n\n    while (!this.tokens.matches1(tt.braceR)) {\n      // ... is allowed at the end and has no runtime behavior.\n      if (this.tokens.matches1(tt.ellipsis)) {\n        this.tokens.removeToken();\n        break;\n      }\n      this.processEnumElement(isSymbolEnum, hasInitializers);\n      if (this.tokens.matches1(tt.comma)) {\n        this.tokens.copyToken();\n      }\n    }\n\n    this.tokens.replaceToken(isMirrored ? \"]);\" : \"});\");\n  }\n\n  /**\n   * Process an individual enum element, producing either an array element or an\n   * object element based on what type of enum this is.\n   */\n  processEnumElement(isSymbolEnum, hasInitializers) {\n    if (isSymbolEnum) {\n      // Symbol enums never have initializers and are expanded to object elements.\n      // A, -> A: Symbol(\"A\"),\n      const elementName = this.tokens.identifierName();\n      this.tokens.copyToken();\n      this.tokens.appendCode(`: Symbol(\"${elementName}\")`);\n    } else if (hasInitializers) {\n      // Initializers are expanded to object elements.\n      // A = 1, -> A: 1,\n      this.tokens.copyToken();\n      this.tokens.replaceTokenTrimmingLeftWhitespace(\":\");\n      this.tokens.copyToken();\n    } else {\n      // Enum elements without initializers become string literal array elements.\n      // A, -> \"A\",\n      this.tokens.replaceToken(`\"${this.tokens.identifierName()}\"`);\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAGA;;;;AAEe,MAAM,wBAAwB,wKAAA,CAAA,UAAW;IACtD,YACG,eAAe,EACf,MAAM,EACN,yBAAyB,CAC1B;QACA,KAAK;QAAG,IAAI,CAAC,eAAe,GAAG;QAAgB,IAAI,CAAC,MAAM,GAAG;QAAO,IAAI,CAAC,yBAAyB,GAAG;;IACvG;IAEA,UAAU;QACR,IACE,IAAI,CAAC,eAAe,CAAC,4BAA4B,MACjD,IAAI,CAAC,eAAe,CAAC,uCAAuC,MAC5D,IAAI,CAAC,eAAe,CAAC,wBAAwB,IAC7C;YACA,OAAO;QACT;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;YAClC,IAAI,CAAC,WAAW;YAChB,OAAO;QACT;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;YAC9C,IAAI,CAAC,sBAAsB;YAC3B,OAAO;QACT;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,QAAQ,EAAE,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;YAC3D,IAAI,CAAC,wBAAwB;YAC7B,OAAO;QACT;QACA,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,yBAAyB;QACvB,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAClC,SAAS;YACT,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,6BAA6B,CAAC;YAC3D,IAAI,CAAC,WAAW;YAChB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,EAAE,SAAS,GAAG,EAAE,SAAS,CAAC,CAAC;QAC9D,OAAO;YACL,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,IAAI,CAAC,WAAW;QAClB;IACF;IAEA;;;;;;;;;GASC,GACD,2BAA2B;QACzB,SAAS;QACT,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC9B,UAAU;QACV,IAAI,CAAC,MAAM,CAAC,WAAW;QACvB,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,6BAA6B,CAAC;QAC3D,IAAI,CAAC,WAAW;QAChB,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;QAC1D,OAAO;YACL,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;QACvD;IACF;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwCC,GACD,cAAc;QACZ,oBAAoB;QACpB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI;QAErC,IAAI,eAAe;QACnB,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,4KAAA,CAAA,oBAAiB,CAAC,GAAG,GAAG;YACxD,IAAI,CAAC,MAAM,CAAC,WAAW;YACvB,eAAe,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,4KAAA,CAAA,oBAAiB,CAAC,OAAO;YACtE,IAAI,CAAC,MAAM,CAAC,WAAW;QACzB;QACA,MAAM,kBAAkB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,EAAE;QACtE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QAEvB,MAAM,aAAa,CAAC,gBAAgB,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC,aAAa,gBAAgB;QAE5E,MAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAG;YACvC,yDAAyD;YACzD,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,QAAQ,GAAG;gBACrC,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB;YACF;YACA,IAAI,CAAC,kBAAkB,CAAC,cAAc;YACtC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;gBAClC,IAAI,CAAC,MAAM,CAAC,SAAS;YACvB;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,QAAQ;IAChD;IAEA;;;GAGC,GACD,mBAAmB,YAAY,EAAE,eAAe,EAAE;QAChD,IAAI,cAAc;YAChB,4EAA4E;YAC5E,wBAAwB;YACxB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,cAAc;YAC9C,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,UAAU,EAAE,YAAY,EAAE,CAAC;QACrD,OAAO,IAAI,iBAAiB;YAC1B,gDAAgD;YAChD,kBAAkB;YAClB,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC;YAC/C,IAAI,CAAC,MAAM,CAAC,SAAS;QACvB,OAAO;YACL,2EAA2E;YAC3E,aAAa;YACb,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,CAAC,CAAC;QAC9D;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2000, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/transformers/JestHoistTransformer.js"], "sourcesContent": [" function _optionalChain(ops) { let lastAccessLHS = undefined; let value = ops[0]; let i = 1; while (i < ops.length) { const op = ops[i]; const fn = ops[i + 1]; i += 2; if ((op === 'optionalAccess' || op === 'optionalCall') && value == null) { return undefined; } if (op === 'access' || op === 'optionalAccess') { lastAccessLHS = value; value = fn(value); } else if (op === 'call' || op === 'optionalCall') { value = fn((...args) => value.call(lastAccessLHS, ...args)); lastAccessLHS = undefined; } } return value; }\n\nimport {TokenType as tt} from \"../parser/tokenizer/types\";\n\n\nimport Transformer from \"./Transformer\";\n\nconst JEST_GLOBAL_NAME = \"jest\";\nconst HOISTED_METHODS = [\"mock\", \"unmock\", \"enableAutomock\", \"disableAutomock\"];\n\n/**\n * Implementation of babel-plugin-jest-hoist, which hoists up some jest method\n * calls above the imports to allow them to override other imports.\n *\n * To preserve line numbers, rather than directly moving the jest.mock code, we\n * wrap each invocation in a function statement and then call the function from\n * the top of the file.\n */\nexport default class JestHoistTransformer extends Transformer {\n    __init() {this.hoistedFunctionNames = []}\n\n  constructor(\n     rootTransformer,\n     tokens,\n     nameManager,\n     importProcessor,\n  ) {\n    super();this.rootTransformer = rootTransformer;this.tokens = tokens;this.nameManager = nameManager;this.importProcessor = importProcessor;JestHoistTransformer.prototype.__init.call(this);;\n  }\n\n  process() {\n    if (\n      this.tokens.currentToken().scopeDepth === 0 &&\n      this.tokens.matches4(tt.name, tt.dot, tt.name, tt.parenL) &&\n      this.tokens.identifierName() === JEST_GLOBAL_NAME\n    ) {\n      // TODO: This only works if imports transform is active, which it will be for jest.\n      //       But if jest adds module support and we no longer need the import transform, this needs fixing.\n      if (_optionalChain([this, 'access', _ => _.importProcessor, 'optionalAccess', _2 => _2.getGlobalNames, 'call', _3 => _3(), 'optionalAccess', _4 => _4.has, 'call', _5 => _5(JEST_GLOBAL_NAME)])) {\n        return false;\n      }\n      return this.extractHoistedCalls();\n    }\n\n    return false;\n  }\n\n  getHoistedCode() {\n    if (this.hoistedFunctionNames.length > 0) {\n      // This will be placed before module interop code, but that's fine since\n      // imports aren't allowed in module mock factories.\n      return this.hoistedFunctionNames.map((name) => `${name}();`).join(\"\");\n    }\n    return \"\";\n  }\n\n  /**\n   * Extracts any methods calls on the jest-object that should be hoisted.\n   *\n   * According to the jest docs, https://jestjs.io/docs/en/jest-object#jestmockmodulename-factory-options,\n   * mock, unmock, enableAutomock, disableAutomock, are the methods that should be hoisted.\n   *\n   * We do not apply the same checks of the arguments as babel-plugin-jest-hoist does.\n   */\n   extractHoistedCalls() {\n    // We're handling a chain of calls where `jest` may or may not need to be inserted for each call\n    // in the chain, so remove the initial `jest` to make the loop implementation cleaner.\n    this.tokens.removeToken();\n    // Track some state so that multiple non-hoisted chained calls in a row keep their chaining\n    // syntax.\n    let followsNonHoistedJestCall = false;\n\n    // Iterate through all chained calls on the jest object.\n    while (this.tokens.matches3(tt.dot, tt.name, tt.parenL)) {\n      const methodName = this.tokens.identifierNameAtIndex(this.tokens.currentIndex() + 1);\n      const shouldHoist = HOISTED_METHODS.includes(methodName);\n      if (shouldHoist) {\n        // We've matched e.g. `.mock(...)` or similar call.\n        // Replace the initial `.` with `function __jestHoist(){jest.`\n        const hoistedFunctionName = this.nameManager.claimFreeName(\"__jestHoist\");\n        this.hoistedFunctionNames.push(hoistedFunctionName);\n        this.tokens.replaceToken(`function ${hoistedFunctionName}(){${JEST_GLOBAL_NAME}.`);\n        this.tokens.copyToken();\n        this.tokens.copyToken();\n        this.rootTransformer.processBalancedCode();\n        this.tokens.copyExpectedToken(tt.parenR);\n        this.tokens.appendCode(\";}\");\n        followsNonHoistedJestCall = false;\n      } else {\n        // This is a non-hoisted method, so just transform the code as usual.\n        if (followsNonHoistedJestCall) {\n          // If we didn't hoist the previous call, we can leave the code as-is to chain off of the\n          // previous method call. It's important to preserve the code here because we don't know\n          // for sure that the method actually returned the jest object for chaining.\n          this.tokens.copyToken();\n        } else {\n          // If we hoisted the previous call, we know it returns the jest object back, so we insert\n          // the identifier `jest` to continue the chain.\n          this.tokens.replaceToken(`${JEST_GLOBAL_NAME}.`);\n        }\n        this.tokens.copyToken();\n        this.tokens.copyToken();\n        this.rootTransformer.processBalancedCode();\n        this.tokens.copyExpectedToken(tt.parenR);\n        followsNonHoistedJestCall = true;\n      }\n    }\n\n    return true;\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAGA;AALC,SAAS,eAAe,GAAG;IAAI,IAAI,gBAAgB;IAAW,IAAI,QAAQ,GAAG,CAAC,EAAE;IAAE,IAAI,IAAI;IAAG,MAAO,IAAI,IAAI,MAAM,CAAE;QAAE,MAAM,KAAK,GAAG,CAAC,EAAE;QAAE,MAAM,KAAK,GAAG,CAAC,IAAI,EAAE;QAAE,KAAK;QAAG,IAAI,CAAC,OAAO,oBAAoB,OAAO,cAAc,KAAK,SAAS,MAAM;YAAE,OAAO;QAAW;QAAE,IAAI,OAAO,YAAY,OAAO,kBAAkB;YAAE,gBAAgB;YAAO,QAAQ,GAAG;QAAQ,OAAO,IAAI,OAAO,UAAU,OAAO,gBAAgB;YAAE,QAAQ,GAAG,CAAC,GAAG,OAAS,MAAM,IAAI,CAAC,kBAAkB;YAAQ,gBAAgB;QAAW;IAAE;IAAE,OAAO;AAAO;;;AAOngB,MAAM,mBAAmB;AACzB,MAAM,kBAAkB;IAAC;IAAQ;IAAU;IAAkB;CAAkB;AAUhE,MAAM,6BAA6B,wKAAA,CAAA,UAAW;IACzD,SAAS;QAAC,IAAI,CAAC,oBAAoB,GAAG,EAAE;IAAA;IAE1C,YACG,eAAe,EACf,MAAM,EACN,WAAW,EACX,eAAe,CAChB;QACA,KAAK;QAAG,IAAI,CAAC,eAAe,GAAG;QAAgB,IAAI,CAAC,MAAM,GAAG;QAAO,IAAI,CAAC,WAAW,GAAG;QAAY,IAAI,CAAC,eAAe,GAAG;QAAgB,qBAAqB,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;;IAC3L;IAEA,UAAU;QACR,IACE,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,UAAU,KAAK,KAC1C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,GAAG,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,KACxD,IAAI,CAAC,MAAM,CAAC,cAAc,OAAO,kBACjC;YACA,mFAAmF;YACnF,uGAAuG;YACvG,IAAI,eAAe;gBAAC,IAAI;gBAAE;gBAAU,CAAA,IAAK,EAAE,eAAe;gBAAE;gBAAkB,CAAA,KAAM,GAAG,cAAc;gBAAE;gBAAQ,CAAA,KAAM;gBAAM;gBAAkB,CAAA,KAAM,GAAG,GAAG;gBAAE;gBAAQ,CAAA,KAAM,GAAG;aAAkB,GAAG;gBAC/L,OAAO;YACT;YACA,OAAO,IAAI,CAAC,mBAAmB;QACjC;QAEA,OAAO;IACT;IAEA,iBAAiB;QACf,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,GAAG;YACxC,wEAAwE;YACxE,mDAAmD;YACnD,OAAO,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,OAAS,GAAG,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC;QACpE;QACA,OAAO;IACT;IAEA;;;;;;;GAOC,GACA,sBAAsB;QACrB,gGAAgG;QAChG,sFAAsF;QACtF,IAAI,CAAC,MAAM,CAAC,WAAW;QACvB,2FAA2F;QAC3F,UAAU;QACV,IAAI,4BAA4B;QAEhC,wDAAwD;QACxD,MAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,GAAG,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAG;YACvD,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK;YAClF,MAAM,cAAc,gBAAgB,QAAQ,CAAC;YAC7C,IAAI,aAAa;gBACf,mDAAmD;gBACnD,8DAA8D;gBAC9D,MAAM,sBAAsB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;gBAC3D,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;gBAC/B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,EAAE,oBAAoB,GAAG,EAAE,iBAAiB,CAAC,CAAC;gBACjF,IAAI,CAAC,MAAM,CAAC,SAAS;gBACrB,IAAI,CAAC,MAAM,CAAC,SAAS;gBACrB,IAAI,CAAC,eAAe,CAAC,mBAAmB;gBACxC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;gBACvC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;gBACvB,4BAA4B;YAC9B,OAAO;gBACL,qEAAqE;gBACrE,IAAI,2BAA2B;oBAC7B,wFAAwF;oBACxF,uFAAuF;oBACvF,2EAA2E;oBAC3E,IAAI,CAAC,MAAM,CAAC,SAAS;gBACvB,OAAO;oBACL,yFAAyF;oBACzF,+CAA+C;oBAC/C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,iBAAiB,CAAC,CAAC;gBACjD;gBACA,IAAI,CAAC,MAAM,CAAC,SAAS;gBACrB,IAAI,CAAC,MAAM,CAAC,SAAS;gBACrB,IAAI,CAAC,eAAe,CAAC,mBAAmB;gBACxC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;gBACvC,4BAA4B;YAC9B;QACF;QAEA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/transformers/NumericSeparatorTransformer.js"], "sourcesContent": ["import {TokenType as tt} from \"../parser/tokenizer/types\";\n\nimport Transformer from \"./Transformer\";\n\nexport default class NumericSeparatorTransformer extends Transformer {\n  constructor( tokens) {\n    super();this.tokens = tokens;;\n  }\n\n  process() {\n    if (this.tokens.matches1(tt.num)) {\n      const code = this.tokens.currentTokenCode();\n      if (code.includes(\"_\")) {\n        this.tokens.replaceToken(code.replace(/_/g, \"\"));\n        return true;\n      }\n    }\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEe,MAAM,oCAAoC,wKAAA,CAAA,UAAW;IAClE,YAAa,MAAM,CAAE;QACnB,KAAK;QAAG,IAAI,CAAC,MAAM,GAAG;;IACxB;IAEA,UAAU;QACR,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,GAAG,GAAG;YAChC,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB;YACzC,IAAI,KAAK,QAAQ,CAAC,MAAM;gBACtB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,OAAO,CAAC,MAAM;gBAC5C,OAAO;YACT;QACF;QACA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/transformers/OptionalCatchBindingTransformer.js"], "sourcesContent": ["\nimport {TokenType as tt} from \"../parser/tokenizer/types\";\n\nimport Transformer from \"./Transformer\";\n\nexport default class OptionalCatchBindingTransformer extends Transformer {\n  constructor( tokens,  nameManager) {\n    super();this.tokens = tokens;this.nameManager = nameManager;;\n  }\n\n  process() {\n    if (this.tokens.matches2(tt._catch, tt.braceL)) {\n      this.tokens.copyToken();\n      this.tokens.appendCode(` (${this.nameManager.claimFreeName(\"e\")})`);\n      return true;\n    }\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AAEA;;;AAEe,MAAM,wCAAwC,wKAAA,CAAA,UAAW;IACtE,YAAa,MAAM,EAAG,WAAW,CAAE;QACjC,KAAK;QAAG,IAAI,CAAC,MAAM,GAAG;QAAO,IAAI,CAAC,WAAW,GAAG;;IAClD;IAEA,UAAU;QACR,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YAC9C,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAClE,OAAO;QACT;QACA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/transformers/OptionalChainingNullishTransformer.js"], "sourcesContent": ["\nimport {TokenType as tt} from \"../parser/tokenizer/types\";\n\nimport Transformer from \"./Transformer\";\n\n/**\n * Transformer supporting the optional chaining and nullish coalescing operators.\n *\n * Tech plan here:\n * https://github.com/alangpierce/sucrase/wiki/Sucrase-Optional-Chaining-and-Nullish-Coalescing-Technical-Plan\n *\n * The prefix and suffix code snippets are handled by TokenProcessor, and this transformer handles\n * the operators themselves.\n */\nexport default class OptionalChainingNullishTransformer extends Transformer {\n  constructor( tokens,  nameManager) {\n    super();this.tokens = tokens;this.nameManager = nameManager;;\n  }\n\n  process() {\n    if (this.tokens.matches1(tt.nullishCoalescing)) {\n      const token = this.tokens.currentToken();\n      if (this.tokens.tokens[token.nullishStartIndex].isAsyncOperation) {\n        this.tokens.replaceTokenTrimmingLeftWhitespace(\", async () => (\");\n      } else {\n        this.tokens.replaceTokenTrimmingLeftWhitespace(\", () => (\");\n      }\n      return true;\n    }\n    if (this.tokens.matches1(tt._delete)) {\n      const nextToken = this.tokens.tokenAtRelativeIndex(1);\n      if (nextToken.isOptionalChainStart) {\n        this.tokens.removeInitialToken();\n        return true;\n      }\n    }\n    const token = this.tokens.currentToken();\n    const chainStart = token.subscriptStartIndex;\n    if (\n      chainStart != null &&\n      this.tokens.tokens[chainStart].isOptionalChainStart &&\n      // Super subscripts can't be optional (since super is never null/undefined), and the syntax\n      // relies on the subscript being intact, so leave this token alone.\n      this.tokens.tokenAtRelativeIndex(-1).type !== tt._super\n    ) {\n      const param = this.nameManager.claimFreeName(\"_\");\n      let arrowStartSnippet;\n      if (\n        chainStart > 0 &&\n        this.tokens.matches1AtIndex(chainStart - 1, tt._delete) &&\n        this.isLastSubscriptInChain()\n      ) {\n        // Delete operations are special: we already removed the delete keyword, and to still\n        // perform a delete, we need to insert a delete in the very last part of the chain, which\n        // in correct code will always be a property access.\n        arrowStartSnippet = `${param} => delete ${param}`;\n      } else {\n        arrowStartSnippet = `${param} => ${param}`;\n      }\n      if (this.tokens.tokens[chainStart].isAsyncOperation) {\n        arrowStartSnippet = `async ${arrowStartSnippet}`;\n      }\n      if (\n        this.tokens.matches2(tt.questionDot, tt.parenL) ||\n        this.tokens.matches2(tt.questionDot, tt.lessThan)\n      ) {\n        if (this.justSkippedSuper()) {\n          this.tokens.appendCode(\".bind(this)\");\n        }\n        this.tokens.replaceTokenTrimmingLeftWhitespace(`, 'optionalCall', ${arrowStartSnippet}`);\n      } else if (this.tokens.matches2(tt.questionDot, tt.bracketL)) {\n        this.tokens.replaceTokenTrimmingLeftWhitespace(`, 'optionalAccess', ${arrowStartSnippet}`);\n      } else if (this.tokens.matches1(tt.questionDot)) {\n        this.tokens.replaceTokenTrimmingLeftWhitespace(`, 'optionalAccess', ${arrowStartSnippet}.`);\n      } else if (this.tokens.matches1(tt.dot)) {\n        this.tokens.replaceTokenTrimmingLeftWhitespace(`, 'access', ${arrowStartSnippet}.`);\n      } else if (this.tokens.matches1(tt.bracketL)) {\n        this.tokens.replaceTokenTrimmingLeftWhitespace(`, 'access', ${arrowStartSnippet}[`);\n      } else if (this.tokens.matches1(tt.parenL)) {\n        if (this.justSkippedSuper()) {\n          this.tokens.appendCode(\".bind(this)\");\n        }\n        this.tokens.replaceTokenTrimmingLeftWhitespace(`, 'call', ${arrowStartSnippet}(`);\n      } else {\n        throw new Error(\"Unexpected subscript operator in optional chain.\");\n      }\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Determine if the current token is the last of its chain, so that we know whether it's eligible\n   * to have a delete op inserted.\n   *\n   * We can do this by walking forward until we determine one way or another. Each\n   * isOptionalChainStart token must be paired with exactly one isOptionalChainEnd token after it in\n   * a nesting way, so we can track depth and walk to the end of the chain (the point where the\n   * depth goes negative) and see if any other subscript token is after us in the chain.\n   */\n  isLastSubscriptInChain() {\n    let depth = 0;\n    for (let i = this.tokens.currentIndex() + 1; ; i++) {\n      if (i >= this.tokens.tokens.length) {\n        throw new Error(\"Reached the end of the code while finding the end of the access chain.\");\n      }\n      if (this.tokens.tokens[i].isOptionalChainStart) {\n        depth++;\n      } else if (this.tokens.tokens[i].isOptionalChainEnd) {\n        depth--;\n      }\n      if (depth < 0) {\n        return true;\n      }\n\n      // This subscript token is a later one in the same chain.\n      if (depth === 0 && this.tokens.tokens[i].subscriptStartIndex != null) {\n        return false;\n      }\n    }\n  }\n\n  /**\n   * Determine if we are the open-paren in an expression like super.a()?.b.\n   *\n   * We can do this by walking backward to find the previous subscript. If that subscript was\n   * preceded by a super, then we must be the subscript after it, so if this is a call expression,\n   * we'll need to attach the right context.\n   */\n  justSkippedSuper() {\n    let depth = 0;\n    let index = this.tokens.currentIndex() - 1;\n    while (true) {\n      if (index < 0) {\n        throw new Error(\n          \"Reached the start of the code while finding the start of the access chain.\",\n        );\n      }\n      if (this.tokens.tokens[index].isOptionalChainStart) {\n        depth--;\n      } else if (this.tokens.tokens[index].isOptionalChainEnd) {\n        depth++;\n      }\n      if (depth < 0) {\n        return false;\n      }\n\n      // This subscript token is a later one in the same chain.\n      if (depth === 0 && this.tokens.tokens[index].subscriptStartIndex != null) {\n        return this.tokens.tokens[index - 1].type === tt._super;\n      }\n      index--;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AAEA;;;AAWe,MAAM,2CAA2C,wKAAA,CAAA,UAAW;IACzE,YAAa,MAAM,EAAG,WAAW,CAAE;QACjC,KAAK;QAAG,IAAI,CAAC,MAAM,GAAG;QAAO,IAAI,CAAC,WAAW,GAAG;;IAClD;IAEA,UAAU;QACR,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,iBAAiB,GAAG;YAC9C,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,YAAY;YACtC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,iBAAiB,CAAC,CAAC,gBAAgB,EAAE;gBAChE,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC;YACjD,OAAO;gBACL,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC;YACjD;YACA,OAAO;QACT;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,GAAG;YACpC,MAAM,YAAY,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC;YACnD,IAAI,UAAU,oBAAoB,EAAE;gBAClC,IAAI,CAAC,MAAM,CAAC,kBAAkB;gBAC9B,OAAO;YACT;QACF;QACA,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,YAAY;QACtC,MAAM,aAAa,MAAM,mBAAmB;QAC5C,IACE,cAAc,QACd,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,oBAAoB,IACnD,2FAA2F;QAC3F,mEAAmE;QACnE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,GAAG,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,MAAM,EACvD;YACA,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;YAC7C,IAAI;YACJ,IACE,aAAa,KACb,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,GAAG,yKAAA,CAAA,YAAE,CAAC,OAAO,KACtD,IAAI,CAAC,sBAAsB,IAC3B;gBACA,qFAAqF;gBACrF,yFAAyF;gBACzF,oDAAoD;gBACpD,oBAAoB,GAAG,MAAM,WAAW,EAAE,OAAO;YACnD,OAAO;gBACL,oBAAoB,GAAG,MAAM,IAAI,EAAE,OAAO;YAC5C;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,EAAE;gBACnD,oBAAoB,CAAC,MAAM,EAAE,mBAAmB;YAClD;YACA,IACE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,WAAW,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,KAC9C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,WAAW,EAAE,yKAAA,CAAA,YAAE,CAAC,QAAQ,GAChD;gBACA,IAAI,IAAI,CAAC,gBAAgB,IAAI;oBAC3B,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;gBACzB;gBACA,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC,CAAC,kBAAkB,EAAE,mBAAmB;YACzF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,WAAW,EAAE,yKAAA,CAAA,YAAE,CAAC,QAAQ,GAAG;gBAC5D,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC,CAAC,oBAAoB,EAAE,mBAAmB;YAC3F,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,WAAW,GAAG;gBAC/C,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;YAC5F,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,GAAG,GAAG;gBACvC,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAAC;YACpF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,QAAQ,GAAG;gBAC5C,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAAC;YACpF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBAC1C,IAAI,IAAI,CAAC,gBAAgB,IAAI;oBAC3B,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;gBACzB;gBACA,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;YAClF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YACA,OAAO;QACT;QACA,OAAO;IACT;IAEA;;;;;;;;GAQC,GACD,yBAAyB;QACvB,IAAI,QAAQ;QACZ,IAAK,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,IAAK,IAAK;YAClD,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;gBAClC,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,oBAAoB,EAAE;gBAC9C;YACF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,kBAAkB,EAAE;gBACnD;YACF;YACA,IAAI,QAAQ,GAAG;gBACb,OAAO;YACT;YAEA,yDAAyD;YACzD,IAAI,UAAU,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,mBAAmB,IAAI,MAAM;gBACpE,OAAO;YACT;QACF;IACF;IAEA;;;;;;GAMC,GACD,mBAAmB;QACjB,IAAI,QAAQ;QACZ,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK;QACzC,MAAO,KAAM;YACX,IAAI,QAAQ,GAAG;gBACb,MAAM,IAAI,MACR;YAEJ;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,oBAAoB,EAAE;gBAClD;YACF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE;gBACvD;YACF;YACA,IAAI,QAAQ,GAAG;gBACb,OAAO;YACT;YAEA,yDAAyD;YACzD,IAAI,UAAU,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,mBAAmB,IAAI,MAAM;gBACxE,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,MAAM;YACzD;YACA;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2333, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/transformers/ReactDisplayNameTransformer.js"], "sourcesContent": ["\n\nimport {IdentifierRole} from \"../parser/tokenizer\";\nimport {TokenType as tt} from \"../parser/tokenizer/types\";\n\n\nimport Transformer from \"./Transformer\";\n\n/**\n * Implementation of babel-plugin-transform-react-display-name, which adds a\n * display name to usages of React.createClass and createReactClass.\n */\nexport default class ReactDisplayNameTransformer extends Transformer {\n  constructor(\n     rootTransformer,\n     tokens,\n     importProcessor,\n     options,\n  ) {\n    super();this.rootTransformer = rootTransformer;this.tokens = tokens;this.importProcessor = importProcessor;this.options = options;;\n  }\n\n  process() {\n    const startIndex = this.tokens.currentIndex();\n    if (this.tokens.identifierName() === \"createReactClass\") {\n      const newName =\n        this.importProcessor && this.importProcessor.getIdentifierReplacement(\"createReactClass\");\n      if (newName) {\n        this.tokens.replaceToken(`(0, ${newName})`);\n      } else {\n        this.tokens.copyToken();\n      }\n      this.tryProcessCreateClassCall(startIndex);\n      return true;\n    }\n    if (\n      this.tokens.matches3(tt.name, tt.dot, tt.name) &&\n      this.tokens.identifierName() === \"React\" &&\n      this.tokens.identifierNameAtIndex(this.tokens.currentIndex() + 2) === \"createClass\"\n    ) {\n      const newName = this.importProcessor\n        ? this.importProcessor.getIdentifierReplacement(\"React\") || \"React\"\n        : \"React\";\n      if (newName) {\n        this.tokens.replaceToken(newName);\n        this.tokens.copyToken();\n        this.tokens.copyToken();\n      } else {\n        this.tokens.copyToken();\n        this.tokens.copyToken();\n        this.tokens.copyToken();\n      }\n      this.tryProcessCreateClassCall(startIndex);\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * This is called with the token position at the open-paren.\n   */\n   tryProcessCreateClassCall(startIndex) {\n    const displayName = this.findDisplayName(startIndex);\n    if (!displayName) {\n      return;\n    }\n\n    if (this.classNeedsDisplayName()) {\n      this.tokens.copyExpectedToken(tt.parenL);\n      this.tokens.copyExpectedToken(tt.braceL);\n      this.tokens.appendCode(`displayName: '${displayName}',`);\n      this.rootTransformer.processBalancedCode();\n      this.tokens.copyExpectedToken(tt.braceR);\n      this.tokens.copyExpectedToken(tt.parenR);\n    }\n  }\n\n   findDisplayName(startIndex) {\n    if (startIndex < 2) {\n      return null;\n    }\n    if (this.tokens.matches2AtIndex(startIndex - 2, tt.name, tt.eq)) {\n      // This is an assignment (or declaration) and the LHS is either an identifier or a member\n      // expression ending in an identifier, so use that identifier name.\n      return this.tokens.identifierNameAtIndex(startIndex - 2);\n    }\n    if (\n      startIndex >= 2 &&\n      this.tokens.tokens[startIndex - 2].identifierRole === IdentifierRole.ObjectKey\n    ) {\n      // This is an object literal value.\n      return this.tokens.identifierNameAtIndex(startIndex - 2);\n    }\n    if (this.tokens.matches2AtIndex(startIndex - 2, tt._export, tt._default)) {\n      return this.getDisplayNameFromFilename();\n    }\n    return null;\n  }\n\n   getDisplayNameFromFilename() {\n    const filePath = this.options.filePath || \"unknown\";\n    const pathSegments = filePath.split(\"/\");\n    const filename = pathSegments[pathSegments.length - 1];\n    const dotIndex = filename.lastIndexOf(\".\");\n    const baseFilename = dotIndex === -1 ? filename : filename.slice(0, dotIndex);\n    if (baseFilename === \"index\" && pathSegments[pathSegments.length - 2]) {\n      return pathSegments[pathSegments.length - 2];\n    } else {\n      return baseFilename;\n    }\n  }\n\n  /**\n   * We only want to add a display name when this is a function call containing\n   * one argument, which is an object literal without `displayName` as an\n   * existing key.\n   */\n   classNeedsDisplayName() {\n    let index = this.tokens.currentIndex();\n    if (!this.tokens.matches2(tt.parenL, tt.braceL)) {\n      return false;\n    }\n    // The block starts on the {, and we expect any displayName key to be in\n    // that context. We need to ignore other other contexts to avoid matching\n    // nested displayName keys.\n    const objectStartIndex = index + 1;\n    const objectContextId = this.tokens.tokens[objectStartIndex].contextId;\n    if (objectContextId == null) {\n      throw new Error(\"Expected non-null context ID on object open-brace.\");\n    }\n\n    for (; index < this.tokens.tokens.length; index++) {\n      const token = this.tokens.tokens[index];\n      if (token.type === tt.braceR && token.contextId === objectContextId) {\n        index++;\n        break;\n      }\n\n      if (\n        this.tokens.identifierNameAtIndex(index) === \"displayName\" &&\n        this.tokens.tokens[index].identifierRole === IdentifierRole.ObjectKey &&\n        token.contextId === objectContextId\n      ) {\n        // We found a displayName key, so bail out.\n        return false;\n      }\n    }\n\n    if (index === this.tokens.tokens.length) {\n      throw new Error(\"Unexpected end of input when processing React class.\");\n    }\n\n    // If we got this far, we know we have createClass with an object with no\n    // display name, so we want to proceed as long as that was the only argument.\n    return (\n      this.tokens.matches1AtIndex(index, tt.parenR) ||\n      this.tokens.matches2AtIndex(index, tt.comma, tt.parenR)\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAGA;;;;AAMe,MAAM,oCAAoC,wKAAA,CAAA,UAAW;IAClE,YACG,eAAe,EACf,MAAM,EACN,eAAe,EACf,OAAO,CACR;QACA,KAAK;QAAG,IAAI,CAAC,eAAe,GAAG;QAAgB,IAAI,CAAC,MAAM,GAAG;QAAO,IAAI,CAAC,eAAe,GAAG;QAAgB,IAAI,CAAC,OAAO,GAAG;;IAC5H;IAEA,UAAU;QACR,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,YAAY;QAC3C,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,OAAO,oBAAoB;YACvD,MAAM,UACJ,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC;YACxE,IAAI,SAAS;gBACX,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC5C,OAAO;gBACL,IAAI,CAAC,MAAM,CAAC,SAAS;YACvB;YACA,IAAI,CAAC,yBAAyB,CAAC;YAC/B,OAAO;QACT;QACA,IACE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,GAAG,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,KAC7C,IAAI,CAAC,MAAM,CAAC,cAAc,OAAO,WACjC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,OAAO,eACtE;YACA,MAAM,UAAU,IAAI,CAAC,eAAe,GAChC,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC,YAAY,UAC1D;YACJ,IAAI,SAAS;gBACX,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;gBACzB,IAAI,CAAC,MAAM,CAAC,SAAS;gBACrB,IAAI,CAAC,MAAM,CAAC,SAAS;YACvB,OAAO;gBACL,IAAI,CAAC,MAAM,CAAC,SAAS;gBACrB,IAAI,CAAC,MAAM,CAAC,SAAS;gBACrB,IAAI,CAAC,MAAM,CAAC,SAAS;YACvB;YACA,IAAI,CAAC,yBAAyB,CAAC;YAC/B,OAAO;QACT;QACA,OAAO;IACT;IAEA;;GAEC,GACA,0BAA0B,UAAU,EAAE;QACrC,MAAM,cAAc,IAAI,CAAC,eAAe,CAAC;QACzC,IAAI,CAAC,aAAa;YAChB;QACF;QAEA,IAAI,IAAI,CAAC,qBAAqB,IAAI;YAChC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;YACvC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;YACvC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,cAAc,EAAE,YAAY,EAAE,CAAC;YACvD,IAAI,CAAC,eAAe,CAAC,mBAAmB;YACxC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;YACvC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;QACzC;IACF;IAEC,gBAAgB,UAAU,EAAE;QAC3B,IAAI,aAAa,GAAG;YAClB,OAAO;QACT;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,GAAG,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,EAAE,GAAG;YAC/D,yFAAyF;YACzF,mEAAmE;YACnE,OAAO,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,aAAa;QACxD;QACA,IACE,cAAc,KACd,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,cAAc,KAAK,yKAAA,CAAA,iBAAc,CAAC,SAAS,EAC9E;YACA,mCAAmC;YACnC,OAAO,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,aAAa;QACxD;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,GAAG,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,QAAQ,GAAG;YACxE,OAAO,IAAI,CAAC,0BAA0B;QACxC;QACA,OAAO;IACT;IAEC,6BAA6B;QAC5B,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI;QAC1C,MAAM,eAAe,SAAS,KAAK,CAAC;QACpC,MAAM,WAAW,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;QACtD,MAAM,WAAW,SAAS,WAAW,CAAC;QACtC,MAAM,eAAe,aAAa,CAAC,IAAI,WAAW,SAAS,KAAK,CAAC,GAAG;QACpE,IAAI,iBAAiB,WAAW,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,EAAE;YACrE,OAAO,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;QAC9C,OAAO;YACL,OAAO;QACT;IACF;IAEA;;;;GAIC,GACA,wBAAwB;QACvB,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,YAAY;QACpC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YAC/C,OAAO;QACT;QACA,wEAAwE;QACxE,yEAAyE;QACzE,2BAA2B;QAC3B,MAAM,mBAAmB,QAAQ;QACjC,MAAM,kBAAkB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,SAAS;QACtE,IAAI,mBAAmB,MAAM;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAO,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAS;YACjD,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM;YACvC,IAAI,MAAM,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,MAAM,IAAI,MAAM,SAAS,KAAK,iBAAiB;gBACnE;gBACA;YACF;YAEA,IACE,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,WAAW,iBAC7C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,KAAK,yKAAA,CAAA,iBAAc,CAAC,SAAS,IACrE,MAAM,SAAS,KAAK,iBACpB;gBACA,2CAA2C;gBAC3C,OAAO;YACT;QACF;QAEA,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,yEAAyE;QACzE,6EAA6E;QAC7E,OACE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,MAAM,KAC5C,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,KAAK,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM;IAE1D;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2467, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/transformers/ReactHotLoaderTransformer.js"], "sourcesContent": ["import {IdentifierRole, isTopLevelDeclaration} from \"../parser/tokenizer\";\n\nimport Transformer from \"./Transformer\";\n\nexport default class ReactHotLoaderTransformer extends Transformer {\n   __init() {this.extractedDefaultExportName = null}\n\n  constructor( tokens,  filePath) {\n    super();this.tokens = tokens;this.filePath = filePath;ReactHotLoaderTransformer.prototype.__init.call(this);;\n  }\n\n  setExtractedDefaultExportName(extractedDefaultExportName) {\n    this.extractedDefaultExportName = extractedDefaultExportName;\n  }\n\n  getPrefixCode() {\n    return `\n      (function () {\n        var enterModule = require('react-hot-loader').enterModule;\n        enterModule && enterModule(module);\n      })();`\n      .replace(/\\s+/g, \" \")\n      .trim();\n  }\n\n  getSuffixCode() {\n    const topLevelNames = new Set();\n    for (const token of this.tokens.tokens) {\n      if (\n        !token.isType &&\n        isTopLevelDeclaration(token) &&\n        token.identifierRole !== IdentifierRole.ImportDeclaration\n      ) {\n        topLevelNames.add(this.tokens.identifierNameForToken(token));\n      }\n    }\n    const namesToRegister = Array.from(topLevelNames).map((name) => ({\n      variableName: name,\n      uniqueLocalName: name,\n    }));\n    if (this.extractedDefaultExportName) {\n      namesToRegister.push({\n        variableName: this.extractedDefaultExportName,\n        uniqueLocalName: \"default\",\n      });\n    }\n    return `\n;(function () {\n  var reactHotLoader = require('react-hot-loader').default;\n  var leaveModule = require('react-hot-loader').leaveModule;\n  if (!reactHotLoader) {\n    return;\n  }\n${namesToRegister\n  .map(\n    ({variableName, uniqueLocalName}) =>\n      `  reactHotLoader.register(${variableName}, \"${uniqueLocalName}\", ${JSON.stringify(\n        this.filePath || \"\",\n      )});`,\n  )\n  .join(\"\\n\")}\n  leaveModule(module);\n})();`;\n  }\n\n  process() {\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEe,MAAM,kCAAkC,wKAAA,CAAA,UAAW;IAC/D,SAAS;QAAC,IAAI,CAAC,0BAA0B,GAAG;IAAI;IAEjD,YAAa,MAAM,EAAG,QAAQ,CAAE;QAC9B,KAAK;QAAG,IAAI,CAAC,MAAM,GAAG;QAAO,IAAI,CAAC,QAAQ,GAAG;QAAS,0BAA0B,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;;IAC5G;IAEA,8BAA8B,0BAA0B,EAAE;QACxD,IAAI,CAAC,0BAA0B,GAAG;IACpC;IAEA,gBAAgB;QACd,OAAO,CAAC;;;;WAID,CAAC,CACL,OAAO,CAAC,QAAQ,KAChB,IAAI;IACT;IAEA,gBAAgB;QACd,MAAM,gBAAgB,IAAI;QAC1B,KAAK,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,CAAE;YACtC,IACE,CAAC,MAAM,MAAM,IACb,CAAA,GAAA,yKAAA,CAAA,wBAAqB,AAAD,EAAE,UACtB,MAAM,cAAc,KAAK,yKAAA,CAAA,iBAAc,CAAC,iBAAiB,EACzD;gBACA,cAAc,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;YACvD;QACF;QACA,MAAM,kBAAkB,MAAM,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,OAAS,CAAC;gBAC/D,cAAc;gBACd,iBAAiB;YACnB,CAAC;QACD,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACnC,gBAAgB,IAAI,CAAC;gBACnB,cAAc,IAAI,CAAC,0BAA0B;gBAC7C,iBAAiB;YACnB;QACF;QACA,OAAO,CAAC;;;;;;;AAOZ,EAAE,gBACC,GAAG,CACF,CAAC,EAAC,YAAY,EAAE,eAAe,EAAC,GAC9B,CAAC,0BAA0B,EAAE,aAAa,GAAG,EAAE,gBAAgB,GAAG,EAAE,KAAK,SAAS,CAChF,IAAI,CAAC,QAAQ,IAAI,IACjB,EAAE,CAAC,EAER,IAAI,CAAC,MAAM;;KAET,CAAC;IACJ;IAEA,UAAU;QACR,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/transformers/TypeScriptTransformer.js"], "sourcesContent": ["\nimport {TokenType as tt} from \"../parser/tokenizer/types\";\n\nimport isIdentifier from \"../util/isIdentifier\";\n\nimport Transformer from \"./Transformer\";\n\nexport default class TypeScriptTransformer extends Transformer {\n  constructor(\n     rootTransformer,\n     tokens,\n     isImportsTransformEnabled,\n  ) {\n    super();this.rootTransformer = rootTransformer;this.tokens = tokens;this.isImportsTransformEnabled = isImportsTransformEnabled;;\n  }\n\n  process() {\n    if (\n      this.rootTransformer.processPossibleArrowParamEnd() ||\n      this.rootTransformer.processPossibleAsyncArrowWithTypeParams() ||\n      this.rootTransformer.processPossibleTypeRange()\n    ) {\n      return true;\n    }\n    if (\n      this.tokens.matches1(tt._public) ||\n      this.tokens.matches1(tt._protected) ||\n      this.tokens.matches1(tt._private) ||\n      this.tokens.matches1(tt._abstract) ||\n      this.tokens.matches1(tt._readonly) ||\n      this.tokens.matches1(tt._override) ||\n      this.tokens.matches1(tt.nonNullAssertion)\n    ) {\n      this.tokens.removeInitialToken();\n      return true;\n    }\n    if (this.tokens.matches1(tt._enum) || this.tokens.matches2(tt._const, tt._enum)) {\n      this.processEnum();\n      return true;\n    }\n    if (\n      this.tokens.matches2(tt._export, tt._enum) ||\n      this.tokens.matches3(tt._export, tt._const, tt._enum)\n    ) {\n      this.processEnum(true);\n      return true;\n    }\n    return false;\n  }\n\n  processEnum(isExport = false) {\n    // We might have \"export const enum\", so just remove all relevant tokens.\n    this.tokens.removeInitialToken();\n    while (this.tokens.matches1(tt._const) || this.tokens.matches1(tt._enum)) {\n      this.tokens.removeToken();\n    }\n    const enumName = this.tokens.identifierName();\n    this.tokens.removeToken();\n    if (isExport && !this.isImportsTransformEnabled) {\n      this.tokens.appendCode(\"export \");\n    }\n    this.tokens.appendCode(`var ${enumName}; (function (${enumName})`);\n    this.tokens.copyExpectedToken(tt.braceL);\n    this.processEnumBody(enumName);\n    this.tokens.copyExpectedToken(tt.braceR);\n    if (isExport && this.isImportsTransformEnabled) {\n      this.tokens.appendCode(`)(${enumName} || (exports.${enumName} = ${enumName} = {}));`);\n    } else {\n      this.tokens.appendCode(`)(${enumName} || (${enumName} = {}));`);\n    }\n  }\n\n  /**\n   * Transform an enum into equivalent JS. This has complexity in a few places:\n   * - TS allows string enums, numeric enums, and a mix of the two styles within an enum.\n   * - Enum keys are allowed to be referenced in later enum values.\n   * - Enum keys are allowed to be strings.\n   * - When enum values are omitted, they should follow an auto-increment behavior.\n   */\n  processEnumBody(enumName) {\n    // Code that can be used to reference the previous enum member, or null if this is the first\n    // enum member.\n    let previousValueCode = null;\n    while (true) {\n      if (this.tokens.matches1(tt.braceR)) {\n        break;\n      }\n      const {nameStringCode, variableName} = this.extractEnumKeyInfo(this.tokens.currentToken());\n      this.tokens.removeInitialToken();\n\n      if (\n        this.tokens.matches3(tt.eq, tt.string, tt.comma) ||\n        this.tokens.matches3(tt.eq, tt.string, tt.braceR)\n      ) {\n        this.processStringLiteralEnumMember(enumName, nameStringCode, variableName);\n      } else if (this.tokens.matches1(tt.eq)) {\n        this.processExplicitValueEnumMember(enumName, nameStringCode, variableName);\n      } else {\n        this.processImplicitValueEnumMember(\n          enumName,\n          nameStringCode,\n          variableName,\n          previousValueCode,\n        );\n      }\n      if (this.tokens.matches1(tt.comma)) {\n        this.tokens.removeToken();\n      }\n\n      if (variableName != null) {\n        previousValueCode = variableName;\n      } else {\n        previousValueCode = `${enumName}[${nameStringCode}]`;\n      }\n    }\n  }\n\n  /**\n   * Detect name information about this enum key, which will be used to determine which code to emit\n   * and whether we should declare a variable as part of this declaration.\n   *\n   * Some cases to keep in mind:\n   * - Enum keys can be implicitly referenced later, e.g. `X = 1, Y = X`. In Sucrase, we implement\n   *   this by declaring a variable `X` so that later expressions can use it.\n   * - In addition to the usual identifier key syntax, enum keys are allowed to be string literals,\n   *   e.g. `\"hello world\" = 3,`. Template literal syntax is NOT allowed.\n   * - Even if the enum key is defined as a string literal, it may still be referenced by identifier\n   *   later, e.g. `\"X\" = 1, Y = X`. That means that we need to detect whether or not a string\n   *   literal is identifier-like and emit a variable if so, even if the declaration did not use an\n   *   identifier.\n   * - Reserved keywords like `break` are valid enum keys, but are not valid to be referenced later\n   *   and would be a syntax error if we emitted a variable, so we need to skip the variable\n   *   declaration in those cases.\n   *\n   * The variableName return value captures these nuances: if non-null, we can and must emit a\n   * variable declaration, and if null, we can't and shouldn't.\n   */\n  extractEnumKeyInfo(nameToken) {\n    if (nameToken.type === tt.name) {\n      const name = this.tokens.identifierNameForToken(nameToken);\n      return {\n        nameStringCode: `\"${name}\"`,\n        variableName: isIdentifier(name) ? name : null,\n      };\n    } else if (nameToken.type === tt.string) {\n      const name = this.tokens.stringValueForToken(nameToken);\n      return {\n        nameStringCode: this.tokens.code.slice(nameToken.start, nameToken.end),\n        variableName: isIdentifier(name) ? name : null,\n      };\n    } else {\n      throw new Error(\"Expected name or string at beginning of enum element.\");\n    }\n  }\n\n  /**\n   * Handle an enum member where the RHS is just a string literal (not omitted, not a number, and\n   * not a complex expression). This is the typical form for TS string enums, and in this case, we\n   * do *not* create a reverse mapping.\n   *\n   * This is called after deleting the key token, when the token processor is at the equals sign.\n   *\n   * Example 1:\n   * someKey = \"some value\"\n   * ->\n   * const someKey = \"some value\"; MyEnum[\"someKey\"] = someKey;\n   *\n   * Example 2:\n   * \"some key\" = \"some value\"\n   * ->\n   * MyEnum[\"some key\"] = \"some value\";\n   */\n  processStringLiteralEnumMember(\n    enumName,\n    nameStringCode,\n    variableName,\n  ) {\n    if (variableName != null) {\n      this.tokens.appendCode(`const ${variableName}`);\n      // =\n      this.tokens.copyToken();\n      // value string\n      this.tokens.copyToken();\n      this.tokens.appendCode(`; ${enumName}[${nameStringCode}] = ${variableName};`);\n    } else {\n      this.tokens.appendCode(`${enumName}[${nameStringCode}]`);\n      // =\n      this.tokens.copyToken();\n      // value string\n      this.tokens.copyToken();\n      this.tokens.appendCode(\";\");\n    }\n  }\n\n  /**\n   * Handle an enum member initialized with an expression on the right-hand side (other than a\n   * string literal). In these cases, we should transform the expression and emit code that sets up\n   * a reverse mapping.\n   *\n   * The TypeScript implementation of this operation distinguishes between expressions that can be\n   * \"constant folded\" at compile time (i.e. consist of number literals and simple math operations\n   * on those numbers) and ones that are dynamic. For constant expressions, it emits the resolved\n   * numeric value, and auto-incrementing is only allowed in that case. Evaluating expressions at\n   * compile time would add significant complexity to Sucrase, so Sucrase instead leaves the\n   * expression as-is, and will later emit something like `MyEnum[\"previousKey\"] + 1` to implement\n   * auto-incrementing.\n   *\n   * This is called after deleting the key token, when the token processor is at the equals sign.\n   *\n   * Example 1:\n   * someKey = 1 + 1\n   * ->\n   * const someKey = 1 + 1; MyEnum[MyEnum[\"someKey\"] = someKey] = \"someKey\";\n   *\n   * Example 2:\n   * \"some key\" = 1 + 1\n   * ->\n   * MyEnum[MyEnum[\"some key\"] = 1 + 1] = \"some key\";\n   */\n  processExplicitValueEnumMember(\n    enumName,\n    nameStringCode,\n    variableName,\n  ) {\n    const rhsEndIndex = this.tokens.currentToken().rhsEndIndex;\n    if (rhsEndIndex == null) {\n      throw new Error(\"Expected rhsEndIndex on enum assign.\");\n    }\n\n    if (variableName != null) {\n      this.tokens.appendCode(`const ${variableName}`);\n      this.tokens.copyToken();\n      while (this.tokens.currentIndex() < rhsEndIndex) {\n        this.rootTransformer.processToken();\n      }\n      this.tokens.appendCode(\n        `; ${enumName}[${enumName}[${nameStringCode}] = ${variableName}] = ${nameStringCode};`,\n      );\n    } else {\n      this.tokens.appendCode(`${enumName}[${enumName}[${nameStringCode}]`);\n      this.tokens.copyToken();\n      while (this.tokens.currentIndex() < rhsEndIndex) {\n        this.rootTransformer.processToken();\n      }\n      this.tokens.appendCode(`] = ${nameStringCode};`);\n    }\n  }\n\n  /**\n   * Handle an enum member with no right-hand side expression. In this case, the value is the\n   * previous value plus 1, or 0 if there was no previous value. We should also always emit a\n   * reverse mapping.\n   *\n   * Example 1:\n   * someKey2\n   * ->\n   * const someKey2 = someKey1 + 1; MyEnum[MyEnum[\"someKey2\"] = someKey2] = \"someKey2\";\n   *\n   * Example 2:\n   * \"some key 2\"\n   * ->\n   * MyEnum[MyEnum[\"some key 2\"] = someKey1 + 1] = \"some key 2\";\n   */\n  processImplicitValueEnumMember(\n    enumName,\n    nameStringCode,\n    variableName,\n    previousValueCode,\n  ) {\n    let valueCode = previousValueCode != null ? `${previousValueCode} + 1` : \"0\";\n    if (variableName != null) {\n      this.tokens.appendCode(`const ${variableName} = ${valueCode}; `);\n      valueCode = variableName;\n    }\n    this.tokens.appendCode(\n      `${enumName}[${enumName}[${nameStringCode}] = ${valueCode}] = ${nameStringCode};`,\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AAEA;AAEA;;;;AAEe,MAAM,8BAA8B,wKAAA,CAAA,UAAW;IAC5D,YACG,eAAe,EACf,MAAM,EACN,yBAAyB,CAC1B;QACA,KAAK;QAAG,IAAI,CAAC,eAAe,GAAG;QAAgB,IAAI,CAAC,MAAM,GAAG;QAAO,IAAI,CAAC,yBAAyB,GAAG;;IACvG;IAEA,UAAU;QACR,IACE,IAAI,CAAC,eAAe,CAAC,4BAA4B,MACjD,IAAI,CAAC,eAAe,CAAC,uCAAuC,MAC5D,IAAI,CAAC,eAAe,CAAC,wBAAwB,IAC7C;YACA,OAAO;QACT;QACA,IACE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,KAC/B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,UAAU,KAClC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,QAAQ,KAChC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,SAAS,KACjC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,SAAS,KACjC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,SAAS,KACjC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,gBAAgB,GACxC;YACA,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,OAAO;QACT;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;YAC/E,IAAI,CAAC,WAAW;YAChB,OAAO;QACT;QACA,IACE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,KAAK,KACzC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,yKAAA,CAAA,YAAE,CAAC,KAAK,GACpD;YACA,IAAI,CAAC,WAAW,CAAC;YACjB,OAAO;QACT;QACA,OAAO;IACT;IAEA,YAAY,WAAW,KAAK,EAAE;QAC5B,yEAAyE;QACzE,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC9B,MAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,EAAG;YACxE,IAAI,CAAC,MAAM,CAAC,WAAW;QACzB;QACA,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,cAAc;QAC3C,IAAI,CAAC,MAAM,CAAC,WAAW;QACvB,IAAI,YAAY,CAAC,IAAI,CAAC,yBAAyB,EAAE;YAC/C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACzB;QACA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,SAAS,aAAa,EAAE,SAAS,CAAC,CAAC;QACjE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;QACvC,IAAI,CAAC,eAAe,CAAC;QACrB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;QACvC,IAAI,YAAY,IAAI,CAAC,yBAAyB,EAAE;YAC9C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,SAAS,aAAa,EAAE,SAAS,GAAG,EAAE,SAAS,QAAQ,CAAC;QACtF,OAAO;YACL,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,SAAS,KAAK,EAAE,SAAS,QAAQ,CAAC;QAChE;IACF;IAEA;;;;;;GAMC,GACD,gBAAgB,QAAQ,EAAE;QACxB,4FAA4F;QAC5F,eAAe;QACf,IAAI,oBAAoB;QACxB,MAAO,KAAM;YACX,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBACnC;YACF;YACA,MAAM,EAAC,cAAc,EAAE,YAAY,EAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY;YACvF,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAE9B,IACE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,EAAE,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,yKAAA,CAAA,YAAE,CAAC,KAAK,KAC/C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,EAAE,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GAChD;gBACA,IAAI,CAAC,8BAA8B,CAAC,UAAU,gBAAgB;YAChE,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,EAAE,GAAG;gBACtC,IAAI,CAAC,8BAA8B,CAAC,UAAU,gBAAgB;YAChE,OAAO;gBACL,IAAI,CAAC,8BAA8B,CACjC,UACA,gBACA,cACA;YAEJ;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;gBAClC,IAAI,CAAC,MAAM,CAAC,WAAW;YACzB;YAEA,IAAI,gBAAgB,MAAM;gBACxB,oBAAoB;YACtB,OAAO;gBACL,oBAAoB,GAAG,SAAS,CAAC,EAAE,eAAe,CAAC,CAAC;YACtD;QACF;IACF;IAEA;;;;;;;;;;;;;;;;;;;GAmBC,GACD,mBAAmB,SAAS,EAAE;QAC5B,IAAI,UAAU,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE;YAC9B,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;YAChD,OAAO;gBACL,gBAAgB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC3B,cAAc,CAAA,GAAA,iKAAA,CAAA,UAAY,AAAD,EAAE,QAAQ,OAAO;YAC5C;QACF,OAAO,IAAI,UAAU,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE;YACvC,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;YAC7C,OAAO;gBACL,gBAAgB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,KAAK,EAAE,UAAU,GAAG;gBACrE,cAAc,CAAA,GAAA,iKAAA,CAAA,UAAY,AAAD,EAAE,QAAQ,OAAO;YAC5C;QACF,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;;;;;;;;;;;;;;;GAgBC,GACD,+BACE,QAAQ,EACR,cAAc,EACd,YAAY,EACZ;QACA,IAAI,gBAAgB,MAAM;YACxB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE,cAAc;YAC9C,IAAI;YACJ,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,eAAe;YACf,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,eAAe,IAAI,EAAE,aAAa,CAAC,CAAC;QAC9E,OAAO;YACL,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC,EAAE,eAAe,CAAC,CAAC;YACvD,IAAI;YACJ,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,eAAe;YACf,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACzB;IACF;IAEA;;;;;;;;;;;;;;;;;;;;;;;;GAwBC,GACD,+BACE,QAAQ,EACR,cAAc,EACd,YAAY,EACZ;QACA,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,WAAW;QAC1D,IAAI,eAAe,MAAM;YACvB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,gBAAgB,MAAM;YACxB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE,cAAc;YAC9C,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,YAAa;gBAC/C,IAAI,CAAC,eAAe,CAAC,YAAY;YACnC;YACA,IAAI,CAAC,MAAM,CAAC,UAAU,CACpB,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,eAAe,IAAI,EAAE,aAAa,IAAI,EAAE,eAAe,CAAC,CAAC;QAE1F,OAAO;YACL,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,eAAe,CAAC,CAAC;YACnE,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,YAAa;gBAC/C,IAAI,CAAC,eAAe,CAAC,YAAY;YACnC;YACA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QACjD;IACF;IAEA;;;;;;;;;;;;;;GAcC,GACD,+BACE,QAAQ,EACR,cAAc,EACd,YAAY,EACZ,iBAAiB,EACjB;QACA,IAAI,YAAY,qBAAqB,OAAO,GAAG,kBAAkB,IAAI,CAAC,GAAG;QACzE,IAAI,gBAAgB,MAAM;YACxB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE,aAAa,GAAG,EAAE,UAAU,EAAE,CAAC;YAC/D,YAAY;QACd;QACA,IAAI,CAAC,MAAM,CAAC,UAAU,CACpB,GAAG,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,eAAe,IAAI,EAAE,UAAU,IAAI,EAAE,eAAe,CAAC,CAAC;IAErF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/transformers/RootTransformer.js"], "sourcesContent": ["\n\n\nimport {ContextualKeyword} from \"../parser/tokenizer/keywords\";\nimport {TokenType as tt} from \"../parser/tokenizer/types\";\n\nimport getClassInfo, {} from \"../util/getClassInfo\";\nimport CJSImportTransformer from \"./CJSImportTransformer\";\nimport ESMImportTransformer from \"./ESMImportTransformer\";\nimport FlowTransformer from \"./FlowTransformer\";\nimport JestHoistTransformer from \"./JestHoistTransformer\";\nimport JSXTransformer from \"./JSXTransformer\";\nimport NumericSeparatorTransformer from \"./NumericSeparatorTransformer\";\nimport OptionalCatchBindingTransformer from \"./OptionalCatchBindingTransformer\";\nimport OptionalChainingNullishTransformer from \"./OptionalChainingNullishTransformer\";\nimport ReactDisplayNameTransformer from \"./ReactDisplayNameTransformer\";\nimport ReactHotLoaderTransformer from \"./ReactHotLoaderTransformer\";\n\nimport TypeScriptTransformer from \"./TypeScriptTransformer\";\n\n\n\n\n\n\n\n\nexport default class RootTransformer {\n   __init() {this.transformers = []}\n  \n  \n   __init2() {this.generatedVariables = []}\n  \n  \n  \n  \n\n  constructor(\n    sucraseContext,\n    transforms,\n    enableLegacyBabel5ModuleInterop,\n    options,\n  ) {;RootTransformer.prototype.__init.call(this);RootTransformer.prototype.__init2.call(this);\n    this.nameManager = sucraseContext.nameManager;\n    this.helperManager = sucraseContext.helperManager;\n    const {tokenProcessor, importProcessor} = sucraseContext;\n    this.tokens = tokenProcessor;\n    this.isImportsTransformEnabled = transforms.includes(\"imports\");\n    this.isReactHotLoaderTransformEnabled = transforms.includes(\"react-hot-loader\");\n    this.disableESTransforms = Boolean(options.disableESTransforms);\n\n    if (!options.disableESTransforms) {\n      this.transformers.push(\n        new OptionalChainingNullishTransformer(tokenProcessor, this.nameManager),\n      );\n      this.transformers.push(new NumericSeparatorTransformer(tokenProcessor));\n      this.transformers.push(new OptionalCatchBindingTransformer(tokenProcessor, this.nameManager));\n    }\n\n    if (transforms.includes(\"jsx\")) {\n      if (options.jsxRuntime !== \"preserve\") {\n        this.transformers.push(\n          new JSXTransformer(this, tokenProcessor, importProcessor, this.nameManager, options),\n        );\n      }\n      this.transformers.push(\n        new ReactDisplayNameTransformer(this, tokenProcessor, importProcessor, options),\n      );\n    }\n\n    let reactHotLoaderTransformer = null;\n    if (transforms.includes(\"react-hot-loader\")) {\n      if (!options.filePath) {\n        throw new Error(\"filePath is required when using the react-hot-loader transform.\");\n      }\n      reactHotLoaderTransformer = new ReactHotLoaderTransformer(tokenProcessor, options.filePath);\n      this.transformers.push(reactHotLoaderTransformer);\n    }\n\n    // Note that we always want to enable the imports transformer, even when the import transform\n    // itself isn't enabled, since we need to do type-only import pruning for both Flow and\n    // TypeScript.\n    if (transforms.includes(\"imports\")) {\n      if (importProcessor === null) {\n        throw new Error(\"Expected non-null importProcessor with imports transform enabled.\");\n      }\n      this.transformers.push(\n        new CJSImportTransformer(\n          this,\n          tokenProcessor,\n          importProcessor,\n          this.nameManager,\n          this.helperManager,\n          reactHotLoaderTransformer,\n          enableLegacyBabel5ModuleInterop,\n          Boolean(options.enableLegacyTypeScriptModuleInterop),\n          transforms.includes(\"typescript\"),\n          transforms.includes(\"flow\"),\n          Boolean(options.preserveDynamicImport),\n          Boolean(options.keepUnusedImports),\n        ),\n      );\n    } else {\n      this.transformers.push(\n        new ESMImportTransformer(\n          tokenProcessor,\n          this.nameManager,\n          this.helperManager,\n          reactHotLoaderTransformer,\n          transforms.includes(\"typescript\"),\n          transforms.includes(\"flow\"),\n          Boolean(options.keepUnusedImports),\n          options,\n        ),\n      );\n    }\n\n    if (transforms.includes(\"flow\")) {\n      this.transformers.push(\n        new FlowTransformer(this, tokenProcessor, transforms.includes(\"imports\")),\n      );\n    }\n    if (transforms.includes(\"typescript\")) {\n      this.transformers.push(\n        new TypeScriptTransformer(this, tokenProcessor, transforms.includes(\"imports\")),\n      );\n    }\n    if (transforms.includes(\"jest\")) {\n      this.transformers.push(\n        new JestHoistTransformer(this, tokenProcessor, this.nameManager, importProcessor),\n      );\n    }\n  }\n\n  transform() {\n    this.tokens.reset();\n    this.processBalancedCode();\n    const shouldAddUseStrict = this.isImportsTransformEnabled;\n    // \"use strict\" always needs to be first, so override the normal transformer order.\n    let prefix = shouldAddUseStrict ? '\"use strict\";' : \"\";\n    for (const transformer of this.transformers) {\n      prefix += transformer.getPrefixCode();\n    }\n    prefix += this.helperManager.emitHelpers();\n    prefix += this.generatedVariables.map((v) => ` var ${v};`).join(\"\");\n    for (const transformer of this.transformers) {\n      prefix += transformer.getHoistedCode();\n    }\n    let suffix = \"\";\n    for (const transformer of this.transformers) {\n      suffix += transformer.getSuffixCode();\n    }\n    const result = this.tokens.finish();\n    let {code} = result;\n    if (code.startsWith(\"#!\")) {\n      let newlineIndex = code.indexOf(\"\\n\");\n      if (newlineIndex === -1) {\n        newlineIndex = code.length;\n        code += \"\\n\";\n      }\n      return {\n        code: code.slice(0, newlineIndex + 1) + prefix + code.slice(newlineIndex + 1) + suffix,\n        // The hashbang line has no tokens, so shifting the tokens to account\n        // for prefix can happen normally.\n        mappings: this.shiftMappings(result.mappings, prefix.length),\n      };\n    } else {\n      return {\n        code: prefix + code + suffix,\n        mappings: this.shiftMappings(result.mappings, prefix.length),\n      };\n    }\n  }\n\n  processBalancedCode() {\n    let braceDepth = 0;\n    let parenDepth = 0;\n    while (!this.tokens.isAtEnd()) {\n      if (this.tokens.matches1(tt.braceL) || this.tokens.matches1(tt.dollarBraceL)) {\n        braceDepth++;\n      } else if (this.tokens.matches1(tt.braceR)) {\n        if (braceDepth === 0) {\n          return;\n        }\n        braceDepth--;\n      }\n      if (this.tokens.matches1(tt.parenL)) {\n        parenDepth++;\n      } else if (this.tokens.matches1(tt.parenR)) {\n        if (parenDepth === 0) {\n          return;\n        }\n        parenDepth--;\n      }\n      this.processToken();\n    }\n  }\n\n  processToken() {\n    if (this.tokens.matches1(tt._class)) {\n      this.processClass();\n      return;\n    }\n    for (const transformer of this.transformers) {\n      const wasProcessed = transformer.process();\n      if (wasProcessed) {\n        return;\n      }\n    }\n    this.tokens.copyToken();\n  }\n\n  /**\n   * Skip past a class with a name and return that name.\n   */\n  processNamedClass() {\n    if (!this.tokens.matches2(tt._class, tt.name)) {\n      throw new Error(\"Expected identifier for exported class name.\");\n    }\n    const name = this.tokens.identifierNameAtIndex(this.tokens.currentIndex() + 1);\n    this.processClass();\n    return name;\n  }\n\n  processClass() {\n    const classInfo = getClassInfo(this, this.tokens, this.nameManager, this.disableESTransforms);\n\n    // Both static and instance initializers need a class name to use to invoke the initializer, so\n    // assign to one if necessary.\n    const needsCommaExpression =\n      (classInfo.headerInfo.isExpression || !classInfo.headerInfo.className) &&\n      classInfo.staticInitializerNames.length + classInfo.instanceInitializerNames.length > 0;\n\n    let className = classInfo.headerInfo.className;\n    if (needsCommaExpression) {\n      className = this.nameManager.claimFreeName(\"_class\");\n      this.generatedVariables.push(className);\n      this.tokens.appendCode(` (${className} =`);\n    }\n\n    const classToken = this.tokens.currentToken();\n    const contextId = classToken.contextId;\n    if (contextId == null) {\n      throw new Error(\"Expected class to have a context ID.\");\n    }\n    this.tokens.copyExpectedToken(tt._class);\n    while (!this.tokens.matchesContextIdAndLabel(tt.braceL, contextId)) {\n      this.processToken();\n    }\n\n    this.processClassBody(classInfo, className);\n\n    const staticInitializerStatements = classInfo.staticInitializerNames.map(\n      (name) => `${className}.${name}()`,\n    );\n    if (needsCommaExpression) {\n      this.tokens.appendCode(\n        `, ${staticInitializerStatements.map((s) => `${s}, `).join(\"\")}${className})`,\n      );\n    } else if (classInfo.staticInitializerNames.length > 0) {\n      this.tokens.appendCode(` ${staticInitializerStatements.map((s) => `${s};`).join(\" \")}`);\n    }\n  }\n\n  /**\n   * We want to just handle class fields in all contexts, since TypeScript supports them. Later,\n   * when some JS implementations support class fields, this should be made optional.\n   */\n  processClassBody(classInfo, className) {\n    const {\n      headerInfo,\n      constructorInsertPos,\n      constructorInitializerStatements,\n      fields,\n      instanceInitializerNames,\n      rangesToRemove,\n    } = classInfo;\n    let fieldIndex = 0;\n    let rangeToRemoveIndex = 0;\n    const classContextId = this.tokens.currentToken().contextId;\n    if (classContextId == null) {\n      throw new Error(\"Expected non-null context ID on class.\");\n    }\n    this.tokens.copyExpectedToken(tt.braceL);\n    if (this.isReactHotLoaderTransformEnabled) {\n      this.tokens.appendCode(\n        \"__reactstandin__regenerateByEval(key, code) {this[key] = eval(code);}\",\n      );\n    }\n\n    const needsConstructorInit =\n      constructorInitializerStatements.length + instanceInitializerNames.length > 0;\n\n    if (constructorInsertPos === null && needsConstructorInit) {\n      const constructorInitializersCode = this.makeConstructorInitCode(\n        constructorInitializerStatements,\n        instanceInitializerNames,\n        className,\n      );\n      if (headerInfo.hasSuperclass) {\n        const argsName = this.nameManager.claimFreeName(\"args\");\n        this.tokens.appendCode(\n          `constructor(...${argsName}) { super(...${argsName}); ${constructorInitializersCode}; }`,\n        );\n      } else {\n        this.tokens.appendCode(`constructor() { ${constructorInitializersCode}; }`);\n      }\n    }\n\n    while (!this.tokens.matchesContextIdAndLabel(tt.braceR, classContextId)) {\n      if (fieldIndex < fields.length && this.tokens.currentIndex() === fields[fieldIndex].start) {\n        let needsCloseBrace = false;\n        if (this.tokens.matches1(tt.bracketL)) {\n          this.tokens.copyTokenWithPrefix(`${fields[fieldIndex].initializerName}() {this`);\n        } else if (this.tokens.matches1(tt.string) || this.tokens.matches1(tt.num)) {\n          this.tokens.copyTokenWithPrefix(`${fields[fieldIndex].initializerName}() {this[`);\n          needsCloseBrace = true;\n        } else {\n          this.tokens.copyTokenWithPrefix(`${fields[fieldIndex].initializerName}() {this.`);\n        }\n        while (this.tokens.currentIndex() < fields[fieldIndex].end) {\n          if (needsCloseBrace && this.tokens.currentIndex() === fields[fieldIndex].equalsIndex) {\n            this.tokens.appendCode(\"]\");\n          }\n          this.processToken();\n        }\n        this.tokens.appendCode(\"}\");\n        fieldIndex++;\n      } else if (\n        rangeToRemoveIndex < rangesToRemove.length &&\n        this.tokens.currentIndex() >= rangesToRemove[rangeToRemoveIndex].start\n      ) {\n        if (this.tokens.currentIndex() < rangesToRemove[rangeToRemoveIndex].end) {\n          this.tokens.removeInitialToken();\n        }\n        while (this.tokens.currentIndex() < rangesToRemove[rangeToRemoveIndex].end) {\n          this.tokens.removeToken();\n        }\n        rangeToRemoveIndex++;\n      } else if (this.tokens.currentIndex() === constructorInsertPos) {\n        this.tokens.copyToken();\n        if (needsConstructorInit) {\n          this.tokens.appendCode(\n            `;${this.makeConstructorInitCode(\n              constructorInitializerStatements,\n              instanceInitializerNames,\n              className,\n            )};`,\n          );\n        }\n        this.processToken();\n      } else {\n        this.processToken();\n      }\n    }\n    this.tokens.copyExpectedToken(tt.braceR);\n  }\n\n  makeConstructorInitCode(\n    constructorInitializerStatements,\n    instanceInitializerNames,\n    className,\n  ) {\n    return [\n      ...constructorInitializerStatements,\n      ...instanceInitializerNames.map((name) => `${className}.prototype.${name}.call(this)`),\n    ].join(\";\");\n  }\n\n  /**\n   * Normally it's ok to simply remove type tokens, but we need to be more careful when dealing with\n   * arrow function return types since they can confuse the parser. In that case, we want to move\n   * the close-paren to the same line as the arrow.\n   *\n   * See https://github.com/alangpierce/sucrase/issues/391 for more details.\n   */\n  processPossibleArrowParamEnd() {\n    if (this.tokens.matches2(tt.parenR, tt.colon) && this.tokens.tokenAtRelativeIndex(1).isType) {\n      let nextNonTypeIndex = this.tokens.currentIndex() + 1;\n      // Look ahead to see if this is an arrow function or something else.\n      while (this.tokens.tokens[nextNonTypeIndex].isType) {\n        nextNonTypeIndex++;\n      }\n      if (this.tokens.matches1AtIndex(nextNonTypeIndex, tt.arrow)) {\n        this.tokens.removeInitialToken();\n        while (this.tokens.currentIndex() < nextNonTypeIndex) {\n          this.tokens.removeToken();\n        }\n        this.tokens.replaceTokenTrimmingLeftWhitespace(\") =>\");\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * An async arrow function might be of the form:\n   *\n   * async <\n   *   T\n   * >() => {}\n   *\n   * in which case, removing the type parameters will cause a syntax error. Detect this case and\n   * move the open-paren earlier.\n   */\n  processPossibleAsyncArrowWithTypeParams() {\n    if (\n      !this.tokens.matchesContextual(ContextualKeyword._async) &&\n      !this.tokens.matches1(tt._async)\n    ) {\n      return false;\n    }\n    const nextToken = this.tokens.tokenAtRelativeIndex(1);\n    if (nextToken.type !== tt.lessThan || !nextToken.isType) {\n      return false;\n    }\n\n    let nextNonTypeIndex = this.tokens.currentIndex() + 1;\n    // Look ahead to see if this is an arrow function or something else.\n    while (this.tokens.tokens[nextNonTypeIndex].isType) {\n      nextNonTypeIndex++;\n    }\n    if (this.tokens.matches1AtIndex(nextNonTypeIndex, tt.parenL)) {\n      this.tokens.replaceToken(\"async (\");\n      this.tokens.removeInitialToken();\n      while (this.tokens.currentIndex() < nextNonTypeIndex) {\n        this.tokens.removeToken();\n      }\n      this.tokens.removeToken();\n      // We ate a ( token, so we need to process the tokens in between and then the ) token so that\n      // we remain balanced.\n      this.processBalancedCode();\n      this.processToken();\n      return true;\n    }\n    return false;\n  }\n\n  processPossibleTypeRange() {\n    if (this.tokens.currentToken().isType) {\n      this.tokens.removeInitialToken();\n      while (this.tokens.currentToken().isType) {\n        this.tokens.removeToken();\n      }\n      return true;\n    }\n    return false;\n  }\n\n  shiftMappings(\n    mappings,\n    prefixLength,\n  ) {\n    for (let i = 0; i < mappings.length; i++) {\n      const mapping = mappings[i];\n      if (mapping !== undefined) {\n        mappings[i] = mapping + prefixLength;\n      }\n    }\n    return mappings;\n  }\n}\n"], "names": [], "mappings": ";;;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;AASe,MAAM;IAClB,SAAS;QAAC,IAAI,CAAC,YAAY,GAAG,EAAE;IAAA;IAGhC,UAAU;QAAC,IAAI,CAAC,kBAAkB,GAAG,EAAE;IAAA;IAMxC,YACE,cAAc,EACd,UAAU,EACV,+BAA+B,EAC/B,OAAO,CACP;;QAAE,gBAAgB,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;QAAE,gBAAgB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;QACzF,IAAI,CAAC,WAAW,GAAG,eAAe,WAAW;QAC7C,IAAI,CAAC,aAAa,GAAG,eAAe,aAAa;QACjD,MAAM,EAAC,cAAc,EAAE,eAAe,EAAC,GAAG;QAC1C,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,yBAAyB,GAAG,WAAW,QAAQ,CAAC;QACrD,IAAI,CAAC,gCAAgC,GAAG,WAAW,QAAQ,CAAC;QAC5D,IAAI,CAAC,mBAAmB,GAAG,QAAQ,QAAQ,mBAAmB;QAE9D,IAAI,CAAC,QAAQ,mBAAmB,EAAE;YAChC,IAAI,CAAC,YAAY,CAAC,IAAI,CACpB,IAAI,+LAAA,CAAA,UAAkC,CAAC,gBAAgB,IAAI,CAAC,WAAW;YAEzE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,wLAAA,CAAA,UAA2B,CAAC;YACvD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,4LAAA,CAAA,UAA+B,CAAC,gBAAgB,IAAI,CAAC,WAAW;QAC7F;QAEA,IAAI,WAAW,QAAQ,CAAC,QAAQ;YAC9B,IAAI,QAAQ,UAAU,KAAK,YAAY;gBACrC,IAAI,CAAC,YAAY,CAAC,IAAI,CACpB,IAAI,2KAAA,CAAA,UAAc,CAAC,IAAI,EAAE,gBAAgB,iBAAiB,IAAI,CAAC,WAAW,EAAE;YAEhF;YACA,IAAI,CAAC,YAAY,CAAC,IAAI,CACpB,IAAI,wLAAA,CAAA,UAA2B,CAAC,IAAI,EAAE,gBAAgB,iBAAiB;QAE3E;QAEA,IAAI,4BAA4B;QAChC,IAAI,WAAW,QAAQ,CAAC,qBAAqB;YAC3C,IAAI,CAAC,QAAQ,QAAQ,EAAE;gBACrB,MAAM,IAAI,MAAM;YAClB;YACA,4BAA4B,IAAI,sLAAA,CAAA,UAAyB,CAAC,gBAAgB,QAAQ,QAAQ;YAC1F,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;QACzB;QAEA,6FAA6F;QAC7F,uFAAuF;QACvF,cAAc;QACd,IAAI,WAAW,QAAQ,CAAC,YAAY;YAClC,IAAI,oBAAoB,MAAM;gBAC5B,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,CAAC,YAAY,CAAC,IAAI,CACpB,IAAI,iLAAA,CAAA,UAAoB,CACtB,IAAI,EACJ,gBACA,iBACA,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,aAAa,EAClB,2BACA,iCACA,QAAQ,QAAQ,mCAAmC,GACnD,WAAW,QAAQ,CAAC,eACpB,WAAW,QAAQ,CAAC,SACpB,QAAQ,QAAQ,qBAAqB,GACrC,QAAQ,QAAQ,iBAAiB;QAGvC,OAAO;YACL,IAAI,CAAC,YAAY,CAAC,IAAI,CACpB,IAAI,iLAAA,CAAA,UAAoB,CACtB,gBACA,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,aAAa,EAClB,2BACA,WAAW,QAAQ,CAAC,eACpB,WAAW,QAAQ,CAAC,SACpB,QAAQ,QAAQ,iBAAiB,GACjC;QAGN;QAEA,IAAI,WAAW,QAAQ,CAAC,SAAS;YAC/B,IAAI,CAAC,YAAY,CAAC,IAAI,CACpB,IAAI,4KAAA,CAAA,UAAe,CAAC,IAAI,EAAE,gBAAgB,WAAW,QAAQ,CAAC;QAElE;QACA,IAAI,WAAW,QAAQ,CAAC,eAAe;YACrC,IAAI,CAAC,YAAY,CAAC,IAAI,CACpB,IAAI,kLAAA,CAAA,UAAqB,CAAC,IAAI,EAAE,gBAAgB,WAAW,QAAQ,CAAC;QAExE;QACA,IAAI,WAAW,QAAQ,CAAC,SAAS;YAC/B,IAAI,CAAC,YAAY,CAAC,IAAI,CACpB,IAAI,iLAAA,CAAA,UAAoB,CAAC,IAAI,EAAE,gBAAgB,IAAI,CAAC,WAAW,EAAE;QAErE;IACF;IAEA,YAAY;QACV,IAAI,CAAC,MAAM,CAAC,KAAK;QACjB,IAAI,CAAC,mBAAmB;QACxB,MAAM,qBAAqB,IAAI,CAAC,yBAAyB;QACzD,mFAAmF;QACnF,IAAI,SAAS,qBAAqB,kBAAkB;QACpD,KAAK,MAAM,eAAe,IAAI,CAAC,YAAY,CAAE;YAC3C,UAAU,YAAY,aAAa;QACrC;QACA,UAAU,IAAI,CAAC,aAAa,CAAC,WAAW;QACxC,UAAU,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;QAChE,KAAK,MAAM,eAAe,IAAI,CAAC,YAAY,CAAE;YAC3C,UAAU,YAAY,cAAc;QACtC;QACA,IAAI,SAAS;QACb,KAAK,MAAM,eAAe,IAAI,CAAC,YAAY,CAAE;YAC3C,UAAU,YAAY,aAAa;QACrC;QACA,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM;QACjC,IAAI,EAAC,IAAI,EAAC,GAAG;QACb,IAAI,KAAK,UAAU,CAAC,OAAO;YACzB,IAAI,eAAe,KAAK,OAAO,CAAC;YAChC,IAAI,iBAAiB,CAAC,GAAG;gBACvB,eAAe,KAAK,MAAM;gBAC1B,QAAQ;YACV;YACA,OAAO;gBACL,MAAM,KAAK,KAAK,CAAC,GAAG,eAAe,KAAK,SAAS,KAAK,KAAK,CAAC,eAAe,KAAK;gBAChF,qEAAqE;gBACrE,kCAAkC;gBAClC,UAAU,IAAI,CAAC,aAAa,CAAC,OAAO,QAAQ,EAAE,OAAO,MAAM;YAC7D;QACF,OAAO;YACL,OAAO;gBACL,MAAM,SAAS,OAAO;gBACtB,UAAU,IAAI,CAAC,aAAa,CAAC,OAAO,QAAQ,EAAE,OAAO,MAAM;YAC7D;QACF;IACF;IAEA,sBAAsB;QACpB,IAAI,aAAa;QACjB,IAAI,aAAa;QACjB,MAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAI;YAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,YAAY,GAAG;gBAC5E;YACF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBAC1C,IAAI,eAAe,GAAG;oBACpB;gBACF;gBACA;YACF;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBACnC;YACF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBAC1C,IAAI,eAAe,GAAG;oBACpB;gBACF;gBACA;YACF;YACA,IAAI,CAAC,YAAY;QACnB;IACF;IAEA,eAAe;QACb,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YACnC,IAAI,CAAC,YAAY;YACjB;QACF;QACA,KAAK,MAAM,eAAe,IAAI,CAAC,YAAY,CAAE;YAC3C,MAAM,eAAe,YAAY,OAAO;YACxC,IAAI,cAAc;gBAChB;YACF;QACF;QACA,IAAI,CAAC,MAAM,CAAC,SAAS;IACvB;IAEA;;GAEC,GACD,oBAAoB;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;YAC7C,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK;QAC5E,IAAI,CAAC,YAAY;QACjB,OAAO;IACT;IAEA,eAAe;QACb,MAAM,YAAY,CAAA,GAAA,iKAAA,CAAA,UAAY,AAAD,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB;QAE5F,+FAA+F;QAC/F,8BAA8B;QAC9B,MAAM,uBACJ,CAAC,UAAU,UAAU,CAAC,YAAY,IAAI,CAAC,UAAU,UAAU,CAAC,SAAS,KACrE,UAAU,sBAAsB,CAAC,MAAM,GAAG,UAAU,wBAAwB,CAAC,MAAM,GAAG;QAExF,IAAI,YAAY,UAAU,UAAU,CAAC,SAAS;QAC9C,IAAI,sBAAsB;YACxB,YAAY,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;YAC3C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC3C;QAEA,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,YAAY;QAC3C,MAAM,YAAY,WAAW,SAAS;QACtC,IAAI,aAAa,MAAM;YACrB,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;QACvC,MAAO,CAAC,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,WAAY;YAClE,IAAI,CAAC,YAAY;QACnB;QAEA,IAAI,CAAC,gBAAgB,CAAC,WAAW;QAEjC,MAAM,8BAA8B,UAAU,sBAAsB,CAAC,GAAG,CACtE,CAAC,OAAS,GAAG,UAAU,CAAC,EAAE,KAAK,EAAE,CAAC;QAEpC,IAAI,sBAAsB;YACxB,IAAI,CAAC,MAAM,CAAC,UAAU,CACpB,CAAC,EAAE,EAAE,4BAA4B,GAAG,CAAC,CAAC,IAAM,GAAG,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,UAAU,CAAC,CAAC;QAEjF,OAAO,IAAI,UAAU,sBAAsB,CAAC,MAAM,GAAG,GAAG;YACtD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,4BAA4B,GAAG,CAAC,CAAC,IAAM,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM;QACxF;IACF;IAEA;;;GAGC,GACD,iBAAiB,SAAS,EAAE,SAAS,EAAE;QACrC,MAAM,EACJ,UAAU,EACV,oBAAoB,EACpB,gCAAgC,EAChC,MAAM,EACN,wBAAwB,EACxB,cAAc,EACf,GAAG;QACJ,IAAI,aAAa;QACjB,IAAI,qBAAqB;QACzB,MAAM,iBAAiB,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,SAAS;QAC3D,IAAI,kBAAkB,MAAM;YAC1B,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;QACvC,IAAI,IAAI,CAAC,gCAAgC,EAAE;YACzC,IAAI,CAAC,MAAM,CAAC,UAAU,CACpB;QAEJ;QAEA,MAAM,uBACJ,iCAAiC,MAAM,GAAG,yBAAyB,MAAM,GAAG;QAE9E,IAAI,yBAAyB,QAAQ,sBAAsB;YACzD,MAAM,8BAA8B,IAAI,CAAC,uBAAuB,CAC9D,kCACA,0BACA;YAEF,IAAI,WAAW,aAAa,EAAE;gBAC5B,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;gBAChD,IAAI,CAAC,MAAM,CAAC,UAAU,CACpB,CAAC,eAAe,EAAE,SAAS,aAAa,EAAE,SAAS,GAAG,EAAE,4BAA4B,GAAG,CAAC;YAE5F,OAAO;gBACL,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,gBAAgB,EAAE,4BAA4B,GAAG,CAAC;YAC5E;QACF;QAEA,MAAO,CAAC,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,gBAAiB;YACvE,IAAI,aAAa,OAAO,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,OAAO,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE;gBACzF,IAAI,kBAAkB;gBACtB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,QAAQ,GAAG;oBACrC,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,QAAQ,CAAC;gBACjF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,GAAG,GAAG;oBAC1E,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,SAAS,CAAC;oBAChF,kBAAkB;gBACpB,OAAO;oBACL,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,SAAS,CAAC;gBAClF;gBACA,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,MAAM,CAAC,WAAW,CAAC,GAAG,CAAE;oBAC1D,IAAI,mBAAmB,IAAI,CAAC,MAAM,CAAC,YAAY,OAAO,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE;wBACpF,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;oBACzB;oBACA,IAAI,CAAC,YAAY;gBACnB;gBACA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;gBACvB;YACF,OAAO,IACL,qBAAqB,eAAe,MAAM,IAC1C,IAAI,CAAC,MAAM,CAAC,YAAY,MAAM,cAAc,CAAC,mBAAmB,CAAC,KAAK,EACtE;gBACA,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,cAAc,CAAC,mBAAmB,CAAC,GAAG,EAAE;oBACvE,IAAI,CAAC,MAAM,CAAC,kBAAkB;gBAChC;gBACA,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,cAAc,CAAC,mBAAmB,CAAC,GAAG,CAAE;oBAC1E,IAAI,CAAC,MAAM,CAAC,WAAW;gBACzB;gBACA;YACF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,OAAO,sBAAsB;gBAC9D,IAAI,CAAC,MAAM,CAAC,SAAS;gBACrB,IAAI,sBAAsB;oBACxB,IAAI,CAAC,MAAM,CAAC,UAAU,CACpB,CAAC,CAAC,EAAE,IAAI,CAAC,uBAAuB,CAC9B,kCACA,0BACA,WACA,CAAC,CAAC;gBAER;gBACA,IAAI,CAAC,YAAY;YACnB,OAAO;gBACL,IAAI,CAAC,YAAY;YACnB;QACF;QACA,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM;IACzC;IAEA,wBACE,gCAAgC,EAChC,wBAAwB,EACxB,SAAS,EACT;QACA,OAAO;eACF;eACA,yBAAyB,GAAG,CAAC,CAAC,OAAS,GAAG,UAAU,WAAW,EAAE,KAAK,WAAW,CAAC;SACtF,CAAC,IAAI,CAAC;IACT;IAEA;;;;;;GAMC,GACD,+BAA+B;QAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,yKAAA,CAAA,YAAE,CAAC,KAAK,KAAK,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,GAAG,MAAM,EAAE;YAC3F,IAAI,mBAAmB,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK;YACpD,oEAAoE;YACpE,MAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAE;gBAClD;YACF;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,kBAAkB,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;gBAC3D,IAAI,CAAC,MAAM,CAAC,kBAAkB;gBAC9B,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,iBAAkB;oBACpD,IAAI,CAAC,MAAM,CAAC,WAAW;gBACzB;gBACA,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC;gBAC/C,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,0CAA0C;QACxC,IACE,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,4KAAA,CAAA,oBAAiB,CAAC,MAAM,KACvD,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAC/B;YACA,OAAO;QACT;QACA,MAAM,YAAY,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC;QACnD,IAAI,UAAU,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,QAAQ,IAAI,CAAC,UAAU,MAAM,EAAE;YACvD,OAAO;QACT;QAEA,IAAI,mBAAmB,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK;QACpD,oEAAoE;QACpE,MAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAE;YAClD;QACF;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,kBAAkB,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YAC5D,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,KAAK,iBAAkB;gBACpD,IAAI,CAAC,MAAM,CAAC,WAAW;YACzB;YACA,IAAI,CAAC,MAAM,CAAC,WAAW;YACvB,6FAA6F;YAC7F,sBAAsB;YACtB,IAAI,CAAC,mBAAmB;YACxB,IAAI,CAAC,YAAY;YACjB,OAAO;QACT;QACA,OAAO;IACT;IAEA,2BAA2B;QACzB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,MAAM,EAAE;YACrC,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,MAAO,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,MAAM,CAAE;gBACxC,IAAI,CAAC,MAAM,CAAC,WAAW;YACzB;YACA,OAAO;QACT;QACA,OAAO;IACT;IAEA,cACE,QAAQ,EACR,YAAY,EACZ;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACxC,MAAM,UAAU,QAAQ,CAAC,EAAE;YAC3B,IAAI,YAAY,WAAW;gBACzB,QAAQ,CAAC,EAAE,GAAG,UAAU;YAC1B;QACF;QACA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}]}