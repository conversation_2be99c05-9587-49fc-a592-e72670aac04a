'use client';

import React from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import GameBoard from './GameBoard';
import ComponentPalette from './ComponentPalette';
import StatsPanel from './StatsPanel';
import { useGameStore } from '@/store/gameStore';

const FactoryGame: React.FC = () => {
  const { isRunning, toggleSimulation } = useGameStore();

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="h-screen flex flex-col bg-gray-900 text-white">
        {/* Header */}
        <header className="bg-gray-800 p-4 border-b border-gray-700">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-blue-400">Factory Builder</h1>
            <div className="flex gap-4 items-center">
              <button
                onClick={toggleSimulation}
                className={`px-4 py-2 rounded font-medium transition-colors ${
                  isRunning
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-green-600 hover:bg-green-700 text-white'
                }`}
              >
                {isRunning ? 'Pause' : 'Start'} Simulation
              </button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <div className="flex-1 flex">
          {/* Component Palette */}
          <div className="w-80 bg-gray-800 border-r border-gray-700">
            <ComponentPalette />
          </div>

          {/* Game Board */}
          <div className="flex-1 relative">
            <GameBoard />
          </div>

          {/* Stats Panel */}
          <div className="w-80 bg-gray-800 border-l border-gray-700">
            <StatsPanel />
          </div>
        </div>
      </div>
    </DndProvider>
  );
};

export default FactoryGame;
