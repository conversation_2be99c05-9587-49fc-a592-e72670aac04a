{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/util/getImportExportSpecifierInfo.js"], "sourcesContent": ["import {TokenType as tt} from \"../parser/tokenizer/types\";\n\n\n \n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Determine information about this named import or named export specifier.\n *\n * This syntax is the `a` from statements like these:\n * import {A} from \"./foo\";\n * export {A};\n * export {A} from \"./foo\";\n *\n * As it turns out, we can exactly characterize the syntax meaning by simply\n * counting the number of tokens, which can be from 1 to 4:\n * {A}\n * {type A}\n * {A as B}\n * {type A as B}\n *\n * In the type case, we never actually need the names in practice, so don't get\n * them.\n *\n * TODO: There's some redundancy with the type detection here and the isType\n * flag that's already present on tokens in TS mode. This function could\n * potentially be simplified and/or pushed to the call sites to avoid the object\n * allocation.\n */\nexport default function getImportExportSpecifierInfo(\n  tokens,\n  index = tokens.currentIndex(),\n) {\n  let endIndex = index + 1;\n  if (isSpecifierEnd(tokens, endIndex)) {\n    // import {A}\n    const name = tokens.identifierNameAtIndex(index);\n    return {\n      isType: false,\n      leftName: name,\n      rightName: name,\n      endIndex,\n    };\n  }\n  endIndex++;\n  if (isSpecifierEnd(tokens, endIndex)) {\n    // import {type A}\n    return {\n      isType: true,\n      leftName: null,\n      rightName: null,\n      endIndex,\n    };\n  }\n  endIndex++;\n  if (isSpecifierEnd(tokens, endIndex)) {\n    // import {A as B}\n    return {\n      isType: false,\n      leftName: tokens.identifierNameAtIndex(index),\n      rightName: tokens.identifierNameAtIndex(index + 2),\n      endIndex,\n    };\n  }\n  endIndex++;\n  if (isSpecifierEnd(tokens, endIndex)) {\n    // import {type A as B}\n    return {\n      isType: true,\n      leftName: null,\n      rightName: null,\n      endIndex,\n    };\n  }\n  throw new Error(`Unexpected import/export specifier at ${index}`);\n}\n\nfunction isSpecifierEnd(tokens, index) {\n  const token = tokens.tokens[index];\n  return token.type === tt.braceR || token.type === tt.comma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAwCe,SAAS,6BACtB,MAAM,EACN,QAAQ,OAAO,YAAY,EAAE;IAE7B,IAAI,WAAW,QAAQ;IACvB,IAAI,eAAe,QAAQ,WAAW;QACpC,aAAa;QACb,MAAM,OAAO,OAAO,qBAAqB,CAAC;QAC1C,OAAO;YACL,QAAQ;YACR,UAAU;YACV,WAAW;YACX;QACF;IACF;IACA;IACA,IAAI,eAAe,QAAQ,WAAW;QACpC,kBAAkB;QAClB,OAAO;YACL,QAAQ;YACR,UAAU;YACV,WAAW;YACX;QACF;IACF;IACA;IACA,IAAI,eAAe,QAAQ,WAAW;QACpC,kBAAkB;QAClB,OAAO;YACL,QAAQ;YACR,UAAU,OAAO,qBAAqB,CAAC;YACvC,WAAW,OAAO,qBAAqB,CAAC,QAAQ;YAChD;QACF;IACF;IACA;IACA,IAAI,eAAe,QAAQ,WAAW;QACpC,uBAAuB;QACvB,OAAO;YACL,QAAQ;YACR,UAAU;YACV,WAAW;YACX;QACF;IACF;IACA,MAAM,IAAI,MAAM,CAAC,sCAAsC,EAAE,OAAO;AAClE;AAEA,SAAS,eAAe,MAAM,EAAE,KAAK;IACnC,MAAM,QAAQ,OAAO,MAAM,CAAC,MAAM;IAClC,OAAO,MAAM,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,MAAM,IAAI,MAAM,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,KAAK;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/util/getJSXPragmaInfo.js"], "sourcesContent": ["\n\n\n\n\n\n\n\n\nexport default function getJSXPragmaInfo(options) {\n  const [base, suffix] = splitPragma(options.jsxPragma || \"React.createElement\");\n  const [fragmentBase, fragmentSuffix] = splitPragma(options.jsxFragmentPragma || \"React.Fragment\");\n  return {base, suffix, fragmentBase, fragmentSuffix};\n}\n\nfunction splitPragma(pragma) {\n  let dotIndex = pragma.indexOf(\".\");\n  if (dotIndex === -1) {\n    dotIndex = pragma.length;\n  }\n  return [pragma.slice(0, dotIndex), pragma.slice(dotIndex)];\n}\n"], "names": [], "mappings": ";;;AASe,SAAS,iBAAiB,OAAO;IAC9C,MAAM,CAAC,MAAM,OAAO,GAAG,YAAY,QAAQ,SAAS,IAAI;IACxD,MAAM,CAAC,cAAc,eAAe,GAAG,YAAY,QAAQ,iBAAiB,IAAI;IAChF,OAAO;QAAC;QAAM;QAAQ;QAAc;IAAc;AACpD;AAEA,SAAS,YAAY,MAAM;IACzB,IAAI,WAAW,OAAO,OAAO,CAAC;IAC9B,IAAI,aAAa,CAAC,GAAG;QACnB,WAAW,OAAO,MAAM;IAC1B;IACA,OAAO;QAAC,OAAO,KAAK,CAAC,GAAG;QAAW,OAAO,KAAK,CAAC;KAAU;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/util/getNonTypeIdentifiers.js"], "sourcesContent": ["\nimport {IdentifierRole} from \"../parser/tokenizer\";\nimport {TokenType, TokenType as tt} from \"../parser/tokenizer/types\";\n\nimport {startsWithLowerCase} from \"../transformers/JSXTransformer\";\nimport getJSXPragmaInfo from \"./getJSXPragmaInfo\";\n\nexport function getNonTypeIdentifiers(tokens, options) {\n  const jsxPragmaInfo = getJSXPragmaInfo(options);\n  const nonTypeIdentifiers = new Set();\n  for (let i = 0; i < tokens.tokens.length; i++) {\n    const token = tokens.tokens[i];\n    if (\n      token.type === tt.name &&\n      !token.isType &&\n      (token.identifierRole === IdentifierRole.Access ||\n        token.identifierRole === IdentifierRole.ObjectShorthand ||\n        token.identifierRole === IdentifierRole.ExportAccess) &&\n      !token.shadowsGlobal\n    ) {\n      nonTypeIdentifiers.add(tokens.identifierNameForToken(token));\n    }\n    if (token.type === tt.jsxTagStart) {\n      nonTypeIdentifiers.add(jsxPragmaInfo.base);\n    }\n    if (\n      token.type === tt.jsxTagStart &&\n      i + 1 < tokens.tokens.length &&\n      tokens.tokens[i + 1].type === tt.jsxTagEnd\n    ) {\n      nonTypeIdentifiers.add(jsxPragmaInfo.base);\n      nonTypeIdentifiers.add(jsxPragmaInfo.fragmentBase);\n    }\n    if (token.type === tt.jsxName && token.identifierRole === IdentifierRole.Access) {\n      const identifierName = tokens.identifierNameForToken(token);\n      // Lower-case single-component tag names like \"div\" don't count.\n      if (!startsWithLowerCase(identifierName) || tokens.tokens[i + 1].type === TokenType.dot) {\n        nonTypeIdentifiers.add(tokens.identifierNameForToken(token));\n      }\n    }\n  }\n  return nonTypeIdentifiers;\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AAEA;AACA;;;;;AAEO,SAAS,sBAAsB,MAAM,EAAE,OAAO;IACnD,MAAM,gBAAgB,CAAA,GAAA,qKAAA,CAAA,UAAgB,AAAD,EAAE;IACvC,MAAM,qBAAqB,IAAI;IAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,CAAC,MAAM,EAAE,IAAK;QAC7C,MAAM,QAAQ,OAAO,MAAM,CAAC,EAAE;QAC9B,IACE,MAAM,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,IAAI,IACtB,CAAC,MAAM,MAAM,IACb,CAAC,MAAM,cAAc,KAAK,yKAAA,CAAA,iBAAc,CAAC,MAAM,IAC7C,MAAM,cAAc,KAAK,yKAAA,CAAA,iBAAc,CAAC,eAAe,IACvD,MAAM,cAAc,KAAK,yKAAA,CAAA,iBAAc,CAAC,YAAY,KACtD,CAAC,MAAM,aAAa,EACpB;YACA,mBAAmB,GAAG,CAAC,OAAO,sBAAsB,CAAC;QACvD;QACA,IAAI,MAAM,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,WAAW,EAAE;YACjC,mBAAmB,GAAG,CAAC,cAAc,IAAI;QAC3C;QACA,IACE,MAAM,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,WAAW,IAC7B,IAAI,IAAI,OAAO,MAAM,CAAC,MAAM,IAC5B,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,SAAS,EAC1C;YACA,mBAAmB,GAAG,CAAC,cAAc,IAAI;YACzC,mBAAmB,GAAG,CAAC,cAAc,YAAY;QACnD;QACA,IAAI,MAAM,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,OAAO,IAAI,MAAM,cAAc,KAAK,yKAAA,CAAA,iBAAc,CAAC,MAAM,EAAE;YAC/E,MAAM,iBAAiB,OAAO,sBAAsB,CAAC;YACrD,gEAAgE;YAChE,IAAI,CAAC,CAAA,GAAA,2KAAA,CAAA,sBAAmB,AAAD,EAAE,mBAAmB,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,yKAAA,CAAA,YAAS,CAAC,GAAG,EAAE;gBACvF,mBAAmB,GAAG,CAAC,OAAO,sBAAsB,CAAC;YACvD;QACF;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/util/getIdentifierNames.js"], "sourcesContent": ["\nimport {TokenType as tt} from \"../parser/tokenizer/types\";\n\n/**\n * Get all identifier names in the code, in order, including duplicates.\n */\nexport default function getIdentifierNames(code, tokens) {\n  const names = [];\n  for (const token of tokens) {\n    if (token.type === tt.name) {\n      names.push(code.slice(token.start, token.end));\n    }\n  }\n  return names;\n}\n"], "names": [], "mappings": ";;;AACA;;AAKe,SAAS,mBAAmB,IAAI,EAAE,MAAM;IACrD,MAAM,QAAQ,EAAE;IAChB,KAAK,MAAM,SAAS,OAAQ;QAC1B,IAAI,MAAM,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE;YAC1B,MAAM,IAAI,CAAC,KAAK,KAAK,CAAC,MAAM,KAAK,EAAE,MAAM,GAAG;QAC9C;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/util/isAsyncOperation.js"], "sourcesContent": ["import {ContextualKeyword} from \"../parser/tokenizer/keywords\";\n\n\n/**\n * Determine whether this optional chain or nullish coalescing operation has any await statements in\n * it. If so, we'll need to transpile to an async operation.\n *\n * We compute this by walking the length of the operation and returning true if we see an await\n * keyword used as a real await (rather than an object key or property access). Nested optional\n * chain/nullish operations need to be tracked but don't silence await, but a nested async function\n * (or any other nested scope) will make the await not count.\n */\nexport default function isAsyncOperation(tokens) {\n  let index = tokens.currentIndex();\n  let depth = 0;\n  const startToken = tokens.currentToken();\n  do {\n    const token = tokens.tokens[index];\n    if (token.isOptionalChainStart) {\n      depth++;\n    }\n    if (token.isOptionalChainEnd) {\n      depth--;\n    }\n    depth += token.numNullishCoalesceStarts;\n    depth -= token.numNullishCoalesceEnds;\n\n    if (\n      token.contextualKeyword === ContextualKeyword._await &&\n      token.identifierRole == null &&\n      token.scopeDepth === startToken.scopeDepth\n    ) {\n      return true;\n    }\n    index += 1;\n  } while (depth > 0 && index < tokens.tokens.length);\n  return false;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAYe,SAAS,iBAAiB,MAAM;IAC7C,IAAI,QAAQ,OAAO,YAAY;IAC/B,IAAI,QAAQ;IACZ,MAAM,aAAa,OAAO,YAAY;IACtC,GAAG;QACD,MAAM,QAAQ,OAAO,MAAM,CAAC,MAAM;QAClC,IAAI,MAAM,oBAAoB,EAAE;YAC9B;QACF;QACA,IAAI,MAAM,kBAAkB,EAAE;YAC5B;QACF;QACA,SAAS,MAAM,wBAAwB;QACvC,SAAS,MAAM,sBAAsB;QAErC,IACE,MAAM,iBAAiB,KAAK,4KAAA,CAAA,oBAAiB,CAAC,MAAM,IACpD,MAAM,cAAc,IAAI,QACxB,MAAM,UAAU,KAAK,WAAW,UAAU,EAC1C;YACA,OAAO;QACT;QACA,SAAS;IACX,QAAS,QAAQ,KAAK,QAAQ,OAAO,MAAM,CAAC,MAAM,CAAE;IACpD,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/util/getClassInfo.js"], "sourcesContent": ["\n\nimport {ContextualKeyword} from \"../parser/tokenizer/keywords\";\nimport {TokenType as tt} from \"../parser/tokenizer/types\";\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Get information about the class fields for this class, given a token processor pointing to the\n * open-brace at the start of the class.\n */\nexport default function getClassInfo(\n  rootTransformer,\n  tokens,\n  nameManager,\n  disableESTransforms,\n) {\n  const snapshot = tokens.snapshot();\n\n  const headerInfo = processClassHeader(tokens);\n\n  let constructorInitializerStatements = [];\n  const instanceInitializerNames = [];\n  const staticInitializerNames = [];\n  let constructorInsertPos = null;\n  const fields = [];\n  const rangesToRemove = [];\n\n  const classContextId = tokens.currentToken().contextId;\n  if (classContextId == null) {\n    throw new Error(\"Expected non-null class context ID on class open-brace.\");\n  }\n\n  tokens.nextToken();\n  while (!tokens.matchesContextIdAndLabel(tt.braceR, classContextId)) {\n    if (tokens.matchesContextual(ContextualKeyword._constructor) && !tokens.currentToken().isType) {\n      ({constructorInitializerStatements, constructorInsertPos} = processConstructor(tokens));\n    } else if (tokens.matches1(tt.semi)) {\n      if (!disableESTransforms) {\n        rangesToRemove.push({start: tokens.currentIndex(), end: tokens.currentIndex() + 1});\n      }\n      tokens.nextToken();\n    } else if (tokens.currentToken().isType) {\n      tokens.nextToken();\n    } else {\n      // Either a method or a field. Skip to the identifier part.\n      const statementStartIndex = tokens.currentIndex();\n      let isStatic = false;\n      let isESPrivate = false;\n      let isDeclareOrAbstract = false;\n      while (isAccessModifier(tokens.currentToken())) {\n        if (tokens.matches1(tt._static)) {\n          isStatic = true;\n        }\n        if (tokens.matches1(tt.hash)) {\n          isESPrivate = true;\n        }\n        if (tokens.matches1(tt._declare) || tokens.matches1(tt._abstract)) {\n          isDeclareOrAbstract = true;\n        }\n        tokens.nextToken();\n      }\n      if (isStatic && tokens.matches1(tt.braceL)) {\n        // This is a static block, so don't process it in any special way.\n        skipToNextClassElement(tokens, classContextId);\n        continue;\n      }\n      if (isESPrivate) {\n        // Sucrase doesn't attempt to transpile private fields; just leave them as-is.\n        skipToNextClassElement(tokens, classContextId);\n        continue;\n      }\n      if (\n        tokens.matchesContextual(ContextualKeyword._constructor) &&\n        !tokens.currentToken().isType\n      ) {\n        ({constructorInitializerStatements, constructorInsertPos} = processConstructor(tokens));\n        continue;\n      }\n\n      const nameStartIndex = tokens.currentIndex();\n      skipFieldName(tokens);\n      if (tokens.matches1(tt.lessThan) || tokens.matches1(tt.parenL)) {\n        // This is a method, so nothing to process.\n        skipToNextClassElement(tokens, classContextId);\n        continue;\n      }\n      // There might be a type annotation that we need to skip.\n      while (tokens.currentToken().isType) {\n        tokens.nextToken();\n      }\n      if (tokens.matches1(tt.eq)) {\n        const equalsIndex = tokens.currentIndex();\n        // This is an initializer, so we need to wrap in an initializer method.\n        const valueEnd = tokens.currentToken().rhsEndIndex;\n        if (valueEnd == null) {\n          throw new Error(\"Expected rhsEndIndex on class field assignment.\");\n        }\n        tokens.nextToken();\n        while (tokens.currentIndex() < valueEnd) {\n          rootTransformer.processToken();\n        }\n        let initializerName;\n        if (isStatic) {\n          initializerName = nameManager.claimFreeName(\"__initStatic\");\n          staticInitializerNames.push(initializerName);\n        } else {\n          initializerName = nameManager.claimFreeName(\"__init\");\n          instanceInitializerNames.push(initializerName);\n        }\n        // Fields start at the name, so `static x = 1;` has a field range of `x = 1;`.\n        fields.push({\n          initializerName,\n          equalsIndex,\n          start: nameStartIndex,\n          end: tokens.currentIndex(),\n        });\n      } else if (!disableESTransforms || isDeclareOrAbstract) {\n        // This is a regular field declaration, like `x;`. With the class transform enabled, we just\n        // remove the line so that no output is produced. With the class transform disabled, we\n        // usually want to preserve the declaration (but still strip types), but if the `declare`\n        // or `abstract` keyword is specified, we should remove the line to avoid initializing the\n        // value to undefined.\n        rangesToRemove.push({start: statementStartIndex, end: tokens.currentIndex()});\n      }\n    }\n  }\n\n  tokens.restoreToSnapshot(snapshot);\n  if (disableESTransforms) {\n    // With ES transforms disabled, we don't want to transform regular class\n    // field declarations, and we don't need to do any additional tricks to\n    // reference the constructor for static init, but we still need to transform\n    // TypeScript field initializers defined as constructor parameters and we\n    // still need to remove `declare` fields. For now, we run the same code\n    // path but omit any field information, as if the class had no field\n    // declarations. In the future, when we fully drop the class fields\n    // transform, we can simplify this code significantly.\n    return {\n      headerInfo,\n      constructorInitializerStatements,\n      instanceInitializerNames: [],\n      staticInitializerNames: [],\n      constructorInsertPos,\n      fields: [],\n      rangesToRemove,\n    };\n  } else {\n    return {\n      headerInfo,\n      constructorInitializerStatements,\n      instanceInitializerNames,\n      staticInitializerNames,\n      constructorInsertPos,\n      fields,\n      rangesToRemove,\n    };\n  }\n}\n\n/**\n * Move the token processor to the next method/field in the class.\n *\n * To do that, we seek forward to the next start of a class name (either an open\n * bracket or an identifier, or the closing curly brace), then seek backward to\n * include any access modifiers.\n */\nfunction skipToNextClassElement(tokens, classContextId) {\n  tokens.nextToken();\n  while (tokens.currentToken().contextId !== classContextId) {\n    tokens.nextToken();\n  }\n  while (isAccessModifier(tokens.tokenAtRelativeIndex(-1))) {\n    tokens.previousToken();\n  }\n}\n\nfunction processClassHeader(tokens) {\n  const classToken = tokens.currentToken();\n  const contextId = classToken.contextId;\n  if (contextId == null) {\n    throw new Error(\"Expected context ID on class token.\");\n  }\n  const isExpression = classToken.isExpression;\n  if (isExpression == null) {\n    throw new Error(\"Expected isExpression on class token.\");\n  }\n  let className = null;\n  let hasSuperclass = false;\n  tokens.nextToken();\n  if (tokens.matches1(tt.name)) {\n    className = tokens.identifierName();\n  }\n  while (!tokens.matchesContextIdAndLabel(tt.braceL, contextId)) {\n    // If this has a superclass, there will always be an `extends` token. If it doesn't have a\n    // superclass, only type parameters and `implements` clauses can show up here, all of which\n    // consist only of type tokens. A declaration like `class A<B extends C> {` should *not* count\n    // as having a superclass.\n    if (tokens.matches1(tt._extends) && !tokens.currentToken().isType) {\n      hasSuperclass = true;\n    }\n    tokens.nextToken();\n  }\n  return {isExpression, className, hasSuperclass};\n}\n\n/**\n * Extract useful information out of a constructor, starting at the \"constructor\" name.\n */\nfunction processConstructor(tokens)\n\n\n {\n  const constructorInitializerStatements = [];\n\n  tokens.nextToken();\n  const constructorContextId = tokens.currentToken().contextId;\n  if (constructorContextId == null) {\n    throw new Error(\"Expected context ID on open-paren starting constructor params.\");\n  }\n  // Advance through parameters looking for access modifiers.\n  while (!tokens.matchesContextIdAndLabel(tt.parenR, constructorContextId)) {\n    if (tokens.currentToken().contextId === constructorContextId) {\n      // Current token is an open paren or comma just before a param, so check\n      // that param for access modifiers.\n      tokens.nextToken();\n      if (isAccessModifier(tokens.currentToken())) {\n        tokens.nextToken();\n        while (isAccessModifier(tokens.currentToken())) {\n          tokens.nextToken();\n        }\n        const token = tokens.currentToken();\n        if (token.type !== tt.name) {\n          throw new Error(\"Expected identifier after access modifiers in constructor arg.\");\n        }\n        const name = tokens.identifierNameForToken(token);\n        constructorInitializerStatements.push(`this.${name} = ${name}`);\n      }\n    } else {\n      tokens.nextToken();\n    }\n  }\n  // )\n  tokens.nextToken();\n  // Constructor type annotations are invalid, but skip them anyway since\n  // they're easy to skip.\n  while (tokens.currentToken().isType) {\n    tokens.nextToken();\n  }\n  let constructorInsertPos = tokens.currentIndex();\n\n  // Advance through body looking for a super call.\n  let foundSuperCall = false;\n  while (!tokens.matchesContextIdAndLabel(tt.braceR, constructorContextId)) {\n    if (!foundSuperCall && tokens.matches2(tt._super, tt.parenL)) {\n      tokens.nextToken();\n      const superCallContextId = tokens.currentToken().contextId;\n      if (superCallContextId == null) {\n        throw new Error(\"Expected a context ID on the super call\");\n      }\n      while (!tokens.matchesContextIdAndLabel(tt.parenR, superCallContextId)) {\n        tokens.nextToken();\n      }\n      constructorInsertPos = tokens.currentIndex();\n      foundSuperCall = true;\n    }\n    tokens.nextToken();\n  }\n  // }\n  tokens.nextToken();\n\n  return {constructorInitializerStatements, constructorInsertPos};\n}\n\n/**\n * Determine if this is any token that can go before the name in a method/field.\n */\nfunction isAccessModifier(token) {\n  return [\n    tt._async,\n    tt._get,\n    tt._set,\n    tt.plus,\n    tt.minus,\n    tt._readonly,\n    tt._static,\n    tt._public,\n    tt._private,\n    tt._protected,\n    tt._override,\n    tt._abstract,\n    tt.star,\n    tt._declare,\n    tt.hash,\n  ].includes(token.type);\n}\n\n/**\n * The next token or set of tokens is either an identifier or an expression in square brackets, for\n * a method or field name.\n */\nfunction skipFieldName(tokens) {\n  if (tokens.matches1(tt.bracketL)) {\n    const startToken = tokens.currentToken();\n    const classContextId = startToken.contextId;\n    if (classContextId == null) {\n      throw new Error(\"Expected class context ID on computed name open bracket.\");\n    }\n    while (!tokens.matchesContextIdAndLabel(tt.bracketR, classContextId)) {\n      tokens.nextToken();\n    }\n    tokens.nextToken();\n  } else {\n    tokens.nextToken();\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;;AA4Ce,SAAS,aACtB,eAAe,EACf,MAAM,EACN,WAAW,EACX,mBAAmB;IAEnB,MAAM,WAAW,OAAO,QAAQ;IAEhC,MAAM,aAAa,mBAAmB;IAEtC,IAAI,mCAAmC,EAAE;IACzC,MAAM,2BAA2B,EAAE;IACnC,MAAM,yBAAyB,EAAE;IACjC,IAAI,uBAAuB;IAC3B,MAAM,SAAS,EAAE;IACjB,MAAM,iBAAiB,EAAE;IAEzB,MAAM,iBAAiB,OAAO,YAAY,GAAG,SAAS;IACtD,IAAI,kBAAkB,MAAM;QAC1B,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,SAAS;IAChB,MAAO,CAAC,OAAO,wBAAwB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,gBAAiB;QAClE,IAAI,OAAO,iBAAiB,CAAC,4KAAA,CAAA,oBAAiB,CAAC,YAAY,KAAK,CAAC,OAAO,YAAY,GAAG,MAAM,EAAE;YAC7F,CAAC,EAAC,gCAAgC,EAAE,oBAAoB,EAAC,GAAG,mBAAmB,OAAO;QACxF,OAAO,IAAI,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;YACnC,IAAI,CAAC,qBAAqB;gBACxB,eAAe,IAAI,CAAC;oBAAC,OAAO,OAAO,YAAY;oBAAI,KAAK,OAAO,YAAY,KAAK;gBAAC;YACnF;YACA,OAAO,SAAS;QAClB,OAAO,IAAI,OAAO,YAAY,GAAG,MAAM,EAAE;YACvC,OAAO,SAAS;QAClB,OAAO;YACL,2DAA2D;YAC3D,MAAM,sBAAsB,OAAO,YAAY;YAC/C,IAAI,WAAW;YACf,IAAI,cAAc;YAClB,IAAI,sBAAsB;YAC1B,MAAO,iBAAiB,OAAO,YAAY,IAAK;gBAC9C,IAAI,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,OAAO,GAAG;oBAC/B,WAAW;gBACb;gBACA,IAAI,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;oBAC5B,cAAc;gBAChB;gBACA,IAAI,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,QAAQ,KAAK,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,SAAS,GAAG;oBACjE,sBAAsB;gBACxB;gBACA,OAAO,SAAS;YAClB;YACA,IAAI,YAAY,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBAC1C,kEAAkE;gBAClE,uBAAuB,QAAQ;gBAC/B;YACF;YACA,IAAI,aAAa;gBACf,8EAA8E;gBAC9E,uBAAuB,QAAQ;gBAC/B;YACF;YACA,IACE,OAAO,iBAAiB,CAAC,4KAAA,CAAA,oBAAiB,CAAC,YAAY,KACvD,CAAC,OAAO,YAAY,GAAG,MAAM,EAC7B;gBACA,CAAC,EAAC,gCAAgC,EAAE,oBAAoB,EAAC,GAAG,mBAAmB,OAAO;gBACtF;YACF;YAEA,MAAM,iBAAiB,OAAO,YAAY;YAC1C,cAAc;YACd,IAAI,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,QAAQ,KAAK,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBAC9D,2CAA2C;gBAC3C,uBAAuB,QAAQ;gBAC/B;YACF;YACA,yDAAyD;YACzD,MAAO,OAAO,YAAY,GAAG,MAAM,CAAE;gBACnC,OAAO,SAAS;YAClB;YACA,IAAI,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,EAAE,GAAG;gBAC1B,MAAM,cAAc,OAAO,YAAY;gBACvC,uEAAuE;gBACvE,MAAM,WAAW,OAAO,YAAY,GAAG,WAAW;gBAClD,IAAI,YAAY,MAAM;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBACA,OAAO,SAAS;gBAChB,MAAO,OAAO,YAAY,KAAK,SAAU;oBACvC,gBAAgB,YAAY;gBAC9B;gBACA,IAAI;gBACJ,IAAI,UAAU;oBACZ,kBAAkB,YAAY,aAAa,CAAC;oBAC5C,uBAAuB,IAAI,CAAC;gBAC9B,OAAO;oBACL,kBAAkB,YAAY,aAAa,CAAC;oBAC5C,yBAAyB,IAAI,CAAC;gBAChC;gBACA,8EAA8E;gBAC9E,OAAO,IAAI,CAAC;oBACV;oBACA;oBACA,OAAO;oBACP,KAAK,OAAO,YAAY;gBAC1B;YACF,OAAO,IAAI,CAAC,uBAAuB,qBAAqB;gBACtD,4FAA4F;gBAC5F,uFAAuF;gBACvF,yFAAyF;gBACzF,0FAA0F;gBAC1F,sBAAsB;gBACtB,eAAe,IAAI,CAAC;oBAAC,OAAO;oBAAqB,KAAK,OAAO,YAAY;gBAAE;YAC7E;QACF;IACF;IAEA,OAAO,iBAAiB,CAAC;IACzB,IAAI,qBAAqB;QACvB,wEAAwE;QACxE,uEAAuE;QACvE,4EAA4E;QAC5E,yEAAyE;QACzE,uEAAuE;QACvE,oEAAoE;QACpE,mEAAmE;QACnE,sDAAsD;QACtD,OAAO;YACL;YACA;YACA,0BAA0B,EAAE;YAC5B,wBAAwB,EAAE;YAC1B;YACA,QAAQ,EAAE;YACV;QACF;IACF,OAAO;QACL,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;AACF;AAEA;;;;;;CAMC,GACD,SAAS,uBAAuB,MAAM,EAAE,cAAc;IACpD,OAAO,SAAS;IAChB,MAAO,OAAO,YAAY,GAAG,SAAS,KAAK,eAAgB;QACzD,OAAO,SAAS;IAClB;IACA,MAAO,iBAAiB,OAAO,oBAAoB,CAAC,CAAC,IAAK;QACxD,OAAO,aAAa;IACtB;AACF;AAEA,SAAS,mBAAmB,MAAM;IAChC,MAAM,aAAa,OAAO,YAAY;IACtC,MAAM,YAAY,WAAW,SAAS;IACtC,IAAI,aAAa,MAAM;QACrB,MAAM,IAAI,MAAM;IAClB;IACA,MAAM,eAAe,WAAW,YAAY;IAC5C,IAAI,gBAAgB,MAAM;QACxB,MAAM,IAAI,MAAM;IAClB;IACA,IAAI,YAAY;IAChB,IAAI,gBAAgB;IACpB,OAAO,SAAS;IAChB,IAAI,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;QAC5B,YAAY,OAAO,cAAc;IACnC;IACA,MAAO,CAAC,OAAO,wBAAwB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,WAAY;QAC7D,0FAA0F;QAC1F,2FAA2F;QAC3F,8FAA8F;QAC9F,0BAA0B;QAC1B,IAAI,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,QAAQ,KAAK,CAAC,OAAO,YAAY,GAAG,MAAM,EAAE;YACjE,gBAAgB;QAClB;QACA,OAAO,SAAS;IAClB;IACA,OAAO;QAAC;QAAc;QAAW;IAAa;AAChD;AAEA;;CAEC,GACD,SAAS,mBAAmB,MAAM;IAIhC,MAAM,mCAAmC,EAAE;IAE3C,OAAO,SAAS;IAChB,MAAM,uBAAuB,OAAO,YAAY,GAAG,SAAS;IAC5D,IAAI,wBAAwB,MAAM;QAChC,MAAM,IAAI,MAAM;IAClB;IACA,2DAA2D;IAC3D,MAAO,CAAC,OAAO,wBAAwB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,sBAAuB;QACxE,IAAI,OAAO,YAAY,GAAG,SAAS,KAAK,sBAAsB;YAC5D,wEAAwE;YACxE,mCAAmC;YACnC,OAAO,SAAS;YAChB,IAAI,iBAAiB,OAAO,YAAY,KAAK;gBAC3C,OAAO,SAAS;gBAChB,MAAO,iBAAiB,OAAO,YAAY,IAAK;oBAC9C,OAAO,SAAS;gBAClB;gBACA,MAAM,QAAQ,OAAO,YAAY;gBACjC,IAAI,MAAM,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE;oBAC1B,MAAM,IAAI,MAAM;gBAClB;gBACA,MAAM,OAAO,OAAO,sBAAsB,CAAC;gBAC3C,iCAAiC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,GAAG,EAAE,MAAM;YAChE;QACF,OAAO;YACL,OAAO,SAAS;QAClB;IACF;IACA,IAAI;IACJ,OAAO,SAAS;IAChB,uEAAuE;IACvE,wBAAwB;IACxB,MAAO,OAAO,YAAY,GAAG,MAAM,CAAE;QACnC,OAAO,SAAS;IAClB;IACA,IAAI,uBAAuB,OAAO,YAAY;IAE9C,iDAAiD;IACjD,IAAI,iBAAiB;IACrB,MAAO,CAAC,OAAO,wBAAwB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,sBAAuB;QACxE,IAAI,CAAC,kBAAkB,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YAC5D,OAAO,SAAS;YAChB,MAAM,qBAAqB,OAAO,YAAY,GAAG,SAAS;YAC1D,IAAI,sBAAsB,MAAM;gBAC9B,MAAM,IAAI,MAAM;YAClB;YACA,MAAO,CAAC,OAAO,wBAAwB,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAE,oBAAqB;gBACtE,OAAO,SAAS;YAClB;YACA,uBAAuB,OAAO,YAAY;YAC1C,iBAAiB;QACnB;QACA,OAAO,SAAS;IAClB;IACA,IAAI;IACJ,OAAO,SAAS;IAEhB,OAAO;QAAC;QAAkC;IAAoB;AAChE;AAEA;;CAEC,GACD,SAAS,iBAAiB,KAAK;IAC7B,OAAO;QACL,yKAAA,CAAA,YAAE,CAAC,MAAM;QACT,yKAAA,CAAA,YAAE,CAAC,IAAI;QACP,yKAAA,CAAA,YAAE,CAAC,IAAI;QACP,yKAAA,CAAA,YAAE,CAAC,IAAI;QACP,yKAAA,CAAA,YAAE,CAAC,KAAK;QACR,yKAAA,CAAA,YAAE,CAAC,SAAS;QACZ,yKAAA,CAAA,YAAE,CAAC,OAAO;QACV,yKAAA,CAAA,YAAE,CAAC,OAAO;QACV,yKAAA,CAAA,YAAE,CAAC,QAAQ;QACX,yKAAA,CAAA,YAAE,CAAC,UAAU;QACb,yKAAA,CAAA,YAAE,CAAC,SAAS;QACZ,yKAAA,CAAA,YAAE,CAAC,SAAS;QACZ,yKAAA,CAAA,YAAE,CAAC,IAAI;QACP,yKAAA,CAAA,YAAE,CAAC,QAAQ;QACX,yKAAA,CAAA,YAAE,CAAC,IAAI;KACR,CAAC,QAAQ,CAAC,MAAM,IAAI;AACvB;AAEA;;;CAGC,GACD,SAAS,cAAc,MAAM;IAC3B,IAAI,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,QAAQ,GAAG;QAChC,MAAM,aAAa,OAAO,YAAY;QACtC,MAAM,iBAAiB,WAAW,SAAS;QAC3C,IAAI,kBAAkB,MAAM;YAC1B,MAAM,IAAI,MAAM;QAClB;QACA,MAAO,CAAC,OAAO,wBAAwB,CAAC,yKAAA,CAAA,YAAE,CAAC,QAAQ,EAAE,gBAAiB;YACpE,OAAO,SAAS;QAClB;QACA,OAAO,SAAS;IAClB,OAAO;QACL,OAAO,SAAS;IAClB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 491, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/util/elideImportEquals.js"], "sourcesContent": ["import {TokenType as tt} from \"../parser/tokenizer/types\";\n\n\nexport default function elideImportEquals(tokens) {\n  // import\n  tokens.removeInitialToken();\n  // name\n  tokens.removeToken();\n  // =\n  tokens.removeToken();\n  // name or require\n  tokens.removeToken();\n  // Handle either `import A = require('A')` or `import A = B.C.D`.\n  if (tokens.matches1(tt.parenL)) {\n    // (\n    tokens.removeToken();\n    // path string\n    tokens.removeToken();\n    // )\n    tokens.removeToken();\n  } else {\n    while (tokens.matches1(tt.dot)) {\n      // .\n      tokens.removeToken();\n      // name\n      tokens.removeToken();\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGe,SAAS,kBAAkB,MAAM;IAC9C,SAAS;IACT,OAAO,kBAAkB;IACzB,OAAO;IACP,OAAO,WAAW;IAClB,IAAI;IACJ,OAAO,WAAW;IAClB,kBAAkB;IAClB,OAAO,WAAW;IAClB,iEAAiE;IACjE,IAAI,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;QAC9B,IAAI;QACJ,OAAO,WAAW;QAClB,cAAc;QACd,OAAO,WAAW;QAClB,IAAI;QACJ,OAAO,WAAW;IACpB,OAAO;QACL,MAAO,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,GAAG,EAAG;YAC9B,IAAI;YACJ,OAAO,WAAW;YAClB,OAAO;YACP,OAAO,WAAW;QACpB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/util/getDeclarationInfo.js"], "sourcesContent": ["import {isTopLevelDeclaration} from \"../parser/tokenizer\";\nimport {TokenType as tt} from \"../parser/tokenizer/types\";\n\n\n\n\n\n\n\nexport const EMPTY_DECLARATION_INFO = {\n  typeDeclarations: new Set(),\n  valueDeclarations: new Set(),\n};\n\n/**\n * Get all top-level identifiers that should be preserved when exported in TypeScript.\n *\n * Examples:\n * - If an identifier is declared as `const x`, then `export {x}` should be preserved.\n * - If it's declared as `type x`, then `export {x}` should be removed.\n * - If it's declared as both `const x` and `type x`, then the export should be preserved.\n * - Classes and enums should be preserved (even though they also introduce types).\n * - Imported identifiers should be preserved since we don't have enough information to\n *   rule them out. --isolatedModules disallows re-exports, which catches errors here.\n */\nexport default function getDeclarationInfo(tokens) {\n  const typeDeclarations = new Set();\n  const valueDeclarations = new Set();\n  for (let i = 0; i < tokens.tokens.length; i++) {\n    const token = tokens.tokens[i];\n    if (token.type === tt.name && isTopLevelDeclaration(token)) {\n      if (token.isType) {\n        typeDeclarations.add(tokens.identifierNameForToken(token));\n      } else {\n        valueDeclarations.add(tokens.identifierNameForToken(token));\n      }\n    }\n  }\n  return {typeDeclarations, valueDeclarations};\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAQO,MAAM,yBAAyB;IACpC,kBAAkB,IAAI;IACtB,mBAAmB,IAAI;AACzB;AAae,SAAS,mBAAmB,MAAM;IAC/C,MAAM,mBAAmB,IAAI;IAC7B,MAAM,oBAAoB,IAAI;IAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,CAAC,MAAM,EAAE,IAAK;QAC7C,MAAM,QAAQ,OAAO,MAAM,CAAC,EAAE;QAC9B,IAAI,MAAM,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,IAAI,IAAI,CAAA,GAAA,yKAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ;YAC1D,IAAI,MAAM,MAAM,EAAE;gBAChB,iBAAiB,GAAG,CAAC,OAAO,sBAAsB,CAAC;YACrD,OAAO;gBACL,kBAAkB,GAAG,CAAC,OAAO,sBAAsB,CAAC;YACtD;QACF;IACF;IACA,OAAO;QAAC;QAAkB;IAAiB;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/util/isExportFrom.js"], "sourcesContent": ["import {ContextualKeyword} from \"../parser/tokenizer/keywords\";\nimport {TokenType as tt} from \"../parser/tokenizer/types\";\n\n\n/**\n * Starting at `export {`, look ahead and return `true` if this is an\n * `export {...} from` statement and `false` if this is a plain multi-export.\n */\nexport default function isExportFrom(tokens) {\n  let closeBraceIndex = tokens.currentIndex();\n  while (!tokens.matches1AtIndex(closeBraceIndex, tt.braceR)) {\n    closeBraceIndex++;\n  }\n  return (\n    tokens.matchesContextualAtIndex(closeBraceIndex + 1, ContextualKeyword._from) &&\n    tokens.matches1AtIndex(closeBraceIndex + 2, tt.string)\n  );\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAOe,SAAS,aAAa,MAAM;IACzC,IAAI,kBAAkB,OAAO,YAAY;IACzC,MAAO,CAAC,OAAO,eAAe,CAAC,iBAAiB,yKAAA,CAAA,YAAE,CAAC,MAAM,EAAG;QAC1D;IACF;IACA,OACE,OAAO,wBAAwB,CAAC,kBAAkB,GAAG,4KAAA,CAAA,oBAAiB,CAAC,KAAK,KAC5E,OAAO,eAAe,CAAC,kBAAkB,GAAG,yKAAA,CAAA,YAAE,CAAC,MAAM;AAEzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/util/removeMaybeImportAttributes.js"], "sourcesContent": ["import {ContextualKeyword} from \"../parser/tokenizer/keywords\";\nimport {TokenType as tt} from \"../parser/tokenizer/types\";\n\n\n/**\n * Starting at a potential `with` or (legacy) `assert` token, remove the import\n * attributes if they exist.\n */\nexport function removeMaybeImportAttributes(tokens) {\n  if (\n    tokens.matches2(tt._with, tt.braceL) ||\n    (tokens.matches2(tt.name, tt.braceL) && tokens.matchesContextual(ContextualKeyword._assert))\n  ) {\n    // assert\n    tokens.removeToken();\n    // {\n    tokens.removeToken();\n    tokens.removeBalancedCode();\n    // }\n    tokens.removeToken();\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAOO,SAAS,4BAA4B,MAAM;IAChD,IACE,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,KAAK,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,KAClC,OAAO,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,KAAK,OAAO,iBAAiB,CAAC,4KAAA,CAAA,oBAAiB,CAAC,OAAO,GAC1F;QACA,SAAS;QACT,OAAO,WAAW;QAClB,IAAI;QACJ,OAAO,WAAW;QAClB,OAAO,kBAAkB;QACzB,IAAI;QACJ,OAAO,WAAW;IACpB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/util/shouldElideDefaultExport.js"], "sourcesContent": ["import {TokenType as tt} from \"../parser/tokenizer/types\";\n\n\n\n/**\n * Common method sharing code between CJS and ESM cases, since they're the same here.\n */\nexport default function shouldElideDefaultExport(\n  isTypeScriptTransformEnabled,\n  keepUnusedImports,\n  tokens,\n  declarationInfo,\n) {\n  if (!isTypeScriptTransformEnabled || keepUnusedImports) {\n    return false;\n  }\n  const exportToken = tokens.currentToken();\n  if (exportToken.rhsEndIndex == null) {\n    throw new Error(\"Expected non-null rhsEndIndex on export token.\");\n  }\n  // The export must be of the form `export default a` or `export default a;`.\n  const numTokens = exportToken.rhsEndIndex - tokens.currentIndex();\n  if (\n    numTokens !== 3 &&\n    !(numTokens === 4 && tokens.matches1AtIndex(exportToken.rhsEndIndex - 1, tt.semi))\n  ) {\n    return false;\n  }\n  const identifierToken = tokens.tokenAtRelativeIndex(2);\n  if (identifierToken.type !== tt.name) {\n    return false;\n  }\n  const exportedName = tokens.identifierNameForToken(identifierToken);\n  return (\n    declarationInfo.typeDeclarations.has(exportedName) &&\n    !declarationInfo.valueDeclarations.has(exportedName)\n  );\n}\n"], "names": [], "mappings": ";;;AAAA;;AAOe,SAAS,yBACtB,4BAA4B,EAC5B,iBAAiB,EACjB,MAAM,EACN,eAAe;IAEf,IAAI,CAAC,gCAAgC,mBAAmB;QACtD,OAAO;IACT;IACA,MAAM,cAAc,OAAO,YAAY;IACvC,IAAI,YAAY,WAAW,IAAI,MAAM;QACnC,MAAM,IAAI,MAAM;IAClB;IACA,4EAA4E;IAC5E,MAAM,YAAY,YAAY,WAAW,GAAG,OAAO,YAAY;IAC/D,IACE,cAAc,KACd,CAAC,CAAC,cAAc,KAAK,OAAO,eAAe,CAAC,YAAY,WAAW,GAAG,GAAG,yKAAA,CAAA,YAAE,CAAC,IAAI,CAAC,GACjF;QACA,OAAO;IACT;IACA,MAAM,kBAAkB,OAAO,oBAAoB,CAAC;IACpD,IAAI,gBAAgB,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE;QACpC,OAAO;IACT;IACA,MAAM,eAAe,OAAO,sBAAsB,CAAC;IACnD,OACE,gBAAgB,gBAAgB,CAAC,GAAG,CAAC,iBACrC,CAAC,gBAAgB,iBAAiB,CAAC,GAAG,CAAC;AAE3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 639, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/util/isIdentifier.js"], "sourcesContent": ["import {IS_IDENTIFIER_CHAR, IS_IDENTIFIER_START} from \"../parser/util/identifier\";\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Lexical_grammar\n// Hard-code a list of reserved words rather than trying to use keywords or contextual keywords\n// from the parser, since currently there are various exceptions, like `package` being reserved\n// but unused and various contextual keywords being reserved. Note that we assume that all code\n// compiled by Sucrase is in a module, so strict mode words and await are all considered reserved\n// here.\nconst RESERVED_WORDS = new Set([\n  // Reserved keywords as of ECMAScript 2015\n  \"break\",\n  \"case\",\n  \"catch\",\n  \"class\",\n  \"const\",\n  \"continue\",\n  \"debugger\",\n  \"default\",\n  \"delete\",\n  \"do\",\n  \"else\",\n  \"export\",\n  \"extends\",\n  \"finally\",\n  \"for\",\n  \"function\",\n  \"if\",\n  \"import\",\n  \"in\",\n  \"instanceof\",\n  \"new\",\n  \"return\",\n  \"super\",\n  \"switch\",\n  \"this\",\n  \"throw\",\n  \"try\",\n  \"typeof\",\n  \"var\",\n  \"void\",\n  \"while\",\n  \"with\",\n  \"yield\",\n  // Future reserved keywords\n  \"enum\",\n  \"implements\",\n  \"interface\",\n  \"let\",\n  \"package\",\n  \"private\",\n  \"protected\",\n  \"public\",\n  \"static\",\n  \"await\",\n  // Literals that cannot be used as identifiers\n  \"false\",\n  \"null\",\n  \"true\",\n]);\n\n/**\n * Determine if the given name is a legal variable name.\n *\n * This is needed when transforming TypeScript enums; if an enum key is a valid\n * variable name, it might be referenced later in the enum, so we need to\n * declare a variable.\n */\nexport default function isIdentifier(name) {\n  if (name.length === 0) {\n    return false;\n  }\n  if (!IS_IDENTIFIER_START[name.charCodeAt(0)]) {\n    return false;\n  }\n  for (let i = 1; i < name.length; i++) {\n    if (!IS_IDENTIFIER_CHAR[name.charCodeAt(i)]) {\n      return false;\n    }\n  }\n  return !RESERVED_WORDS.has(name);\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,oFAAoF;AACpF,+FAA+F;AAC/F,+FAA+F;AAC/F,+FAA+F;AAC/F,iGAAiG;AACjG,QAAQ;AACR,MAAM,iBAAiB,IAAI,IAAI;IAC7B,0CAA0C;IAC1C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,2BAA2B;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,8CAA8C;IAC9C;IACA;IACA;CACD;AASc,SAAS,aAAa,IAAI;IACvC,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IACA,IAAI,CAAC,yKAAA,CAAA,sBAAmB,CAAC,KAAK,UAAU,CAAC,GAAG,EAAE;QAC5C,OAAO;IACT;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,IAAI,CAAC,yKAAA,CAAA,qBAAkB,CAAC,KAAK,UAAU,CAAC,GAAG,EAAE;YAC3C,OAAO;QACT;IACF;IACA,OAAO,CAAC,eAAe,GAAG,CAAC;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/util/formatTokens.js"], "sourcesContent": ["import LinesAndColumns from \"lines-and-columns\";\n\n\nimport {formatTokenType} from \"../parser/tokenizer/types\";\n\nexport default function formatTokens(code, tokens) {\n  if (tokens.length === 0) {\n    return \"\";\n  }\n\n  const tokenKeys = Object.keys(tokens[0]).filter(\n    (k) => k !== \"type\" && k !== \"value\" && k !== \"start\" && k !== \"end\" && k !== \"loc\",\n  );\n  const typeKeys = Object.keys(tokens[0].type).filter((k) => k !== \"label\" && k !== \"keyword\");\n\n  const headings = [\"Location\", \"Label\", \"Raw\", ...tokenKeys, ...typeKeys];\n\n  const lines = new LinesAndColumns(code);\n  const rows = [headings, ...tokens.map(getTokenComponents)];\n  const padding = headings.map(() => 0);\n  for (const components of rows) {\n    for (let i = 0; i < components.length; i++) {\n      padding[i] = Math.max(padding[i], components[i].length);\n    }\n  }\n  return rows\n    .map((components) => components.map((component, i) => component.padEnd(padding[i])).join(\" \"))\n    .join(\"\\n\");\n\n  function getTokenComponents(token) {\n    const raw = code.slice(token.start, token.end);\n    return [\n      formatRange(token.start, token.end),\n      formatTokenType(token.type),\n      truncate(String(raw), 14),\n      // @ts-ignore: Intentional dynamic access by key.\n      ...tokenKeys.map((key) => formatValue(token[key], key)),\n      // @ts-ignore: Intentional dynamic access by key.\n      ...typeKeys.map((key) => formatValue(token.type[key], key)),\n    ];\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  function formatValue(value, key) {\n    if (value === true) {\n      return key;\n    } else if (value === false || value === null) {\n      return \"\";\n    } else {\n      return String(value);\n    }\n  }\n\n  function formatRange(start, end) {\n    return `${formatPos(start)}-${formatPos(end)}`;\n  }\n\n  function formatPos(pos) {\n    const location = lines.locationForIndex(pos);\n    if (!location) {\n      return \"Unknown\";\n    } else {\n      return `${location.line + 1}:${location.column + 1}`;\n    }\n  }\n}\n\nfunction truncate(s, length) {\n  if (s.length > length) {\n    return `${s.slice(0, length - 3)}...`;\n  } else {\n    return s;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAGA;;;AAEe,SAAS,aAAa,IAAI,EAAE,MAAM;IAC/C,IAAI,OAAO,MAAM,KAAK,GAAG;QACvB,OAAO;IACT;IAEA,MAAM,YAAY,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAC7C,CAAC,IAAM,MAAM,UAAU,MAAM,WAAW,MAAM,WAAW,MAAM,SAAS,MAAM;IAEhF,MAAM,WAAW,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,IAAM,MAAM,WAAW,MAAM;IAElF,MAAM,WAAW;QAAC;QAAY;QAAS;WAAU;WAAc;KAAS;IAExE,MAAM,QAAQ,IAAI,4JAAA,CAAA,UAAe,CAAC;IAClC,MAAM,OAAO;QAAC;WAAa,OAAO,GAAG,CAAC;KAAoB;IAC1D,MAAM,UAAU,SAAS,GAAG,CAAC,IAAM;IACnC,KAAK,MAAM,cAAc,KAAM;QAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YAC1C,OAAO,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,CAAC,MAAM;QACxD;IACF;IACA,OAAO,KACJ,GAAG,CAAC,CAAC,aAAe,WAAW,GAAG,CAAC,CAAC,WAAW,IAAM,UAAU,MAAM,CAAC,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,MACxF,IAAI,CAAC;;IAER,SAAS,mBAAmB,KAAK;QAC/B,MAAM,MAAM,KAAK,KAAK,CAAC,MAAM,KAAK,EAAE,MAAM,GAAG;QAC7C,OAAO;YACL,YAAY,MAAM,KAAK,EAAE,MAAM,GAAG;YAClC,CAAA,GAAA,yKAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,IAAI;YAC1B,SAAS,OAAO,MAAM;YACtB,iDAAiD;eAC9C,UAAU,GAAG,CAAC,CAAC,MAAQ,YAAY,KAAK,CAAC,IAAI,EAAE;YAClD,iDAAiD;eAC9C,SAAS,GAAG,CAAC,CAAC,MAAQ,YAAY,MAAM,IAAI,CAAC,IAAI,EAAE;SACvD;IACH;IAEA,8DAA8D;IAC9D,SAAS,YAAY,KAAK,EAAE,GAAG;QAC7B,IAAI,UAAU,MAAM;YAClB,OAAO;QACT,OAAO,IAAI,UAAU,SAAS,UAAU,MAAM;YAC5C,OAAO;QACT,OAAO;YACL,OAAO,OAAO;QAChB;IACF;IAEA,SAAS,YAAY,KAAK,EAAE,GAAG;QAC7B,OAAO,GAAG,UAAU,OAAO,CAAC,EAAE,UAAU,MAAM;IAChD;IAEA,SAAS,UAAU,GAAG;QACpB,MAAM,WAAW,MAAM,gBAAgB,CAAC;QACxC,IAAI,CAAC,UAAU;YACb,OAAO;QACT,OAAO;YACL,OAAO,GAAG,SAAS,IAAI,GAAG,EAAE,CAAC,EAAE,SAAS,MAAM,GAAG,GAAG;QACtD;IACF;AACF;AAEA,SAAS,SAAS,CAAC,EAAE,MAAM;IACzB,IAAI,EAAE,MAAM,GAAG,QAAQ;QACrB,OAAO,GAAG,EAAE,KAAK,CAAC,GAAG,SAAS,GAAG,GAAG,CAAC;IACvC,OAAO;QACL,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/util/getTSImportedNames.js"], "sourcesContent": ["import {TokenType as tt} from \"../parser/tokenizer/types\";\n\nimport getImportExportSpecifierInfo from \"./getImportExportSpecifierInfo\";\n\n/**\n * Special case code to scan for imported names in ESM TypeScript. We need to do this so we can\n * properly get globals so we can compute shadowed globals.\n *\n * This is similar to logic in CJSImportProcessor, but trimmed down to avoid logic with CJS\n * replacement and flow type imports.\n */\nexport default function getTSImportedNames(tokens) {\n  const importedNames = new Set();\n  for (let i = 0; i < tokens.tokens.length; i++) {\n    if (\n      tokens.matches1AtIndex(i, tt._import) &&\n      !tokens.matches3AtIndex(i, tt._import, tt.name, tt.eq)\n    ) {\n      collectNamesForImport(tokens, i, importedNames);\n    }\n  }\n  return importedNames;\n}\n\nfunction collectNamesForImport(\n  tokens,\n  index,\n  importedNames,\n) {\n  index++;\n\n  if (tokens.matches1AtIndex(index, tt.parenL)) {\n    // Dynamic import, so nothing to do\n    return;\n  }\n\n  if (tokens.matches1AtIndex(index, tt.name)) {\n    importedNames.add(tokens.identifierNameAtIndex(index));\n    index++;\n    if (tokens.matches1AtIndex(index, tt.comma)) {\n      index++;\n    }\n  }\n\n  if (tokens.matches1AtIndex(index, tt.star)) {\n    // * as\n    index += 2;\n    importedNames.add(tokens.identifierNameAtIndex(index));\n    index++;\n  }\n\n  if (tokens.matches1AtIndex(index, tt.braceL)) {\n    index++;\n    collectNamesForNamedImport(tokens, index, importedNames);\n  }\n}\n\nfunction collectNamesForNamedImport(\n  tokens,\n  index,\n  importedNames,\n) {\n  while (true) {\n    if (tokens.matches1AtIndex(index, tt.braceR)) {\n      return;\n    }\n\n    const specifierInfo = getImportExportSpecifierInfo(tokens, index);\n    index = specifierInfo.endIndex;\n    if (!specifierInfo.isType) {\n      importedNames.add(specifierInfo.rightName);\n    }\n\n    if (tokens.matches2AtIndex(index, tt.comma, tt.braceR)) {\n      return;\n    } else if (tokens.matches1AtIndex(index, tt.braceR)) {\n      return;\n    } else if (tokens.matches1AtIndex(index, tt.comma)) {\n      index++;\n    } else {\n      throw new Error(`Unexpected token: ${JSON.stringify(tokens.tokens[index])}`);\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AASe,SAAS,mBAAmB,MAAM;IAC/C,MAAM,gBAAgB,IAAI;IAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,CAAC,MAAM,EAAE,IAAK;QAC7C,IACE,OAAO,eAAe,CAAC,GAAG,yKAAA,CAAA,YAAE,CAAC,OAAO,KACpC,CAAC,OAAO,eAAe,CAAC,GAAG,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,EAAE,GACrD;YACA,sBAAsB,QAAQ,GAAG;QACnC;IACF;IACA,OAAO;AACT;AAEA,SAAS,sBACP,MAAM,EACN,KAAK,EACL,aAAa;IAEb;IAEA,IAAI,OAAO,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;QAC5C,mCAAmC;QACnC;IACF;IAEA,IAAI,OAAO,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;QAC1C,cAAc,GAAG,CAAC,OAAO,qBAAqB,CAAC;QAC/C;QACA,IAAI,OAAO,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;YAC3C;QACF;IACF;IAEA,IAAI,OAAO,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;QAC1C,OAAO;QACP,SAAS;QACT,cAAc,GAAG,CAAC,OAAO,qBAAqB,CAAC;QAC/C;IACF;IAEA,IAAI,OAAO,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;QAC5C;QACA,2BAA2B,QAAQ,OAAO;IAC5C;AACF;AAEA,SAAS,2BACP,MAAM,EACN,KAAK,EACL,aAAa;IAEb,MAAO,KAAM;QACX,IAAI,OAAO,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YAC5C;QACF;QAEA,MAAM,gBAAgB,CAAA,GAAA,iLAAA,CAAA,UAA4B,AAAD,EAAE,QAAQ;QAC3D,QAAQ,cAAc,QAAQ;QAC9B,IAAI,CAAC,cAAc,MAAM,EAAE;YACzB,cAAc,GAAG,CAAC,cAAc,SAAS;QAC3C;QAEA,IAAI,OAAO,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,KAAK,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YACtD;QACF,OAAO,IAAI,OAAO,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YACnD;QACF,OAAO,IAAI,OAAO,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;YAClD;QACF,OAAO;YACL,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,KAAK,SAAS,CAAC,OAAO,MAAM,CAAC,MAAM,GAAG;QAC7E;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/CJSImportProcessor.js"], "sourcesContent": ["\n\n\nimport {isDeclaration} from \"./parser/tokenizer\";\nimport {ContextualKeyword} from \"./parser/tokenizer/keywords\";\nimport {TokenType as tt} from \"./parser/tokenizer/types\";\n\nimport getImportExportSpecifierInfo from \"./util/getImportExportSpecifierInfo\";\nimport {getNonTypeIdentifiers} from \"./util/getNonTypeIdentifiers\";\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Class responsible for preprocessing and bookkeeping import and export declarations within the\n * file.\n *\n * TypeScript uses a simpler mechanism that does not use functions like interopRequireDefault and\n * interopRequireWildcard, so we also allow that mode for compatibility.\n */\nexport default class CJSImportProcessor {\n   __init() {this.nonTypeIdentifiers = new Set()}\n   __init2() {this.importInfoByPath = new Map()}\n   __init3() {this.importsToReplace = new Map()}\n   __init4() {this.identifierReplacements = new Map()}\n   __init5() {this.exportBindingsByLocalName = new Map()}\n\n  constructor(\n     nameManager,\n     tokens,\n     enableLegacyTypeScriptModuleInterop,\n     options,\n     isTypeScriptTransformEnabled,\n     keepUnusedImports,\n     helperManager,\n  ) {;this.nameManager = nameManager;this.tokens = tokens;this.enableLegacyTypeScriptModuleInterop = enableLegacyTypeScriptModuleInterop;this.options = options;this.isTypeScriptTransformEnabled = isTypeScriptTransformEnabled;this.keepUnusedImports = keepUnusedImports;this.helperManager = helperManager;CJSImportProcessor.prototype.__init.call(this);CJSImportProcessor.prototype.__init2.call(this);CJSImportProcessor.prototype.__init3.call(this);CJSImportProcessor.prototype.__init4.call(this);CJSImportProcessor.prototype.__init5.call(this);}\n\n  preprocessTokens() {\n    for (let i = 0; i < this.tokens.tokens.length; i++) {\n      if (\n        this.tokens.matches1AtIndex(i, tt._import) &&\n        !this.tokens.matches3AtIndex(i, tt._import, tt.name, tt.eq)\n      ) {\n        this.preprocessImportAtIndex(i);\n      }\n      if (\n        this.tokens.matches1AtIndex(i, tt._export) &&\n        !this.tokens.matches2AtIndex(i, tt._export, tt.eq)\n      ) {\n        this.preprocessExportAtIndex(i);\n      }\n    }\n    this.generateImportReplacements();\n  }\n\n  /**\n   * In TypeScript, import statements that only import types should be removed.\n   * This includes `import {} from 'foo';`, but not `import 'foo';`.\n   */\n  pruneTypeOnlyImports() {\n    this.nonTypeIdentifiers = getNonTypeIdentifiers(this.tokens, this.options);\n    for (const [path, importInfo] of this.importInfoByPath.entries()) {\n      if (\n        importInfo.hasBareImport ||\n        importInfo.hasStarExport ||\n        importInfo.exportStarNames.length > 0 ||\n        importInfo.namedExports.length > 0\n      ) {\n        continue;\n      }\n      const names = [\n        ...importInfo.defaultNames,\n        ...importInfo.wildcardNames,\n        ...importInfo.namedImports.map(({localName}) => localName),\n      ];\n      if (names.every((name) => this.shouldAutomaticallyElideImportedName(name))) {\n        this.importsToReplace.set(path, \"\");\n      }\n    }\n  }\n\n  shouldAutomaticallyElideImportedName(name) {\n    return (\n      this.isTypeScriptTransformEnabled &&\n      !this.keepUnusedImports &&\n      !this.nonTypeIdentifiers.has(name)\n    );\n  }\n\n   generateImportReplacements() {\n    for (const [path, importInfo] of this.importInfoByPath.entries()) {\n      const {\n        defaultNames,\n        wildcardNames,\n        namedImports,\n        namedExports,\n        exportStarNames,\n        hasStarExport,\n      } = importInfo;\n\n      if (\n        defaultNames.length === 0 &&\n        wildcardNames.length === 0 &&\n        namedImports.length === 0 &&\n        namedExports.length === 0 &&\n        exportStarNames.length === 0 &&\n        !hasStarExport\n      ) {\n        // Import is never used, so don't even assign a name.\n        this.importsToReplace.set(path, `require('${path}');`);\n        continue;\n      }\n\n      const primaryImportName = this.getFreeIdentifierForPath(path);\n      let secondaryImportName;\n      if (this.enableLegacyTypeScriptModuleInterop) {\n        secondaryImportName = primaryImportName;\n      } else {\n        secondaryImportName =\n          wildcardNames.length > 0 ? wildcardNames[0] : this.getFreeIdentifierForPath(path);\n      }\n      let requireCode = `var ${primaryImportName} = require('${path}');`;\n      if (wildcardNames.length > 0) {\n        for (const wildcardName of wildcardNames) {\n          const moduleExpr = this.enableLegacyTypeScriptModuleInterop\n            ? primaryImportName\n            : `${this.helperManager.getHelperName(\"interopRequireWildcard\")}(${primaryImportName})`;\n          requireCode += ` var ${wildcardName} = ${moduleExpr};`;\n        }\n      } else if (exportStarNames.length > 0 && secondaryImportName !== primaryImportName) {\n        requireCode += ` var ${secondaryImportName} = ${this.helperManager.getHelperName(\n          \"interopRequireWildcard\",\n        )}(${primaryImportName});`;\n      } else if (defaultNames.length > 0 && secondaryImportName !== primaryImportName) {\n        requireCode += ` var ${secondaryImportName} = ${this.helperManager.getHelperName(\n          \"interopRequireDefault\",\n        )}(${primaryImportName});`;\n      }\n\n      for (const {importedName, localName} of namedExports) {\n        requireCode += ` ${this.helperManager.getHelperName(\n          \"createNamedExportFrom\",\n        )}(${primaryImportName}, '${localName}', '${importedName}');`;\n      }\n      for (const exportStarName of exportStarNames) {\n        requireCode += ` exports.${exportStarName} = ${secondaryImportName};`;\n      }\n      if (hasStarExport) {\n        requireCode += ` ${this.helperManager.getHelperName(\n          \"createStarExport\",\n        )}(${primaryImportName});`;\n      }\n\n      this.importsToReplace.set(path, requireCode);\n\n      for (const defaultName of defaultNames) {\n        this.identifierReplacements.set(defaultName, `${secondaryImportName}.default`);\n      }\n      for (const {importedName, localName} of namedImports) {\n        this.identifierReplacements.set(localName, `${primaryImportName}.${importedName}`);\n      }\n    }\n  }\n\n  getFreeIdentifierForPath(path) {\n    const components = path.split(\"/\");\n    const lastComponent = components[components.length - 1];\n    const baseName = lastComponent.replace(/\\W/g, \"\");\n    return this.nameManager.claimFreeName(`_${baseName}`);\n  }\n\n   preprocessImportAtIndex(index) {\n    const defaultNames = [];\n    const wildcardNames = [];\n    const namedImports = [];\n\n    index++;\n    if (\n      (this.tokens.matchesContextualAtIndex(index, ContextualKeyword._type) ||\n        this.tokens.matches1AtIndex(index, tt._typeof)) &&\n      !this.tokens.matches1AtIndex(index + 1, tt.comma) &&\n      !this.tokens.matchesContextualAtIndex(index + 1, ContextualKeyword._from)\n    ) {\n      // import type declaration, so no need to process anything.\n      return;\n    }\n\n    if (this.tokens.matches1AtIndex(index, tt.parenL)) {\n      // Dynamic import, so nothing to do\n      return;\n    }\n\n    if (this.tokens.matches1AtIndex(index, tt.name)) {\n      defaultNames.push(this.tokens.identifierNameAtIndex(index));\n      index++;\n      if (this.tokens.matches1AtIndex(index, tt.comma)) {\n        index++;\n      }\n    }\n\n    if (this.tokens.matches1AtIndex(index, tt.star)) {\n      // * as\n      index += 2;\n      wildcardNames.push(this.tokens.identifierNameAtIndex(index));\n      index++;\n    }\n\n    if (this.tokens.matches1AtIndex(index, tt.braceL)) {\n      const result = this.getNamedImports(index + 1);\n      index = result.newIndex;\n\n      for (const namedImport of result.namedImports) {\n        // Treat {default as X} as a default import to ensure usage of require interop helper\n        if (namedImport.importedName === \"default\") {\n          defaultNames.push(namedImport.localName);\n        } else {\n          namedImports.push(namedImport);\n        }\n      }\n    }\n\n    if (this.tokens.matchesContextualAtIndex(index, ContextualKeyword._from)) {\n      index++;\n    }\n\n    if (!this.tokens.matches1AtIndex(index, tt.string)) {\n      throw new Error(\"Expected string token at the end of import statement.\");\n    }\n    const path = this.tokens.stringValueAtIndex(index);\n    const importInfo = this.getImportInfo(path);\n    importInfo.defaultNames.push(...defaultNames);\n    importInfo.wildcardNames.push(...wildcardNames);\n    importInfo.namedImports.push(...namedImports);\n    if (defaultNames.length === 0 && wildcardNames.length === 0 && namedImports.length === 0) {\n      importInfo.hasBareImport = true;\n    }\n  }\n\n   preprocessExportAtIndex(index) {\n    if (\n      this.tokens.matches2AtIndex(index, tt._export, tt._var) ||\n      this.tokens.matches2AtIndex(index, tt._export, tt._let) ||\n      this.tokens.matches2AtIndex(index, tt._export, tt._const)\n    ) {\n      this.preprocessVarExportAtIndex(index);\n    } else if (\n      this.tokens.matches2AtIndex(index, tt._export, tt._function) ||\n      this.tokens.matches2AtIndex(index, tt._export, tt._class)\n    ) {\n      const exportName = this.tokens.identifierNameAtIndex(index + 2);\n      this.addExportBinding(exportName, exportName);\n    } else if (this.tokens.matches3AtIndex(index, tt._export, tt.name, tt._function)) {\n      const exportName = this.tokens.identifierNameAtIndex(index + 3);\n      this.addExportBinding(exportName, exportName);\n    } else if (this.tokens.matches2AtIndex(index, tt._export, tt.braceL)) {\n      this.preprocessNamedExportAtIndex(index);\n    } else if (this.tokens.matches2AtIndex(index, tt._export, tt.star)) {\n      this.preprocessExportStarAtIndex(index);\n    }\n  }\n\n   preprocessVarExportAtIndex(index) {\n    let depth = 0;\n    // Handle cases like `export let {x} = y;`, starting at the open-brace in that case.\n    for (let i = index + 2; ; i++) {\n      if (\n        this.tokens.matches1AtIndex(i, tt.braceL) ||\n        this.tokens.matches1AtIndex(i, tt.dollarBraceL) ||\n        this.tokens.matches1AtIndex(i, tt.bracketL)\n      ) {\n        depth++;\n      } else if (\n        this.tokens.matches1AtIndex(i, tt.braceR) ||\n        this.tokens.matches1AtIndex(i, tt.bracketR)\n      ) {\n        depth--;\n      } else if (depth === 0 && !this.tokens.matches1AtIndex(i, tt.name)) {\n        break;\n      } else if (this.tokens.matches1AtIndex(1, tt.eq)) {\n        const endIndex = this.tokens.currentToken().rhsEndIndex;\n        if (endIndex == null) {\n          throw new Error(\"Expected = token with an end index.\");\n        }\n        i = endIndex - 1;\n      } else {\n        const token = this.tokens.tokens[i];\n        if (isDeclaration(token)) {\n          const exportName = this.tokens.identifierNameAtIndex(i);\n          this.identifierReplacements.set(exportName, `exports.${exportName}`);\n        }\n      }\n    }\n  }\n\n  /**\n   * Walk this export statement just in case it's an export...from statement.\n   * If it is, combine it into the import info for that path. Otherwise, just\n   * bail out; it'll be handled later.\n   */\n   preprocessNamedExportAtIndex(index) {\n    // export {\n    index += 2;\n    const {newIndex, namedImports} = this.getNamedImports(index);\n    index = newIndex;\n\n    if (this.tokens.matchesContextualAtIndex(index, ContextualKeyword._from)) {\n      index++;\n    } else {\n      // Reinterpret \"a as b\" to be local/exported rather than imported/local.\n      for (const {importedName: localName, localName: exportedName} of namedImports) {\n        this.addExportBinding(localName, exportedName);\n      }\n      return;\n    }\n\n    if (!this.tokens.matches1AtIndex(index, tt.string)) {\n      throw new Error(\"Expected string token at the end of import statement.\");\n    }\n    const path = this.tokens.stringValueAtIndex(index);\n    const importInfo = this.getImportInfo(path);\n    importInfo.namedExports.push(...namedImports);\n  }\n\n   preprocessExportStarAtIndex(index) {\n    let exportedName = null;\n    if (this.tokens.matches3AtIndex(index, tt._export, tt.star, tt._as)) {\n      // export * as\n      index += 3;\n      exportedName = this.tokens.identifierNameAtIndex(index);\n      // foo from\n      index += 2;\n    } else {\n      // export * from\n      index += 3;\n    }\n    if (!this.tokens.matches1AtIndex(index, tt.string)) {\n      throw new Error(\"Expected string token at the end of star export statement.\");\n    }\n    const path = this.tokens.stringValueAtIndex(index);\n    const importInfo = this.getImportInfo(path);\n    if (exportedName !== null) {\n      importInfo.exportStarNames.push(exportedName);\n    } else {\n      importInfo.hasStarExport = true;\n    }\n  }\n\n   getNamedImports(index) {\n    const namedImports = [];\n    while (true) {\n      if (this.tokens.matches1AtIndex(index, tt.braceR)) {\n        index++;\n        break;\n      }\n\n      const specifierInfo = getImportExportSpecifierInfo(this.tokens, index);\n      index = specifierInfo.endIndex;\n      if (!specifierInfo.isType) {\n        namedImports.push({\n          importedName: specifierInfo.leftName,\n          localName: specifierInfo.rightName,\n        });\n      }\n\n      if (this.tokens.matches2AtIndex(index, tt.comma, tt.braceR)) {\n        index += 2;\n        break;\n      } else if (this.tokens.matches1AtIndex(index, tt.braceR)) {\n        index++;\n        break;\n      } else if (this.tokens.matches1AtIndex(index, tt.comma)) {\n        index++;\n      } else {\n        throw new Error(`Unexpected token: ${JSON.stringify(this.tokens.tokens[index])}`);\n      }\n    }\n    return {newIndex: index, namedImports};\n  }\n\n  /**\n   * Get a mutable import info object for this path, creating one if it doesn't\n   * exist yet.\n   */\n   getImportInfo(path) {\n    const existingInfo = this.importInfoByPath.get(path);\n    if (existingInfo) {\n      return existingInfo;\n    }\n    const newInfo = {\n      defaultNames: [],\n      wildcardNames: [],\n      namedImports: [],\n      namedExports: [],\n      hasBareImport: false,\n      exportStarNames: [],\n      hasStarExport: false,\n    };\n    this.importInfoByPath.set(path, newInfo);\n    return newInfo;\n  }\n\n   addExportBinding(localName, exportedName) {\n    if (!this.exportBindingsByLocalName.has(localName)) {\n      this.exportBindingsByLocalName.set(localName, []);\n    }\n    this.exportBindingsByLocalName.get(localName).push(exportedName);\n  }\n\n  /**\n   * Return the code to use for the import for this path, or the empty string if\n   * the code has already been \"claimed\" by a previous import.\n   */\n  claimImportCode(importPath) {\n    const result = this.importsToReplace.get(importPath);\n    this.importsToReplace.set(importPath, \"\");\n    return result || \"\";\n  }\n\n  getIdentifierReplacement(identifierName) {\n    return this.identifierReplacements.get(identifierName) || null;\n  }\n\n  /**\n   * Return a string like `exports.foo = exports.bar`.\n   */\n  resolveExportBinding(assignedName) {\n    const exportedNames = this.exportBindingsByLocalName.get(assignedName);\n    if (!exportedNames || exportedNames.length === 0) {\n      return null;\n    }\n    return exportedNames.map((exportedName) => `exports.${exportedName}`).join(\" = \");\n  }\n\n  /**\n   * Return all imported/exported names where we might be interested in whether usages of those\n   * names are shadowed.\n   */\n  getGlobalNames() {\n    return new Set([\n      ...this.identifierReplacements.keys(),\n      ...this.exportBindingsByLocalName.keys(),\n    ]);\n  }\n}\n"], "names": [], "mappings": ";;;AAGA;AACA;AACA;AAEA;AACA;;;;;;AAwBe,MAAM;IAClB,SAAS;QAAC,IAAI,CAAC,kBAAkB,GAAG,IAAI;IAAK;IAC7C,UAAU;QAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI;IAAK;IAC5C,UAAU;QAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI;IAAK;IAC5C,UAAU;QAAC,IAAI,CAAC,sBAAsB,GAAG,IAAI;IAAK;IAClD,UAAU;QAAC,IAAI,CAAC,yBAAyB,GAAG,IAAI;IAAK;IAEtD,YACG,WAAW,EACX,MAAM,EACN,mCAAmC,EACnC,OAAO,EACP,4BAA4B,EAC5B,iBAAiB,EACjB,aAAa,CACd;;QAAE,IAAI,CAAC,WAAW,GAAG;QAAY,IAAI,CAAC,MAAM,GAAG;QAAO,IAAI,CAAC,mCAAmC,GAAG;QAAoC,IAAI,CAAC,OAAO,GAAG;QAAQ,IAAI,CAAC,4BAA4B,GAAG;QAA6B,IAAI,CAAC,iBAAiB,GAAG;QAAkB,IAAI,CAAC,aAAa,GAAG;QAAc,mBAAmB,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;QAAE,mBAAmB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;QAAE,mBAAmB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;QAAE,mBAAmB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;QAAE,mBAAmB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;IAAE;IAE5hB,mBAAmB;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,IAAK;YAClD,IACE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,yKAAA,CAAA,YAAE,CAAC,OAAO,KACzC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,EAAE,GAC1D;gBACA,IAAI,CAAC,uBAAuB,CAAC;YAC/B;YACA,IACE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,yKAAA,CAAA,YAAE,CAAC,OAAO,KACzC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,EAAE,GACjD;gBACA,IAAI,CAAC,uBAAuB,CAAC;YAC/B;QACF;QACA,IAAI,CAAC,0BAA0B;IACjC;IAEA;;;GAGC,GACD,uBAAuB;QACrB,IAAI,CAAC,kBAAkB,GAAG,CAAA,GAAA,0KAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO;QACzE,KAAK,MAAM,CAAC,MAAM,WAAW,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAI;YAChE,IACE,WAAW,aAAa,IACxB,WAAW,aAAa,IACxB,WAAW,eAAe,CAAC,MAAM,GAAG,KACpC,WAAW,YAAY,CAAC,MAAM,GAAG,GACjC;gBACA;YACF;YACA,MAAM,QAAQ;mBACT,WAAW,YAAY;mBACvB,WAAW,aAAa;mBACxB,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,EAAC,SAAS,EAAC,GAAK;aACjD;YACD,IAAI,MAAM,KAAK,CAAC,CAAC,OAAS,IAAI,CAAC,oCAAoC,CAAC,QAAQ;gBAC1E,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM;YAClC;QACF;IACF;IAEA,qCAAqC,IAAI,EAAE;QACzC,OACE,IAAI,CAAC,4BAA4B,IACjC,CAAC,IAAI,CAAC,iBAAiB,IACvB,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;IAEjC;IAEC,6BAA6B;QAC5B,KAAK,MAAM,CAAC,MAAM,WAAW,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAI;YAChE,MAAM,EACJ,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,eAAe,EACf,aAAa,EACd,GAAG;YAEJ,IACE,aAAa,MAAM,KAAK,KACxB,cAAc,MAAM,KAAK,KACzB,aAAa,MAAM,KAAK,KACxB,aAAa,MAAM,KAAK,KACxB,gBAAgB,MAAM,KAAK,KAC3B,CAAC,eACD;gBACA,qDAAqD;gBACrD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,GAAG,CAAC;gBACrD;YACF;YAEA,MAAM,oBAAoB,IAAI,CAAC,wBAAwB,CAAC;YACxD,IAAI;YACJ,IAAI,IAAI,CAAC,mCAAmC,EAAE;gBAC5C,sBAAsB;YACxB,OAAO;gBACL,sBACE,cAAc,MAAM,GAAG,IAAI,aAAa,CAAC,EAAE,GAAG,IAAI,CAAC,wBAAwB,CAAC;YAChF;YACA,IAAI,cAAc,CAAC,IAAI,EAAE,kBAAkB,YAAY,EAAE,KAAK,GAAG,CAAC;YAClE,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,KAAK,MAAM,gBAAgB,cAAe;oBACxC,MAAM,aAAa,IAAI,CAAC,mCAAmC,GACvD,oBACA,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,0BAA0B,CAAC,EAAE,kBAAkB,CAAC,CAAC;oBACzF,eAAe,CAAC,KAAK,EAAE,aAAa,GAAG,EAAE,WAAW,CAAC,CAAC;gBACxD;YACF,OAAO,IAAI,gBAAgB,MAAM,GAAG,KAAK,wBAAwB,mBAAmB;gBAClF,eAAe,CAAC,KAAK,EAAE,oBAAoB,GAAG,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa,CAC9E,0BACA,CAAC,EAAE,kBAAkB,EAAE,CAAC;YAC5B,OAAO,IAAI,aAAa,MAAM,GAAG,KAAK,wBAAwB,mBAAmB;gBAC/E,eAAe,CAAC,KAAK,EAAE,oBAAoB,GAAG,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa,CAC9E,yBACA,CAAC,EAAE,kBAAkB,EAAE,CAAC;YAC5B;YAEA,KAAK,MAAM,EAAC,YAAY,EAAE,SAAS,EAAC,IAAI,aAAc;gBACpD,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa,CACjD,yBACA,CAAC,EAAE,kBAAkB,GAAG,EAAE,UAAU,IAAI,EAAE,aAAa,GAAG,CAAC;YAC/D;YACA,KAAK,MAAM,kBAAkB,gBAAiB;gBAC5C,eAAe,CAAC,SAAS,EAAE,eAAe,GAAG,EAAE,oBAAoB,CAAC,CAAC;YACvE;YACA,IAAI,eAAe;gBACjB,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa,CACjD,oBACA,CAAC,EAAE,kBAAkB,EAAE,CAAC;YAC5B;YAEA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM;YAEhC,KAAK,MAAM,eAAe,aAAc;gBACtC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,aAAa,GAAG,oBAAoB,QAAQ,CAAC;YAC/E;YACA,KAAK,MAAM,EAAC,YAAY,EAAE,SAAS,EAAC,IAAI,aAAc;gBACpD,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,WAAW,GAAG,kBAAkB,CAAC,EAAE,cAAc;YACnF;QACF;IACF;IAEA,yBAAyB,IAAI,EAAE;QAC7B,MAAM,aAAa,KAAK,KAAK,CAAC;QAC9B,MAAM,gBAAgB,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE;QACvD,MAAM,WAAW,cAAc,OAAO,CAAC,OAAO;QAC9C,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,UAAU;IACtD;IAEC,wBAAwB,KAAK,EAAE;QAC9B,MAAM,eAAe,EAAE;QACvB,MAAM,gBAAgB,EAAE;QACxB,MAAM,eAAe,EAAE;QAEvB;QACA,IACE,CAAC,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,OAAO,4KAAA,CAAA,oBAAiB,CAAC,KAAK,KAClE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,OAAO,CAAC,KAChD,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,GAAG,yKAAA,CAAA,YAAE,CAAC,KAAK,KAChD,CAAC,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,QAAQ,GAAG,4KAAA,CAAA,oBAAiB,CAAC,KAAK,GACxE;YACA,2DAA2D;YAC3D;QACF;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YACjD,mCAAmC;YACnC;QACF;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;YAC/C,aAAa,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;YACpD;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;gBAChD;YACF;QACF;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;YAC/C,OAAO;YACP,SAAS;YACT,cAAc,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;YACrD;QACF;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YACjD,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC,QAAQ;YAC5C,QAAQ,OAAO,QAAQ;YAEvB,KAAK,MAAM,eAAe,OAAO,YAAY,CAAE;gBAC7C,qFAAqF;gBACrF,IAAI,YAAY,YAAY,KAAK,WAAW;oBAC1C,aAAa,IAAI,CAAC,YAAY,SAAS;gBACzC,OAAO;oBACL,aAAa,IAAI,CAAC;gBACpB;YACF;QACF;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,OAAO,4KAAA,CAAA,oBAAiB,CAAC,KAAK,GAAG;YACxE;QACF;QAEA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YAClD,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAC5C,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC;QACtC,WAAW,YAAY,CAAC,IAAI,IAAI;QAChC,WAAW,aAAa,CAAC,IAAI,IAAI;QACjC,WAAW,YAAY,CAAC,IAAI,IAAI;QAChC,IAAI,aAAa,MAAM,KAAK,KAAK,cAAc,MAAM,KAAK,KAAK,aAAa,MAAM,KAAK,GAAG;YACxF,WAAW,aAAa,GAAG;QAC7B;IACF;IAEC,wBAAwB,KAAK,EAAE;QAC9B,IACE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,KACtD,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,KACtD,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GACxD;YACA,IAAI,CAAC,0BAA0B,CAAC;QAClC,OAAO,IACL,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,SAAS,KAC3D,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GACxD;YACA,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,QAAQ;YAC7D,IAAI,CAAC,gBAAgB,CAAC,YAAY;QACpC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,SAAS,GAAG;YAChF,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,QAAQ;YAC7D,IAAI,CAAC,gBAAgB,CAAC,YAAY;QACpC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YACpE,IAAI,CAAC,4BAA4B,CAAC;QACpC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;YAClE,IAAI,CAAC,2BAA2B,CAAC;QACnC;IACF;IAEC,2BAA2B,KAAK,EAAE;QACjC,IAAI,QAAQ;QACZ,oFAAoF;QACpF,IAAK,IAAI,IAAI,QAAQ,IAAK,IAAK;YAC7B,IACE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,yKAAA,CAAA,YAAE,CAAC,MAAM,KACxC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,yKAAA,CAAA,YAAE,CAAC,YAAY,KAC9C,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,yKAAA,CAAA,YAAE,CAAC,QAAQ,GAC1C;gBACA;YACF,OAAO,IACL,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,yKAAA,CAAA,YAAE,CAAC,MAAM,KACxC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,yKAAA,CAAA,YAAE,CAAC,QAAQ,GAC1C;gBACA;YACF,OAAO,IAAI,UAAU,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,yKAAA,CAAA,YAAE,CAAC,IAAI,GAAG;gBAClE;YACF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,yKAAA,CAAA,YAAE,CAAC,EAAE,GAAG;gBAChD,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,WAAW;gBACvD,IAAI,YAAY,MAAM;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBACA,IAAI,WAAW;YACjB,OAAO;gBACL,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;gBACnC,IAAI,CAAA,GAAA,yKAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;oBACxB,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;oBACrD,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,YAAY;gBACrE;YACF;QACF;IACF;IAEA;;;;GAIC,GACA,6BAA6B,KAAK,EAAE;QACnC,WAAW;QACX,SAAS;QACT,MAAM,EAAC,QAAQ,EAAE,YAAY,EAAC,GAAG,IAAI,CAAC,eAAe,CAAC;QACtD,QAAQ;QAER,IAAI,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,OAAO,4KAAA,CAAA,oBAAiB,CAAC,KAAK,GAAG;YACxE;QACF,OAAO;YACL,wEAAwE;YACxE,KAAK,MAAM,EAAC,cAAc,SAAS,EAAE,WAAW,YAAY,EAAC,IAAI,aAAc;gBAC7E,IAAI,CAAC,gBAAgB,CAAC,WAAW;YACnC;YACA;QACF;QAEA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YAClD,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAC5C,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC;QACtC,WAAW,YAAY,CAAC,IAAI,IAAI;IAClC;IAEC,4BAA4B,KAAK,EAAE;QAClC,IAAI,eAAe;QACnB,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE,yKAAA,CAAA,YAAE,CAAC,IAAI,EAAE,yKAAA,CAAA,YAAE,CAAC,GAAG,GAAG;YACnE,cAAc;YACd,SAAS;YACT,eAAe,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;YACjD,WAAW;YACX,SAAS;QACX,OAAO;YACL,gBAAgB;YAChB,SAAS;QACX;QACA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;YAClD,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAC5C,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC;QACtC,IAAI,iBAAiB,MAAM;YACzB,WAAW,eAAe,CAAC,IAAI,CAAC;QAClC,OAAO;YACL,WAAW,aAAa,GAAG;QAC7B;IACF;IAEC,gBAAgB,KAAK,EAAE;QACtB,MAAM,eAAe,EAAE;QACvB,MAAO,KAAM;YACX,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBACjD;gBACA;YACF;YAEA,MAAM,gBAAgB,CAAA,GAAA,iLAAA,CAAA,UAA4B,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE;YAChE,QAAQ,cAAc,QAAQ;YAC9B,IAAI,CAAC,cAAc,MAAM,EAAE;gBACzB,aAAa,IAAI,CAAC;oBAChB,cAAc,cAAc,QAAQ;oBACpC,WAAW,cAAc,SAAS;gBACpC;YACF;YAEA,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,KAAK,EAAE,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBAC3D,SAAS;gBACT;YACF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBACxD;gBACA;YACF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,KAAK,GAAG;gBACvD;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,KAAK,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG;YAClF;QACF;QACA,OAAO;YAAC,UAAU;YAAO;QAAY;IACvC;IAEA;;;GAGC,GACA,cAAc,IAAI,EAAE;QACnB,MAAM,eAAe,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;QAC/C,IAAI,cAAc;YAChB,OAAO;QACT;QACA,MAAM,UAAU;YACd,cAAc,EAAE;YAChB,eAAe,EAAE;YACjB,cAAc,EAAE;YAChB,cAAc,EAAE;YAChB,eAAe;YACf,iBAAiB,EAAE;YACnB,eAAe;QACjB;QACA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM;QAChC,OAAO;IACT;IAEC,iBAAiB,SAAS,EAAE,YAAY,EAAE;QACzC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,YAAY;YAClD,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,WAAW,EAAE;QAClD;QACA,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC;IACrD;IAEA;;;GAGC,GACD,gBAAgB,UAAU,EAAE;QAC1B,MAAM,SAAS,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;QACzC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY;QACtC,OAAO,UAAU;IACnB;IAEA,yBAAyB,cAAc,EAAE;QACvC,OAAO,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,mBAAmB;IAC5D;IAEA;;GAEC,GACD,qBAAqB,YAAY,EAAE;QACjC,MAAM,gBAAgB,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC;QACzD,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG;YAChD,OAAO;QACT;QACA,OAAO,cAAc,GAAG,CAAC,CAAC,eAAiB,CAAC,QAAQ,EAAE,cAAc,EAAE,IAAI,CAAC;IAC7E;IAEA;;;GAGC,GACD,iBAAiB;QACf,OAAO,IAAI,IAAI;eACV,IAAI,CAAC,sBAAsB,CAAC,IAAI;eAChC,IAAI,CAAC,yBAAyB,CAAC,IAAI;SACvC;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/computeSourceMap.js"], "sourcesContent": ["import {GenMapping, maybeAddSegment, toEncodedMap} from \"@jridgewell/gen-mapping\";\n\n\n\nimport {charCodes} from \"./parser/util/charcodes\";\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Generate a source map indicating that each line maps directly to the original line,\n * with the tokens in their new positions.\n */\nexport default function computeSourceMap(\n  {code: generatedCode, mappings: rawMappings},\n  filePath,\n  options,\n  source,\n  tokens,\n) {\n  const sourceColumns = computeSourceColumns(source, tokens);\n  const map = new GenMapping({file: options.compiledFilename});\n  let tokenIndex = 0;\n  // currentMapping is the output source index for the current input token being\n  // considered.\n  let currentMapping = rawMappings[0];\n  while (currentMapping === undefined && tokenIndex < rawMappings.length - 1) {\n    tokenIndex++;\n    currentMapping = rawMappings[tokenIndex];\n  }\n  let line = 0;\n  let lineStart = 0;\n  if (currentMapping !== lineStart) {\n    maybeAddSegment(map, line, 0, filePath, line, 0);\n  }\n  for (let i = 0; i < generatedCode.length; i++) {\n    if (i === currentMapping) {\n      const genColumn = currentMapping - lineStart;\n      const sourceColumn = sourceColumns[tokenIndex];\n      maybeAddSegment(map, line, genColumn, filePath, line, sourceColumn);\n      while (\n        (currentMapping === i || currentMapping === undefined) &&\n        tokenIndex < rawMappings.length - 1\n      ) {\n        tokenIndex++;\n        currentMapping = rawMappings[tokenIndex];\n      }\n    }\n    if (generatedCode.charCodeAt(i) === charCodes.lineFeed) {\n      line++;\n      lineStart = i + 1;\n      if (currentMapping !== lineStart) {\n        maybeAddSegment(map, line, 0, filePath, line, 0);\n      }\n    }\n  }\n  const {sourceRoot, sourcesContent, ...sourceMap} = toEncodedMap(map);\n  return sourceMap ;\n}\n\n/**\n * Create an array mapping each token index to the 0-based column of the start\n * position of the token.\n */\nfunction computeSourceColumns(code, tokens) {\n  const sourceColumns = new Array(tokens.length);\n  let tokenIndex = 0;\n  let currentMapping = tokens[tokenIndex].start;\n  let lineStart = 0;\n  for (let i = 0; i < code.length; i++) {\n    if (i === currentMapping) {\n      sourceColumns[tokenIndex] = currentMapping - lineStart;\n      tokenIndex++;\n      currentMapping = tokens[tokenIndex].start;\n    }\n    if (code.charCodeAt(i) === charCodes.lineFeed) {\n      lineStart = i + 1;\n    }\n  }\n  return sourceColumns;\n}\n"], "names": [], "mappings": ";;;AAAA;AAIA;;;AAiBe,SAAS,iBACtB,EAAC,MAAM,aAAa,EAAE,UAAU,WAAW,EAAC,EAC5C,QAAQ,EACR,OAAO,EACP,MAAM,EACN,MAAM;IAEN,MAAM,gBAAgB,qBAAqB,QAAQ;IACnD,MAAM,MAAM,IAAI,4KAAA,CAAA,aAAU,CAAC;QAAC,MAAM,QAAQ,gBAAgB;IAAA;IAC1D,IAAI,aAAa;IACjB,8EAA8E;IAC9E,cAAc;IACd,IAAI,iBAAiB,WAAW,CAAC,EAAE;IACnC,MAAO,mBAAmB,aAAa,aAAa,YAAY,MAAM,GAAG,EAAG;QAC1E;QACA,iBAAiB,WAAW,CAAC,WAAW;IAC1C;IACA,IAAI,OAAO;IACX,IAAI,YAAY;IAChB,IAAI,mBAAmB,WAAW;QAChC,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,MAAM,GAAG,UAAU,MAAM;IAChD;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;QAC7C,IAAI,MAAM,gBAAgB;YACxB,MAAM,YAAY,iBAAiB;YACnC,MAAM,eAAe,aAAa,CAAC,WAAW;YAC9C,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,MAAM,WAAW,UAAU,MAAM;YACtD,MACE,CAAC,mBAAmB,KAAK,mBAAmB,SAAS,KACrD,aAAa,YAAY,MAAM,GAAG,EAClC;gBACA;gBACA,iBAAiB,WAAW,CAAC,WAAW;YAC1C;QACF;QACA,IAAI,cAAc,UAAU,CAAC,OAAO,wKAAA,CAAA,YAAS,CAAC,QAAQ,EAAE;YACtD;YACA,YAAY,IAAI;YAChB,IAAI,mBAAmB,WAAW;gBAChC,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,MAAM,GAAG,UAAU,MAAM;YAChD;QACF;IACF;IACA,MAAM,EAAC,UAAU,EAAE,cAAc,EAAE,GAAG,WAAU,GAAG,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE;IAChE,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,qBAAqB,IAAI,EAAE,MAAM;IACxC,MAAM,gBAAgB,IAAI,MAAM,OAAO,MAAM;IAC7C,IAAI,aAAa;IACjB,IAAI,iBAAiB,MAAM,CAAC,WAAW,CAAC,KAAK;IAC7C,IAAI,YAAY;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,IAAI,MAAM,gBAAgB;YACxB,aAAa,CAAC,WAAW,GAAG,iBAAiB;YAC7C;YACA,iBAAiB,MAAM,CAAC,WAAW,CAAC,KAAK;QAC3C;QACA,IAAI,KAAK,UAAU,CAAC,OAAO,wKAAA,CAAA,YAAS,CAAC,QAAQ,EAAE;YAC7C,YAAY,IAAI;QAClB;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/HelperManager.js"], "sourcesContent": ["\n\nconst HELPERS = {\n  require: `\n    import {createRequire as CREATE_REQUIRE_NAME} from \"module\";\n    const require = CREATE_REQUIRE_NAME(import.meta.url);\n  `,\n  interopRequireWildcard: `\n    function interopRequireWildcard(obj) {\n      if (obj && obj.__esModule) {\n        return obj;\n      } else {\n        var newObj = {};\n        if (obj != null) {\n          for (var key in obj) {\n            if (Object.prototype.hasOwnProperty.call(obj, key)) {\n              newObj[key] = obj[key];\n            }\n          }\n        }\n        newObj.default = obj;\n        return newObj;\n      }\n    }\n  `,\n  interopRequireDefault: `\n    function interopRequireDefault(obj) {\n      return obj && obj.__esModule ? obj : { default: obj };\n    }\n  `,\n  createNamedExportFrom: `\n    function createNamedExportFrom(obj, localName, importedName) {\n      Object.defineProperty(exports, localName, {enumerable: true, configurable: true, get: () => obj[importedName]});\n    }\n  `,\n  // Note that TypeScript and Babel do this differently; TypeScript does a simple existence\n  // check in the exports object and does a plain assignment, whereas Babel uses\n  // defineProperty and builds an object of explicitly-exported names so that star exports can\n  // always take lower precedence. For now, we do the easier TypeScript thing.\n  createStarExport: `\n    function createStarExport(obj) {\n      Object.keys(obj)\n        .filter((key) => key !== \"default\" && key !== \"__esModule\")\n        .forEach((key) => {\n          if (exports.hasOwnProperty(key)) {\n            return;\n          }\n          Object.defineProperty(exports, key, {enumerable: true, configurable: true, get: () => obj[key]});\n        });\n    }\n  `,\n  nullishCoalesce: `\n    function nullishCoalesce(lhs, rhsFn) {\n      if (lhs != null) {\n        return lhs;\n      } else {\n        return rhsFn();\n      }\n    }\n  `,\n  asyncNullishCoalesce: `\n    async function asyncNullishCoalesce(lhs, rhsFn) {\n      if (lhs != null) {\n        return lhs;\n      } else {\n        return await rhsFn();\n      }\n    }\n  `,\n  optionalChain: `\n    function optionalChain(ops) {\n      let lastAccessLHS = undefined;\n      let value = ops[0];\n      let i = 1;\n      while (i < ops.length) {\n        const op = ops[i];\n        const fn = ops[i + 1];\n        i += 2;\n        if ((op === 'optionalAccess' || op === 'optionalCall') && value == null) {\n          return undefined;\n        }\n        if (op === 'access' || op === 'optionalAccess') {\n          lastAccessLHS = value;\n          value = fn(value);\n        } else if (op === 'call' || op === 'optionalCall') {\n          value = fn((...args) => value.call(lastAccessLHS, ...args));\n          lastAccessLHS = undefined;\n        }\n      }\n      return value;\n    }\n  `,\n  asyncOptionalChain: `\n    async function asyncOptionalChain(ops) {\n      let lastAccessLHS = undefined;\n      let value = ops[0];\n      let i = 1;\n      while (i < ops.length) {\n        const op = ops[i];\n        const fn = ops[i + 1];\n        i += 2;\n        if ((op === 'optionalAccess' || op === 'optionalCall') && value == null) {\n          return undefined;\n        }\n        if (op === 'access' || op === 'optionalAccess') {\n          lastAccessLHS = value;\n          value = await fn(value);\n        } else if (op === 'call' || op === 'optionalCall') {\n          value = await fn((...args) => value.call(lastAccessLHS, ...args));\n          lastAccessLHS = undefined;\n        }\n      }\n      return value;\n    }\n  `,\n  optionalChainDelete: `\n    function optionalChainDelete(ops) {\n      const result = OPTIONAL_CHAIN_NAME(ops);\n      return result == null ? true : result;\n    }\n  `,\n  asyncOptionalChainDelete: `\n    async function asyncOptionalChainDelete(ops) {\n      const result = await ASYNC_OPTIONAL_CHAIN_NAME(ops);\n      return result == null ? true : result;\n    }\n  `,\n};\n\nexport class HelperManager {\n  __init() {this.helperNames = {}}\n  __init2() {this.createRequireName = null}\n  constructor( nameManager) {;this.nameManager = nameManager;HelperManager.prototype.__init.call(this);HelperManager.prototype.__init2.call(this);}\n\n  getHelperName(baseName) {\n    let helperName = this.helperNames[baseName];\n    if (helperName) {\n      return helperName;\n    }\n    helperName = this.nameManager.claimFreeName(`_${baseName}`);\n    this.helperNames[baseName] = helperName;\n    return helperName;\n  }\n\n  emitHelpers() {\n    let resultCode = \"\";\n    if (this.helperNames.optionalChainDelete) {\n      this.getHelperName(\"optionalChain\");\n    }\n    if (this.helperNames.asyncOptionalChainDelete) {\n      this.getHelperName(\"asyncOptionalChain\");\n    }\n    for (const [baseName, helperCodeTemplate] of Object.entries(HELPERS)) {\n      const helperName = this.helperNames[baseName];\n      let helperCode = helperCodeTemplate;\n      if (baseName === \"optionalChainDelete\") {\n        helperCode = helperCode.replace(\"OPTIONAL_CHAIN_NAME\", this.helperNames.optionalChain);\n      } else if (baseName === \"asyncOptionalChainDelete\") {\n        helperCode = helperCode.replace(\n          \"ASYNC_OPTIONAL_CHAIN_NAME\",\n          this.helperNames.asyncOptionalChain,\n        );\n      } else if (baseName === \"require\") {\n        if (this.createRequireName === null) {\n          this.createRequireName = this.nameManager.claimFreeName(\"_createRequire\");\n        }\n        helperCode = helperCode.replace(/CREATE_REQUIRE_NAME/g, this.createRequireName);\n      }\n      if (helperName) {\n        resultCode += \" \";\n        resultCode += helperCode.replace(baseName, helperName).replace(/\\s+/g, \" \").trim();\n      }\n    }\n    return resultCode;\n  }\n}\n"], "names": [], "mappings": ";;;AAEA,MAAM,UAAU;IACd,SAAS,CAAC;;;EAGV,CAAC;IACD,wBAAwB,CAAC;;;;;;;;;;;;;;;;;EAiBzB,CAAC;IACD,uBAAuB,CAAC;;;;EAIxB,CAAC;IACD,uBAAuB,CAAC;;;;EAIxB,CAAC;IACD,yFAAyF;IACzF,8EAA8E;IAC9E,4FAA4F;IAC5F,4EAA4E;IAC5E,kBAAkB,CAAC;;;;;;;;;;;EAWnB,CAAC;IACD,iBAAiB,CAAC;;;;;;;;EAQlB,CAAC;IACD,sBAAsB,CAAC;;;;;;;;EAQvB,CAAC;IACD,eAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;EAsBhB,CAAC;IACD,oBAAoB,CAAC;;;;;;;;;;;;;;;;;;;;;;EAsBrB,CAAC;IACD,qBAAqB,CAAC;;;;;EAKtB,CAAC;IACD,0BAA0B,CAAC;;;;;EAK3B,CAAC;AACH;AAEO,MAAM;IACX,SAAS;QAAC,IAAI,CAAC,WAAW,GAAG,CAAC;IAAC;IAC/B,UAAU;QAAC,IAAI,CAAC,iBAAiB,GAAG;IAAI;IACxC,YAAa,WAAW,CAAE;;QAAE,IAAI,CAAC,WAAW,GAAG;QAAY,cAAc,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;QAAE,cAAc,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;IAAE;IAEhJ,cAAc,QAAQ,EAAE;QACtB,IAAI,aAAa,IAAI,CAAC,WAAW,CAAC,SAAS;QAC3C,IAAI,YAAY;YACd,OAAO;QACT;QACA,aAAa,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,UAAU;QAC1D,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG;QAC7B,OAAO;IACT;IAEA,cAAc;QACZ,IAAI,aAAa;QACjB,IAAI,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE;YACxC,IAAI,CAAC,aAAa,CAAC;QACrB;QACA,IAAI,IAAI,CAAC,WAAW,CAAC,wBAAwB,EAAE;YAC7C,IAAI,CAAC,aAAa,CAAC;QACrB;QACA,KAAK,MAAM,CAAC,UAAU,mBAAmB,IAAI,OAAO,OAAO,CAAC,SAAU;YACpE,MAAM,aAAa,IAAI,CAAC,WAAW,CAAC,SAAS;YAC7C,IAAI,aAAa;YACjB,IAAI,aAAa,uBAAuB;gBACtC,aAAa,WAAW,OAAO,CAAC,uBAAuB,IAAI,CAAC,WAAW,CAAC,aAAa;YACvF,OAAO,IAAI,aAAa,4BAA4B;gBAClD,aAAa,WAAW,OAAO,CAC7B,6BACA,IAAI,CAAC,WAAW,CAAC,kBAAkB;YAEvC,OAAO,IAAI,aAAa,WAAW;gBACjC,IAAI,IAAI,CAAC,iBAAiB,KAAK,MAAM;oBACnC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;gBAC1D;gBACA,aAAa,WAAW,OAAO,CAAC,wBAAwB,IAAI,CAAC,iBAAiB;YAChF;YACA,IAAI,YAAY;gBACd,cAAc;gBACd,cAAc,WAAW,OAAO,CAAC,UAAU,YAAY,OAAO,CAAC,QAAQ,KAAK,IAAI;YAClF;QACF;QACA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1488, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/identifyShadowedGlobals.js"], "sourcesContent": ["import {\n  isBlockScopedDeclaration,\n  isFunctionScopedDeclaration,\n  isNonTopLevelDeclaration,\n} from \"./parser/tokenizer\";\n\nimport {TokenType as tt} from \"./parser/tokenizer/types\";\n\n\n/**\n * Traverse the given tokens and modify them if necessary to indicate that some names shadow global\n * variables.\n */\nexport default function identifyShadowedGlobals(\n  tokens,\n  scopes,\n  globalNames,\n) {\n  if (!hasShadowedGlobals(tokens, globalNames)) {\n    return;\n  }\n  markShadowedGlobals(tokens, scopes, globalNames);\n}\n\n/**\n * We can do a fast up-front check to see if there are any declarations to global names. If not,\n * then there's no point in computing scope assignments.\n */\n// Exported for testing.\nexport function hasShadowedGlobals(tokens, globalNames) {\n  for (const token of tokens.tokens) {\n    if (\n      token.type === tt.name &&\n      !token.isType &&\n      isNonTopLevelDeclaration(token) &&\n      globalNames.has(tokens.identifierNameForToken(token))\n    ) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction markShadowedGlobals(\n  tokens,\n  scopes,\n  globalNames,\n) {\n  const scopeStack = [];\n  let scopeIndex = scopes.length - 1;\n  // Scopes were generated at completion time, so they're sorted by end index, so we can maintain a\n  // good stack by going backwards through them.\n  for (let i = tokens.tokens.length - 1; ; i--) {\n    while (scopeStack.length > 0 && scopeStack[scopeStack.length - 1].startTokenIndex === i + 1) {\n      scopeStack.pop();\n    }\n    while (scopeIndex >= 0 && scopes[scopeIndex].endTokenIndex === i + 1) {\n      scopeStack.push(scopes[scopeIndex]);\n      scopeIndex--;\n    }\n    // Process scopes after the last iteration so we can make sure we pop all of them.\n    if (i < 0) {\n      break;\n    }\n\n    const token = tokens.tokens[i];\n    const name = tokens.identifierNameForToken(token);\n    if (scopeStack.length > 1 && !token.isType && token.type === tt.name && globalNames.has(name)) {\n      if (isBlockScopedDeclaration(token)) {\n        markShadowedForScope(scopeStack[scopeStack.length - 1], tokens, name);\n      } else if (isFunctionScopedDeclaration(token)) {\n        let stackIndex = scopeStack.length - 1;\n        while (stackIndex > 0 && !scopeStack[stackIndex].isFunctionScope) {\n          stackIndex--;\n        }\n        if (stackIndex < 0) {\n          throw new Error(\"Did not find parent function scope.\");\n        }\n        markShadowedForScope(scopeStack[stackIndex], tokens, name);\n      }\n    }\n  }\n  if (scopeStack.length > 0) {\n    throw new Error(\"Expected empty scope stack after processing file.\");\n  }\n}\n\nfunction markShadowedForScope(scope, tokens, name) {\n  for (let i = scope.startTokenIndex; i < scope.endTokenIndex; i++) {\n    const token = tokens.tokens[i];\n    if (\n      (token.type === tt.name || token.type === tt.jsxName) &&\n      tokens.identifierNameForToken(token) === name\n    ) {\n      token.shadowsGlobal = true;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AAMA;;;AAOe,SAAS,wBACtB,MAAM,EACN,MAAM,EACN,WAAW;IAEX,IAAI,CAAC,mBAAmB,QAAQ,cAAc;QAC5C;IACF;IACA,oBAAoB,QAAQ,QAAQ;AACtC;AAOO,SAAS,mBAAmB,MAAM,EAAE,WAAW;IACpD,KAAK,MAAM,SAAS,OAAO,MAAM,CAAE;QACjC,IACE,MAAM,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,IAAI,IACtB,CAAC,MAAM,MAAM,IACb,CAAA,GAAA,yKAAA,CAAA,2BAAwB,AAAD,EAAE,UACzB,YAAY,GAAG,CAAC,OAAO,sBAAsB,CAAC,SAC9C;YACA,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,SAAS,oBACP,MAAM,EACN,MAAM,EACN,WAAW;IAEX,MAAM,aAAa,EAAE;IACrB,IAAI,aAAa,OAAO,MAAM,GAAG;IACjC,iGAAiG;IACjG,8CAA8C;IAC9C,IAAK,IAAI,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,IAAK,IAAK;QAC5C,MAAO,WAAW,MAAM,GAAG,KAAK,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,eAAe,KAAK,IAAI,EAAG;YAC3F,WAAW,GAAG;QAChB;QACA,MAAO,cAAc,KAAK,MAAM,CAAC,WAAW,CAAC,aAAa,KAAK,IAAI,EAAG;YACpE,WAAW,IAAI,CAAC,MAAM,CAAC,WAAW;YAClC;QACF;QACA,kFAAkF;QAClF,IAAI,IAAI,GAAG;YACT;QACF;QAEA,MAAM,QAAQ,OAAO,MAAM,CAAC,EAAE;QAC9B,MAAM,OAAO,OAAO,sBAAsB,CAAC;QAC3C,IAAI,WAAW,MAAM,GAAG,KAAK,CAAC,MAAM,MAAM,IAAI,MAAM,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,IAAI,IAAI,YAAY,GAAG,CAAC,OAAO;YAC7F,IAAI,CAAA,GAAA,yKAAA,CAAA,2BAAwB,AAAD,EAAE,QAAQ;gBACnC,qBAAqB,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,EAAE,QAAQ;YAClE,OAAO,IAAI,CAAA,GAAA,yKAAA,CAAA,8BAA2B,AAAD,EAAE,QAAQ;gBAC7C,IAAI,aAAa,WAAW,MAAM,GAAG;gBACrC,MAAO,aAAa,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,eAAe,CAAE;oBAChE;gBACF;gBACA,IAAI,aAAa,GAAG;oBAClB,MAAM,IAAI,MAAM;gBAClB;gBACA,qBAAqB,UAAU,CAAC,WAAW,EAAE,QAAQ;YACvD;QACF;IACF;IACA,IAAI,WAAW,MAAM,GAAG,GAAG;QACzB,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,SAAS,qBAAqB,KAAK,EAAE,MAAM,EAAE,IAAI;IAC/C,IAAK,IAAI,IAAI,MAAM,eAAe,EAAE,IAAI,MAAM,aAAa,EAAE,IAAK;QAChE,MAAM,QAAQ,OAAO,MAAM,CAAC,EAAE;QAC9B,IACE,CAAC,MAAM,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,IAAI,IAAI,MAAM,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,OAAO,KACpD,OAAO,sBAAsB,CAAC,WAAW,MACzC;YACA,MAAM,aAAa,GAAG;QACxB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1562, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/NameManager.js"], "sourcesContent": ["\nimport getIdentifierNames from \"./util/getIdentifierNames\";\n\nexport default class NameManager {\n    __init() {this.usedNames = new Set()}\n\n  constructor(code, tokens) {;NameManager.prototype.__init.call(this);\n    this.usedNames = new Set(getIdentifierNames(code, tokens));\n  }\n\n  claimFreeName(name) {\n    const newName = this.findFreeName(name);\n    this.usedNames.add(newName);\n    return newName;\n  }\n\n  findFreeName(name) {\n    if (!this.usedNames.has(name)) {\n      return name;\n    }\n    let suffixNum = 2;\n    while (this.usedNames.has(name + String(suffixNum))) {\n      suffixNum++;\n    }\n    return name + String(suffixNum);\n  }\n}\n"], "names": [], "mappings": ";;;AACA;;AAEe,MAAM;IACjB,SAAS;QAAC,IAAI,CAAC,SAAS,GAAG,IAAI;IAAK;IAEtC,YAAY,IAAI,EAAE,MAAM,CAAE;;QAAE,YAAY,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;QAChE,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,CAAA,GAAA,uKAAA,CAAA,UAAkB,AAAD,EAAE,MAAM;IACpD;IAEA,cAAc,IAAI,EAAE;QAClB,MAAM,UAAU,IAAI,CAAC,YAAY,CAAC;QAClC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACnB,OAAO;IACT;IAEA,aAAa,IAAI,EAAE;QACjB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO;YAC7B,OAAO;QACT;QACA,IAAI,YAAY;QAChB,MAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,OAAO,YAAa;YACnD;QACF;QACA,OAAO,OAAO,OAAO;IACvB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1598, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/Options-gen-types.js"], "sourcesContent": ["/**\n * This module was automatically generated by `ts-interface-builder`\n */\nimport * as t from \"ts-interface-checker\";\n// tslint:disable:object-literal-key-quotes\n\nexport const Transform = t.union(\n  t.lit(\"jsx\"),\n  t.lit(\"typescript\"),\n  t.lit(\"flow\"),\n  t.lit(\"imports\"),\n  t.lit(\"react-hot-loader\"),\n  t.lit(\"jest\"),\n);\n\nexport const SourceMapOptions = t.iface([], {\n  compiledFilename: \"string\",\n});\n\nexport const Options = t.iface([], {\n  transforms: t.array(\"Transform\"),\n  disableESTransforms: t.opt(\"boolean\"),\n  jsxRuntime: t.opt(t.union(t.lit(\"classic\"), t.lit(\"automatic\"), t.lit(\"preserve\"))),\n  production: t.opt(\"boolean\"),\n  jsxImportSource: t.opt(\"string\"),\n  jsxPragma: t.opt(\"string\"),\n  jsxFragmentPragma: t.opt(\"string\"),\n  keepUnusedImports: t.opt(\"boolean\"),\n  preserveDynamicImport: t.opt(\"boolean\"),\n  injectCreateRequireForImportRequire: t.opt(\"boolean\"),\n  enableLegacyTypeScriptModuleInterop: t.opt(\"boolean\"),\n  enableLegacyBabel5ModuleInterop: t.opt(\"boolean\"),\n  sourceMapOptions: t.opt(\"SourceMapOptions\"),\n  filePath: t.opt(\"string\"),\n});\n\nconst exportedTypeSuite = {\n  Transform,\n  SourceMapOptions,\n  Options,\n};\nexport default exportedTypeSuite;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;AACD;;AAGO,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,QAAO,AAAD,EAC7B,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE,QACN,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE,eACN,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE,SACN,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE,YACN,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE,qBACN,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE;AAGD,MAAM,mBAAmB,CAAA,GAAA,8JAAA,CAAA,QAAO,AAAD,EAAE,EAAE,EAAE;IAC1C,kBAAkB;AACpB;AAEO,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,QAAO,AAAD,EAAE,EAAE,EAAE;IACjC,YAAY,CAAA,GAAA,8JAAA,CAAA,QAAO,AAAD,EAAE;IACpB,qBAAqB,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE;IAC3B,YAAY,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE,CAAA,GAAA,8JAAA,CAAA,QAAO,AAAD,EAAE,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE,YAAY,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE,cAAc,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE;IACtE,YAAY,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE;IAClB,iBAAiB,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE;IACvB,WAAW,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE;IACjB,mBAAmB,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE;IACzB,mBAAmB,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE;IACzB,uBAAuB,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE;IAC7B,qCAAqC,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE;IAC3C,qCAAqC,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE;IAC3C,iCAAiC,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE;IACvC,kBAAkB,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE;IACxB,UAAU,CAAA,GAAA,8JAAA,CAAA,MAAK,AAAD,EAAE;AAClB;AAEA,MAAM,oBAAoB;IACxB;IACA;IACA;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1640, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/Options.js"], "sourcesContent": ["import {createChe<PERSON>} from \"ts-interface-checker\";\n\nimport OptionsGenTypes from \"./Options-gen-types\";\n\nconst {Options: OptionsChecker} = createCheckers(OptionsGenTypes);\n\n \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nexport function validateOptions(options) {\n  OptionsChecker.strictCheck(options);\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEA,MAAM,EAAC,SAAS,cAAc,EAAC,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD,EAAE,oKAAA,CAAA,UAAe;AA8FzD,SAAS,gBAAgB,OAAO;IACrC,eAAe,WAAW,CAAC;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1657, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/TokenProcessor.js"], "sourcesContent": ["\n\n\nimport { TokenType as tt} from \"./parser/tokenizer/types\";\nimport isAsyncOperation from \"./util/isAsyncOperation\";\n\n\n\n\n\n\n\n\n\n\n\nexport default class TokenProcessor {\n   __init() {this.resultCode = \"\"}\n  // Array mapping input token index to optional string index position in the\n  // output code.\n   __init2() {this.resultMappings = new Array(this.tokens.length)}\n   __init3() {this.tokenIndex = 0}\n\n  constructor(\n     code,\n     tokens,\n     isFlowEnabled,\n     disableESTransforms,\n     helperManager,\n  ) {;this.code = code;this.tokens = tokens;this.isFlowEnabled = isFlowEnabled;this.disableESTransforms = disableESTransforms;this.helperManager = helperManager;TokenProcessor.prototype.__init.call(this);TokenProcessor.prototype.__init2.call(this);TokenProcessor.prototype.__init3.call(this);}\n\n  /**\n   * Snapshot the token state in a way that can be restored later, useful for\n   * things like lookahead.\n   *\n   * resultMappings do not need to be copied since in all use cases, they will\n   * be overwritten anyway after restore.\n   */\n  snapshot() {\n    return {\n      resultCode: this.resultCode,\n      tokenIndex: this.tokenIndex,\n    };\n  }\n\n  restoreToSnapshot(snapshot) {\n    this.resultCode = snapshot.resultCode;\n    this.tokenIndex = snapshot.tokenIndex;\n  }\n\n  /**\n   * Remove and return the code generated since the snapshot, leaving the\n   * current token position in-place. Unlike most TokenProcessor operations,\n   * this operation can result in input/output line number mismatches because\n   * the removed code may contain newlines, so this operation should be used\n   * sparingly.\n   */\n  dangerouslyGetAndRemoveCodeSinceSnapshot(snapshot) {\n    const result = this.resultCode.slice(snapshot.resultCode.length);\n    this.resultCode = snapshot.resultCode;\n    return result;\n  }\n\n  reset() {\n    this.resultCode = \"\";\n    this.resultMappings = new Array(this.tokens.length);\n    this.tokenIndex = 0;\n  }\n\n  matchesContextualAtIndex(index, contextualKeyword) {\n    return (\n      this.matches1AtIndex(index, tt.name) &&\n      this.tokens[index].contextualKeyword === contextualKeyword\n    );\n  }\n\n  identifierNameAtIndex(index) {\n    // TODO: We need to process escapes since technically you can have unicode escapes in variable\n    // names.\n    return this.identifierNameForToken(this.tokens[index]);\n  }\n\n  identifierNameAtRelativeIndex(relativeIndex) {\n    return this.identifierNameForToken(this.tokenAtRelativeIndex(relativeIndex));\n  }\n\n  identifierName() {\n    return this.identifierNameForToken(this.currentToken());\n  }\n\n  identifierNameForToken(token) {\n    return this.code.slice(token.start, token.end);\n  }\n\n  rawCodeForToken(token) {\n    return this.code.slice(token.start, token.end);\n  }\n\n  stringValueAtIndex(index) {\n    return this.stringValueForToken(this.tokens[index]);\n  }\n\n  stringValue() {\n    return this.stringValueForToken(this.currentToken());\n  }\n\n  stringValueForToken(token) {\n    // This is used to identify when two imports are the same and to resolve TypeScript enum keys.\n    // Ideally we'd process escapes within the strings, but for now we pretty much take the raw\n    // code.\n    return this.code.slice(token.start + 1, token.end - 1);\n  }\n\n  matches1AtIndex(index, t1) {\n    return this.tokens[index].type === t1;\n  }\n\n  matches2AtIndex(index, t1, t2) {\n    return this.tokens[index].type === t1 && this.tokens[index + 1].type === t2;\n  }\n\n  matches3AtIndex(index, t1, t2, t3) {\n    return (\n      this.tokens[index].type === t1 &&\n      this.tokens[index + 1].type === t2 &&\n      this.tokens[index + 2].type === t3\n    );\n  }\n\n  matches1(t1) {\n    return this.tokens[this.tokenIndex].type === t1;\n  }\n\n  matches2(t1, t2) {\n    return this.tokens[this.tokenIndex].type === t1 && this.tokens[this.tokenIndex + 1].type === t2;\n  }\n\n  matches3(t1, t2, t3) {\n    return (\n      this.tokens[this.tokenIndex].type === t1 &&\n      this.tokens[this.tokenIndex + 1].type === t2 &&\n      this.tokens[this.tokenIndex + 2].type === t3\n    );\n  }\n\n  matches4(t1, t2, t3, t4) {\n    return (\n      this.tokens[this.tokenIndex].type === t1 &&\n      this.tokens[this.tokenIndex + 1].type === t2 &&\n      this.tokens[this.tokenIndex + 2].type === t3 &&\n      this.tokens[this.tokenIndex + 3].type === t4\n    );\n  }\n\n  matches5(t1, t2, t3, t4, t5) {\n    return (\n      this.tokens[this.tokenIndex].type === t1 &&\n      this.tokens[this.tokenIndex + 1].type === t2 &&\n      this.tokens[this.tokenIndex + 2].type === t3 &&\n      this.tokens[this.tokenIndex + 3].type === t4 &&\n      this.tokens[this.tokenIndex + 4].type === t5\n    );\n  }\n\n  matchesContextual(contextualKeyword) {\n    return this.matchesContextualAtIndex(this.tokenIndex, contextualKeyword);\n  }\n\n  matchesContextIdAndLabel(type, contextId) {\n    return this.matches1(type) && this.currentToken().contextId === contextId;\n  }\n\n  previousWhitespaceAndComments() {\n    let whitespaceAndComments = this.code.slice(\n      this.tokenIndex > 0 ? this.tokens[this.tokenIndex - 1].end : 0,\n      this.tokenIndex < this.tokens.length ? this.tokens[this.tokenIndex].start : this.code.length,\n    );\n    if (this.isFlowEnabled) {\n      whitespaceAndComments = whitespaceAndComments.replace(/@flow/g, \"\");\n    }\n    return whitespaceAndComments;\n  }\n\n  replaceToken(newCode) {\n    this.resultCode += this.previousWhitespaceAndComments();\n    this.appendTokenPrefix();\n    this.resultMappings[this.tokenIndex] = this.resultCode.length;\n    this.resultCode += newCode;\n    this.appendTokenSuffix();\n    this.tokenIndex++;\n  }\n\n  replaceTokenTrimmingLeftWhitespace(newCode) {\n    this.resultCode += this.previousWhitespaceAndComments().replace(/[^\\r\\n]/g, \"\");\n    this.appendTokenPrefix();\n    this.resultMappings[this.tokenIndex] = this.resultCode.length;\n    this.resultCode += newCode;\n    this.appendTokenSuffix();\n    this.tokenIndex++;\n  }\n\n  removeInitialToken() {\n    this.replaceToken(\"\");\n  }\n\n  removeToken() {\n    this.replaceTokenTrimmingLeftWhitespace(\"\");\n  }\n\n  /**\n   * Remove all code until the next }, accounting for balanced braces.\n   */\n  removeBalancedCode() {\n    let braceDepth = 0;\n    while (!this.isAtEnd()) {\n      if (this.matches1(tt.braceL)) {\n        braceDepth++;\n      } else if (this.matches1(tt.braceR)) {\n        if (braceDepth === 0) {\n          return;\n        }\n        braceDepth--;\n      }\n      this.removeToken();\n    }\n  }\n\n  copyExpectedToken(tokenType) {\n    if (this.tokens[this.tokenIndex].type !== tokenType) {\n      throw new Error(`Expected token ${tokenType}`);\n    }\n    this.copyToken();\n  }\n\n  copyToken() {\n    this.resultCode += this.previousWhitespaceAndComments();\n    this.appendTokenPrefix();\n    this.resultMappings[this.tokenIndex] = this.resultCode.length;\n    this.resultCode += this.code.slice(\n      this.tokens[this.tokenIndex].start,\n      this.tokens[this.tokenIndex].end,\n    );\n    this.appendTokenSuffix();\n    this.tokenIndex++;\n  }\n\n  copyTokenWithPrefix(prefix) {\n    this.resultCode += this.previousWhitespaceAndComments();\n    this.appendTokenPrefix();\n    this.resultCode += prefix;\n    this.resultMappings[this.tokenIndex] = this.resultCode.length;\n    this.resultCode += this.code.slice(\n      this.tokens[this.tokenIndex].start,\n      this.tokens[this.tokenIndex].end,\n    );\n    this.appendTokenSuffix();\n    this.tokenIndex++;\n  }\n\n   appendTokenPrefix() {\n    const token = this.currentToken();\n    if (token.numNullishCoalesceStarts || token.isOptionalChainStart) {\n      token.isAsyncOperation = isAsyncOperation(this);\n    }\n    if (this.disableESTransforms) {\n      return;\n    }\n    if (token.numNullishCoalesceStarts) {\n      for (let i = 0; i < token.numNullishCoalesceStarts; i++) {\n        if (token.isAsyncOperation) {\n          this.resultCode += \"await \";\n          this.resultCode += this.helperManager.getHelperName(\"asyncNullishCoalesce\");\n        } else {\n          this.resultCode += this.helperManager.getHelperName(\"nullishCoalesce\");\n        }\n        this.resultCode += \"(\";\n      }\n    }\n    if (token.isOptionalChainStart) {\n      if (token.isAsyncOperation) {\n        this.resultCode += \"await \";\n      }\n      if (this.tokenIndex > 0 && this.tokenAtRelativeIndex(-1).type === tt._delete) {\n        if (token.isAsyncOperation) {\n          this.resultCode += this.helperManager.getHelperName(\"asyncOptionalChainDelete\");\n        } else {\n          this.resultCode += this.helperManager.getHelperName(\"optionalChainDelete\");\n        }\n      } else if (token.isAsyncOperation) {\n        this.resultCode += this.helperManager.getHelperName(\"asyncOptionalChain\");\n      } else {\n        this.resultCode += this.helperManager.getHelperName(\"optionalChain\");\n      }\n      this.resultCode += \"([\";\n    }\n  }\n\n   appendTokenSuffix() {\n    const token = this.currentToken();\n    if (token.isOptionalChainEnd && !this.disableESTransforms) {\n      this.resultCode += \"])\";\n    }\n    if (token.numNullishCoalesceEnds && !this.disableESTransforms) {\n      for (let i = 0; i < token.numNullishCoalesceEnds; i++) {\n        this.resultCode += \"))\";\n      }\n    }\n  }\n\n  appendCode(code) {\n    this.resultCode += code;\n  }\n\n  currentToken() {\n    return this.tokens[this.tokenIndex];\n  }\n\n  currentTokenCode() {\n    const token = this.currentToken();\n    return this.code.slice(token.start, token.end);\n  }\n\n  tokenAtRelativeIndex(relativeIndex) {\n    return this.tokens[this.tokenIndex + relativeIndex];\n  }\n\n  currentIndex() {\n    return this.tokenIndex;\n  }\n\n  /**\n   * Move to the next token. Only suitable in preprocessing steps. When\n   * generating new code, you should use copyToken or removeToken.\n   */\n  nextToken() {\n    if (this.tokenIndex === this.tokens.length) {\n      throw new Error(\"Unexpectedly reached end of input.\");\n    }\n    this.tokenIndex++;\n  }\n\n  previousToken() {\n    this.tokenIndex--;\n  }\n\n  finish() {\n    if (this.tokenIndex !== this.tokens.length) {\n      throw new Error(\"Tried to finish processing tokens before reaching the end.\");\n    }\n    this.resultCode += this.previousWhitespaceAndComments();\n    return {code: this.resultCode, mappings: this.resultMappings};\n  }\n\n  isAtEnd() {\n    return this.tokenIndex === this.tokens.length;\n  }\n}\n"], "names": [], "mappings": ";;;AAGA;AACA;;;AAYe,MAAM;IAClB,SAAS;QAAC,IAAI,CAAC,UAAU,GAAG;IAAE;IAC/B,2EAA2E;IAC3E,eAAe;IACd,UAAU;QAAC,IAAI,CAAC,cAAc,GAAG,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM;IAAC;IAC9D,UAAU;QAAC,IAAI,CAAC,UAAU,GAAG;IAAC;IAE/B,YACG,IAAI,EACJ,MAAM,EACN,aAAa,EACb,mBAAmB,EACnB,aAAa,CACd;;QAAE,IAAI,CAAC,IAAI,GAAG;QAAK,IAAI,CAAC,MAAM,GAAG;QAAO,IAAI,CAAC,aAAa,GAAG;QAAc,IAAI,CAAC,mBAAmB,GAAG;QAAoB,IAAI,CAAC,aAAa,GAAG;QAAc,eAAe,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;QAAE,eAAe,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;QAAE,eAAe,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;IAAE;IAElS;;;;;;GAMC,GACD,WAAW;QACT,OAAO;YACL,YAAY,IAAI,CAAC,UAAU;YAC3B,YAAY,IAAI,CAAC,UAAU;QAC7B;IACF;IAEA,kBAAkB,QAAQ,EAAE;QAC1B,IAAI,CAAC,UAAU,GAAG,SAAS,UAAU;QACrC,IAAI,CAAC,UAAU,GAAG,SAAS,UAAU;IACvC;IAEA;;;;;;GAMC,GACD,yCAAyC,QAAQ,EAAE;QACjD,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,UAAU,CAAC,MAAM;QAC/D,IAAI,CAAC,UAAU,GAAG,SAAS,UAAU;QACrC,OAAO;IACT;IAEA,QAAQ;QACN,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,cAAc,GAAG,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM;QAClD,IAAI,CAAC,UAAU,GAAG;IACpB;IAEA,yBAAyB,KAAK,EAAE,iBAAiB,EAAE;QACjD,OACE,IAAI,CAAC,eAAe,CAAC,OAAO,yKAAA,CAAA,YAAE,CAAC,IAAI,KACnC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,KAAK;IAE7C;IAEA,sBAAsB,KAAK,EAAE;QAC3B,8FAA8F;QAC9F,SAAS;QACT,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IACvD;IAEA,8BAA8B,aAAa,EAAE;QAC3C,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,oBAAoB,CAAC;IAC/D;IAEA,iBAAiB;QACf,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY;IACtD;IAEA,uBAAuB,KAAK,EAAE;QAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,EAAE,MAAM,GAAG;IAC/C;IAEA,gBAAgB,KAAK,EAAE;QACrB,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,EAAE,MAAM,GAAG;IAC/C;IAEA,mBAAmB,KAAK,EAAE;QACxB,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IACpD;IAEA,cAAc;QACZ,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY;IACnD;IAEA,oBAAoB,KAAK,EAAE;QACzB,8FAA8F;QAC9F,2FAA2F;QAC3F,QAAQ;QACR,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG,GAAG,MAAM,GAAG,GAAG;IACtD;IAEA,gBAAgB,KAAK,EAAE,EAAE,EAAE;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK;IACrC;IAEA,gBAAgB,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE;QAC7B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,KAAK;IAC3E;IAEA,gBAAgB,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACjC,OACE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,MAC5B,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,KAAK,MAChC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,KAAK;IAEpC;IAEA,SAAS,EAAE,EAAE;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,KAAK;IAC/C;IAEA,SAAS,EAAE,EAAE,EAAE,EAAE;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,KAAK;IAC/F;IAEA,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACnB,OACE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,KAAK,MACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,KAAK,MAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,KAAK;IAE9C;IAEA,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACvB,OACE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,KAAK,MACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,KAAK,MAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,KAAK,MAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,KAAK;IAE9C;IAEA,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QAC3B,OACE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,KAAK,MACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,KAAK,MAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,KAAK,MAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,KAAK,MAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,KAAK;IAE9C;IAEA,kBAAkB,iBAAiB,EAAE;QACnC,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,UAAU,EAAE;IACxD;IAEA,yBAAyB,IAAI,EAAE,SAAS,EAAE;QACxC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,YAAY,GAAG,SAAS,KAAK;IAClE;IAEA,gCAAgC;QAC9B,IAAI,wBAAwB,IAAI,CAAC,IAAI,CAAC,KAAK,CACzC,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,GAAG,GAAG,GAC7D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM;QAE9F,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,wBAAwB,sBAAsB,OAAO,CAAC,UAAU;QAClE;QACA,OAAO;IACT;IAEA,aAAa,OAAO,EAAE;QACpB,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,6BAA6B;QACrD,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM;QAC7D,IAAI,CAAC,UAAU,IAAI;QACnB,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,UAAU;IACjB;IAEA,mCAAmC,OAAO,EAAE;QAC1C,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,6BAA6B,GAAG,OAAO,CAAC,YAAY;QAC5E,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM;QAC7D,IAAI,CAAC,UAAU,IAAI;QACnB,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,UAAU;IACjB;IAEA,qBAAqB;QACnB,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,cAAc;QACZ,IAAI,CAAC,kCAAkC,CAAC;IAC1C;IAEA;;GAEC,GACD,qBAAqB;QACnB,IAAI,aAAa;QACjB,MAAO,CAAC,IAAI,CAAC,OAAO,GAAI;YACtB,IAAI,IAAI,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBAC5B;YACF,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,yKAAA,CAAA,YAAE,CAAC,MAAM,GAAG;gBACnC,IAAI,eAAe,GAAG;oBACpB;gBACF;gBACA;YACF;YACA,IAAI,CAAC,WAAW;QAClB;IACF;IAEA,kBAAkB,SAAS,EAAE;QAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,KAAK,WAAW;YACnD,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,WAAW;QAC/C;QACA,IAAI,CAAC,SAAS;IAChB;IAEA,YAAY;QACV,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,6BAA6B;QACrD,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM;QAC7D,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,EAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG;QAElC,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,UAAU;IACjB;IAEA,oBAAoB,MAAM,EAAE;QAC1B,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,6BAA6B;QACrD,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,UAAU,IAAI;QACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM;QAC7D,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,EAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG;QAElC,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,UAAU;IACjB;IAEC,oBAAoB;QACnB,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,IAAI,MAAM,wBAAwB,IAAI,MAAM,oBAAoB,EAAE;YAChE,MAAM,gBAAgB,GAAG,CAAA,GAAA,qKAAA,CAAA,UAAgB,AAAD,EAAE,IAAI;QAChD;QACA,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B;QACF;QACA,IAAI,MAAM,wBAAwB,EAAE;YAClC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,wBAAwB,EAAE,IAAK;gBACvD,IAAI,MAAM,gBAAgB,EAAE;oBAC1B,IAAI,CAAC,UAAU,IAAI;oBACnB,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;gBACtD,OAAO;oBACL,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;gBACtD;gBACA,IAAI,CAAC,UAAU,IAAI;YACrB;QACF;QACA,IAAI,MAAM,oBAAoB,EAAE;YAC9B,IAAI,MAAM,gBAAgB,EAAE;gBAC1B,IAAI,CAAC,UAAU,IAAI;YACrB;YACA,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,IAAI,CAAC,oBAAoB,CAAC,CAAC,GAAG,IAAI,KAAK,yKAAA,CAAA,YAAE,CAAC,OAAO,EAAE;gBAC5E,IAAI,MAAM,gBAAgB,EAAE;oBAC1B,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;gBACtD,OAAO;oBACL,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;gBACtD;YACF,OAAO,IAAI,MAAM,gBAAgB,EAAE;gBACjC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;YACtD,OAAO;gBACL,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;YACtD;YACA,IAAI,CAAC,UAAU,IAAI;QACrB;IACF;IAEC,oBAAoB;QACnB,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,IAAI,MAAM,kBAAkB,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YACzD,IAAI,CAAC,UAAU,IAAI;QACrB;QACA,IAAI,MAAM,sBAAsB,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7D,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,sBAAsB,EAAE,IAAK;gBACrD,IAAI,CAAC,UAAU,IAAI;YACrB;QACF;IACF;IAEA,WAAW,IAAI,EAAE;QACf,IAAI,CAAC,UAAU,IAAI;IACrB;IAEA,eAAe;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;IACrC;IAEA,mBAAmB;QACjB,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,EAAE,MAAM,GAAG;IAC/C;IAEA,qBAAqB,aAAa,EAAE;QAClC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,cAAc;IACrD;IAEA,eAAe;QACb,OAAO,IAAI,CAAC,UAAU;IACxB;IAEA;;;GAGC,GACD,YAAY;QACV,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC1C,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,UAAU;IACjB;IAEA,gBAAgB;QACd,IAAI,CAAC,UAAU;IACjB;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC1C,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,6BAA6B;QACrD,OAAO;YAAC,MAAM,IAAI,CAAC,UAAU;YAAE,UAAU,IAAI,CAAC,cAAc;QAAA;IAC9D;IAEA,UAAU;QACR,OAAO,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM;IAC/C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1945, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/sucrase/dist/esm/index.js"], "sourcesContent": ["import CJSImportProcessor from \"./CJSImportProcessor\";\nimport computeSourceMap, {} from \"./computeSourceMap\";\nimport {HelperManager} from \"./HelperManager\";\nimport identifyShadowedGlobals from \"./identifyShadowedGlobals\";\nimport NameManager from \"./NameManager\";\nimport {validateOptions} from \"./Options\";\n\nimport {parse} from \"./parser\";\n\nimport TokenProcessor from \"./TokenProcessor\";\nimport RootTransformer from \"./transformers/RootTransformer\";\nimport formatTokens from \"./util/formatTokens\";\nimport getTSImportedNames from \"./util/getTSImportedNames\";\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n;\n\nexport function getVersion() {\n  /* istanbul ignore next */\n  return \"3.35.0\";\n}\n\nexport function transform(code, options) {\n  validateOptions(options);\n  try {\n    const sucraseContext = getSucraseContext(code, options);\n    const transformer = new RootTransformer(\n      sucraseContext,\n      options.transforms,\n      Boolean(options.enableLegacyBabel5ModuleInterop),\n      options,\n    );\n    const transformerResult = transformer.transform();\n    let result = {code: transformerResult.code};\n    if (options.sourceMapOptions) {\n      if (!options.filePath) {\n        throw new Error(\"filePath must be specified when generating a source map.\");\n      }\n      result = {\n        ...result,\n        sourceMap: computeSourceMap(\n          transformerResult,\n          options.filePath,\n          options.sourceMapOptions,\n          code,\n          sucraseContext.tokenProcessor.tokens,\n        ),\n      };\n    }\n    return result;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  } catch (e) {\n    if (options.filePath) {\n      e.message = `Error transforming ${options.filePath}: ${e.message}`;\n    }\n    throw e;\n  }\n}\n\n/**\n * Return a string representation of the sucrase tokens, mostly useful for\n * diagnostic purposes.\n */\nexport function getFormattedTokens(code, options) {\n  const tokens = getSucraseContext(code, options).tokenProcessor.tokens;\n  return formatTokens(code, tokens);\n}\n\n/**\n * Call into the parser/tokenizer and do some further preprocessing:\n * - Come up with a set of used names so that we can assign new names.\n * - Preprocess all import/export statements so we know which globals we are interested in.\n * - Compute situations where any of those globals are shadowed.\n *\n * In the future, some of these preprocessing steps can be skipped based on what actual work is\n * being done.\n */\nfunction getSucraseContext(code, options) {\n  const isJSXEnabled = options.transforms.includes(\"jsx\");\n  const isTypeScriptEnabled = options.transforms.includes(\"typescript\");\n  const isFlowEnabled = options.transforms.includes(\"flow\");\n  const disableESTransforms = options.disableESTransforms === true;\n  const file = parse(code, isJSXEnabled, isTypeScriptEnabled, isFlowEnabled);\n  const tokens = file.tokens;\n  const scopes = file.scopes;\n\n  const nameManager = new NameManager(code, tokens);\n  const helperManager = new HelperManager(nameManager);\n  const tokenProcessor = new TokenProcessor(\n    code,\n    tokens,\n    isFlowEnabled,\n    disableESTransforms,\n    helperManager,\n  );\n  const enableLegacyTypeScriptModuleInterop = Boolean(options.enableLegacyTypeScriptModuleInterop);\n\n  let importProcessor = null;\n  if (options.transforms.includes(\"imports\")) {\n    importProcessor = new CJSImportProcessor(\n      nameManager,\n      tokenProcessor,\n      enableLegacyTypeScriptModuleInterop,\n      options,\n      options.transforms.includes(\"typescript\"),\n      Boolean(options.keepUnusedImports),\n      helperManager,\n    );\n    importProcessor.preprocessTokens();\n    // We need to mark shadowed globals after processing imports so we know that the globals are,\n    // but before type-only import pruning, since that relies on shadowing information.\n    identifyShadowedGlobals(tokenProcessor, scopes, importProcessor.getGlobalNames());\n    if (options.transforms.includes(\"typescript\") && !options.keepUnusedImports) {\n      importProcessor.pruneTypeOnlyImports();\n    }\n  } else if (options.transforms.includes(\"typescript\") && !options.keepUnusedImports) {\n    // Shadowed global detection is needed for TS implicit elision of imported names.\n    identifyShadowedGlobals(tokenProcessor, scopes, getTSImportedNames(tokenProcessor));\n  }\n  return {tokenProcessor, scopes, nameManager, importProcessor, helperManager};\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;AAiBO,SAAS;IACd,wBAAwB,GACxB,OAAO;AACT;AAEO,SAAS,UAAU,IAAI,EAAE,OAAO;IACrC,CAAA,GAAA,oJAAA,CAAA,kBAAe,AAAD,EAAE;IAChB,IAAI;QACF,MAAM,iBAAiB,kBAAkB,MAAM;QAC/C,MAAM,cAAc,IAAI,4KAAA,CAAA,UAAe,CACrC,gBACA,QAAQ,UAAU,EAClB,QAAQ,QAAQ,+BAA+B,GAC/C;QAEF,MAAM,oBAAoB,YAAY,SAAS;QAC/C,IAAI,SAAS;YAAC,MAAM,kBAAkB,IAAI;QAAA;QAC1C,IAAI,QAAQ,gBAAgB,EAAE;YAC5B,IAAI,CAAC,QAAQ,QAAQ,EAAE;gBACrB,MAAM,IAAI,MAAM;YAClB;YACA,SAAS;gBACP,GAAG,MAAM;gBACT,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAgB,AAAD,EACxB,mBACA,QAAQ,QAAQ,EAChB,QAAQ,gBAAgB,EACxB,MACA,eAAe,cAAc,CAAC,MAAM;YAExC;QACF;QACA,OAAO;IACP,8DAA8D;IAChE,EAAE,OAAO,GAAG;QACV,IAAI,QAAQ,QAAQ,EAAE;YACpB,EAAE,OAAO,GAAG,CAAC,mBAAmB,EAAE,QAAQ,QAAQ,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE;QACpE;QACA,MAAM;IACR;AACF;AAMO,SAAS,mBAAmB,IAAI,EAAE,OAAO;IAC9C,MAAM,SAAS,kBAAkB,MAAM,SAAS,cAAc,CAAC,MAAM;IACrE,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAY,AAAD,EAAE,MAAM;AAC5B;AAEA;;;;;;;;CAQC,GACD,SAAS,kBAAkB,IAAI,EAAE,OAAO;IACtC,MAAM,eAAe,QAAQ,UAAU,CAAC,QAAQ,CAAC;IACjD,MAAM,sBAAsB,QAAQ,UAAU,CAAC,QAAQ,CAAC;IACxD,MAAM,gBAAgB,QAAQ,UAAU,CAAC,QAAQ,CAAC;IAClD,MAAM,sBAAsB,QAAQ,mBAAmB,KAAK;IAC5D,MAAM,OAAO,CAAA,GAAA,4JAAA,CAAA,QAAK,AAAD,EAAE,MAAM,cAAc,qBAAqB;IAC5D,MAAM,SAAS,KAAK,MAAM;IAC1B,MAAM,SAAS,KAAK,MAAM;IAE1B,MAAM,cAAc,IAAI,wJAAA,CAAA,UAAW,CAAC,MAAM;IAC1C,MAAM,gBAAgB,IAAI,0JAAA,CAAA,gBAAa,CAAC;IACxC,MAAM,iBAAiB,IAAI,2JAAA,CAAA,UAAc,CACvC,MACA,QACA,eACA,qBACA;IAEF,MAAM,sCAAsC,QAAQ,QAAQ,mCAAmC;IAE/F,IAAI,kBAAkB;IACtB,IAAI,QAAQ,UAAU,CAAC,QAAQ,CAAC,YAAY;QAC1C,kBAAkB,IAAI,+JAAA,CAAA,UAAkB,CACtC,aACA,gBACA,qCACA,SACA,QAAQ,UAAU,CAAC,QAAQ,CAAC,eAC5B,QAAQ,QAAQ,iBAAiB,GACjC;QAEF,gBAAgB,gBAAgB;QAChC,6FAA6F;QAC7F,mFAAmF;QACnF,CAAA,GAAA,oKAAA,CAAA,UAAuB,AAAD,EAAE,gBAAgB,QAAQ,gBAAgB,cAAc;QAC9E,IAAI,QAAQ,UAAU,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,iBAAiB,EAAE;YAC3E,gBAAgB,oBAAoB;QACtC;IACF,OAAO,IAAI,QAAQ,UAAU,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,iBAAiB,EAAE;QAClF,iFAAiF;QACjF,CAAA,GAAA,oKAAA,CAAA,UAAuB,AAAD,EAAE,gBAAgB,QAAQ,CAAA,GAAA,uKAAA,CAAA,UAAkB,AAAD,EAAE;IACrE;IACA,OAAO;QAAC;QAAgB;QAAQ;QAAa;QAAiB;IAAa;AAC7E", "ignoreList": [0], "debugId": null}}]}