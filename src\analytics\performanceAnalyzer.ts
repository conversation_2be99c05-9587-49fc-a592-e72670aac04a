import {
  GameComponent,
  ComponentType,
  ResourceType,
  PerformanceMetrics,
  GameState,
  COMPONENT_DEFINITIONS,
} from '@/types/game';

export interface ComponentAnalytics {
  id: string;
  type: ComponentType;
  utilization: number; // 0-1
  throughput: number; // items per minute
  efficiency: number; // 0-1
  bottleneckScore: number; // 0-1, higher means more of a bottleneck
  inputStarvation: number; // 0-1, higher means starved for inputs
  outputBlocked: number; // 0-1, higher means outputs are blocked
  averageInventory: number; // average items in inventory
  processingTime: number; // average time to process items
}

export interface ResourceFlowAnalytics {
  resource: ResourceType;
  totalProduction: number; // items per minute
  totalConsumption: number; // items per minute
  netFlow: number; // production - consumption
  flowEfficiency: number; // 0-1
  bottleneckComponents: string[]; // component IDs causing bottlenecks
}

export interface FactoryAnalytics {
  overallEfficiency: number; // 0-1
  totalThroughput: number; // total items processed per minute
  componentAnalytics: Map<string, ComponentAnalytics>;
  resourceFlows: Map<ResourceType, ResourceFlowAnalytics>;
  bottlenecks: string[]; // sorted by severity
  recommendations: string[];
  performanceScore: number; // 0-100
}

export class PerformanceAnalyzer {
  private historicalData: Map<string, number[]> = new Map();
  private lastAnalysisTime: number = 0;
  private readonly ANALYSIS_INTERVAL = 5000; // 5 seconds
  private readonly HISTORY_LENGTH = 20; // Keep 20 data points

  public analyzeFactory(gameState: GameState): FactoryAnalytics {
    const currentTime = Date.now();
    
    // Only run full analysis periodically
    if (currentTime - this.lastAnalysisTime < this.ANALYSIS_INTERVAL) {
      return this.getLastAnalysis(gameState);
    }

    this.lastAnalysisTime = currentTime;

    const componentAnalytics = this.analyzeComponents(gameState);
    const resourceFlows = this.analyzeResourceFlows(gameState, componentAnalytics);
    const bottlenecks = this.identifyBottlenecks(componentAnalytics);
    const overallEfficiency = this.calculateOverallEfficiency(componentAnalytics);
    const totalThroughput = this.calculateTotalThroughput(componentAnalytics);
    const recommendations = this.generateRecommendations(componentAnalytics, resourceFlows, bottlenecks);
    const performanceScore = this.calculatePerformanceScore(overallEfficiency, totalThroughput, bottlenecks.length);

    // Store historical data
    this.updateHistoricalData('efficiency', overallEfficiency);
    this.updateHistoricalData('throughput', totalThroughput);

    return {
      overallEfficiency,
      totalThroughput,
      componentAnalytics,
      resourceFlows,
      bottlenecks,
      recommendations,
      performanceScore,
    };
  }

  private analyzeComponents(gameState: GameState): Map<string, ComponentAnalytics> {
    const analytics = new Map<string, ComponentAnalytics>();

    for (const [id, component] of gameState.components) {
      const definition = COMPONENT_DEFINITIONS[component.type];
      const utilization = this.calculateUtilization(component);
      const throughput = this.calculateThroughput(component);
      const efficiency = this.calculateComponentEfficiency(component);
      const bottleneckScore = this.calculateBottleneckScore(component, gameState);
      const inputStarvation = this.calculateInputStarvation(component, gameState);
      const outputBlocked = this.calculateOutputBlocked(component, gameState);
      const averageInventory = this.calculateAverageInventory(component);
      const processingTime = this.calculateProcessingTime(component);

      analytics.set(id, {
        id,
        type: component.type,
        utilization,
        throughput,
        efficiency,
        bottleneckScore,
        inputStarvation,
        outputBlocked,
        averageInventory,
        processingTime,
      });
    }

    return analytics;
  }

  private calculateUtilization(component: GameComponent): number {
    // Simple utilization based on activity
    return component.isActive ? 1.0 : 0.0;
  }

  private calculateThroughput(component: GameComponent): number {
    const definition = COMPONENT_DEFINITIONS[component.type];
    const baseSpeed = definition.speed;
    
    // Convert to items per minute
    const itemsPerMinute = baseSpeed * 60;
    
    // Adjust based on actual utilization
    return component.isActive ? itemsPerMinute : 0;
  }

  private calculateComponentEfficiency(component: GameComponent): number {
    const definition = COMPONENT_DEFINITIONS[component.type];
    
    // For assemblers, check if they have the right inputs
    if (component.type === ComponentType.ASSEMBLER && component.recipe) {
      const hasAllInputs = component.recipe.inputs.every(input => {
        const available = component.inventory.get(input.resource) || 0;
        return available >= input.amount;
      });
      return hasAllInputs ? 1.0 : 0.5;
    }

    // For other components, efficiency is based on activity
    return component.isActive ? 1.0 : 0.0;
  }

  private calculateBottleneckScore(component: GameComponent, gameState: GameState): number {
    // A component is a bottleneck if it's blocking others or being blocked
    let score = 0;

    // Check if outputs are connected but not flowing
    const outputComponents = component.connections.outputs
      .map(id => gameState.components.get(id))
      .filter(comp => comp !== undefined);

    if (outputComponents.length > 0) {
      const blockedOutputs = outputComponents.filter(comp => !comp!.isActive).length;
      score += blockedOutputs / outputComponents.length * 0.5;
    }

    // Check if inputs are starved
    const inputComponents = component.connections.inputs
      .map(id => gameState.components.get(id))
      .filter(comp => comp !== undefined);

    if (inputComponents.length > 0) {
      const starvedInputs = inputComponents.filter(comp => !comp!.isActive).length;
      score += starvedInputs / inputComponents.length * 0.5;
    }

    return Math.min(score, 1.0);
  }

  private calculateInputStarvation(component: GameComponent, gameState: GameState): number {
    if (component.type === ComponentType.MINER) return 0; // Miners don't need inputs

    const totalInventory = Array.from(component.inventory.values()).reduce((sum, amount) => sum + amount, 0);
    const maxCapacity = this.getComponentCapacity(component);
    
    return 1 - (totalInventory / Math.max(maxCapacity * 0.5, 1)); // Starved if less than 50% capacity
  }

  private calculateOutputBlocked(component: GameComponent, gameState: GameState): number {
    const totalInventory = Array.from(component.inventory.values()).reduce((sum, amount) => sum + amount, 0);
    const maxCapacity = this.getComponentCapacity(component);
    
    return totalInventory / Math.max(maxCapacity, 1); // Blocked if inventory is full
  }

  private calculateAverageInventory(component: GameComponent): number {
    const totalInventory = Array.from(component.inventory.values()).reduce((sum, amount) => sum + amount, 0);
    return totalInventory;
  }

  private calculateProcessingTime(component: GameComponent): number {
    if (component.recipe) {
      return component.recipe.processingTime;
    }
    
    const definition = COMPONENT_DEFINITIONS[component.type];
    return 1000 / definition.speed; // Convert speed to processing time
  }

  private getComponentCapacity(component: GameComponent): number {
    switch (component.type) {
      case ComponentType.STORAGE:
        return 1000;
      case ComponentType.ASSEMBLER:
        return 100;
      case ComponentType.MINER:
        return 50;
      default:
        return 10;
    }
  }

  private analyzeResourceFlows(
    gameState: GameState,
    componentAnalytics: Map<string, ComponentAnalytics>
  ): Map<ResourceType, ResourceFlowAnalytics> {
    const flows = new Map<ResourceType, ResourceFlowAnalytics>();

    // Initialize all resource types
    Object.values(ResourceType).forEach(resource => {
      flows.set(resource, {
        resource,
        totalProduction: 0,
        totalConsumption: 0,
        netFlow: 0,
        flowEfficiency: 0,
        bottleneckComponents: [],
      });
    });

    // Calculate production and consumption
    for (const [id, component] of gameState.components) {
      const analytics = componentAnalytics.get(id);
      if (!analytics) continue;

      // Production (miners and assemblers)
      if (component.type === ComponentType.MINER) {
        const flow = flows.get(ResourceType.IRON_ORE)!;
        flow.totalProduction += analytics.throughput;
      } else if (component.type === ComponentType.ASSEMBLER && component.recipe) {
        component.recipe.outputs.forEach(output => {
          const flow = flows.get(output.resource)!;
          flow.totalProduction += analytics.throughput * output.amount;
        });

        component.recipe.inputs.forEach(input => {
          const flow = flows.get(input.resource)!;
          flow.totalConsumption += analytics.throughput * input.amount;
        });
      }

      // Check for bottlenecks
      if (analytics.bottleneckScore > 0.5) {
        Object.values(ResourceType).forEach(resource => {
          if (component.inventory.has(resource)) {
            flows.get(resource)!.bottleneckComponents.push(id);
          }
        });
      }
    }

    // Calculate net flow and efficiency
    flows.forEach(flow => {
      flow.netFlow = flow.totalProduction - flow.totalConsumption;
      flow.flowEfficiency = flow.totalConsumption > 0 
        ? Math.min(flow.totalProduction / flow.totalConsumption, 1.0)
        : flow.totalProduction > 0 ? 1.0 : 0.0;
    });

    return flows;
  }

  private identifyBottlenecks(componentAnalytics: Map<string, ComponentAnalytics>): string[] {
    return Array.from(componentAnalytics.entries())
      .filter(([_, analytics]) => analytics.bottleneckScore > 0.3)
      .sort((a, b) => b[1].bottleneckScore - a[1].bottleneckScore)
      .map(([id, _]) => id);
  }

  private calculateOverallEfficiency(componentAnalytics: Map<string, ComponentAnalytics>): number {
    if (componentAnalytics.size === 0) return 0;

    const totalEfficiency = Array.from(componentAnalytics.values())
      .reduce((sum, analytics) => sum + analytics.efficiency, 0);
    
    return totalEfficiency / componentAnalytics.size;
  }

  private calculateTotalThroughput(componentAnalytics: Map<string, ComponentAnalytics>): number {
    return Array.from(componentAnalytics.values())
      .reduce((sum, analytics) => sum + analytics.throughput, 0);
  }

  private generateRecommendations(
    componentAnalytics: Map<string, ComponentAnalytics>,
    resourceFlows: Map<ResourceType, ResourceFlowAnalytics>,
    bottlenecks: string[]
  ): string[] {
    const recommendations: string[] = [];

    // Check for bottlenecks
    if (bottlenecks.length > 0) {
      recommendations.push(`Address ${bottlenecks.length} bottleneck(s) to improve efficiency`);
    }

    // Check for resource imbalances
    resourceFlows.forEach(flow => {
      if (flow.netFlow < -10) {
        recommendations.push(`Increase production of ${flow.resource.replace('_', ' ')}`);
      } else if (flow.netFlow > 50) {
        recommendations.push(`Consider using excess ${flow.resource.replace('_', ' ')}`);
      }
    });

    // Check for underutilized components
    const underutilized = Array.from(componentAnalytics.values())
      .filter(analytics => analytics.utilization < 0.5);
    
    if (underutilized.length > 0) {
      recommendations.push(`${underutilized.length} components are underutilized`);
    }

    return recommendations;
  }

  private calculatePerformanceScore(
    efficiency: number,
    throughput: number,
    bottleneckCount: number
  ): number {
    const efficiencyScore = efficiency * 40; // 40 points max
    const throughputScore = Math.min(throughput / 100, 1) * 40; // 40 points max
    const bottleneckPenalty = bottleneckCount * 5; // -5 points per bottleneck
    
    return Math.max(0, Math.min(100, efficiencyScore + throughputScore - bottleneckPenalty));
  }

  private updateHistoricalData(key: string, value: number): void {
    if (!this.historicalData.has(key)) {
      this.historicalData.set(key, []);
    }

    const data = this.historicalData.get(key)!;
    data.push(value);

    // Keep only recent data
    if (data.length > this.HISTORY_LENGTH) {
      data.shift();
    }
  }

  private getLastAnalysis(gameState: GameState): FactoryAnalytics {
    // Return a simplified analysis for frequent updates
    const componentAnalytics = new Map<string, ComponentAnalytics>();
    
    for (const [id, component] of gameState.components) {
      componentAnalytics.set(id, {
        id,
        type: component.type,
        utilization: component.isActive ? 1.0 : 0.0,
        throughput: 0,
        efficiency: 0,
        bottleneckScore: 0,
        inputStarvation: 0,
        outputBlocked: 0,
        averageInventory: 0,
        processingTime: 0,
      });
    }

    return {
      overallEfficiency: 0,
      totalThroughput: 0,
      componentAnalytics,
      resourceFlows: new Map(),
      bottlenecks: [],
      recommendations: [],
      performanceScore: 0,
    };
  }

  public getHistoricalData(key: string): number[] {
    return this.historicalData.get(key) || [];
  }
}
