'use client';

import React, { useState } from 'react';
import { useDrag } from 'react-dnd';
import { ComponentType, COMPONENT_DEFINITIONS } from '@/types/game';
import { 
  ArrowRight, 
  Cog, 
  Package, 
  Pickaxe, 
  Split, 
  Merge,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import clsx from 'clsx';

interface DraggableComponentProps {
  type: ComponentType;
  definition: typeof COMPONENT_DEFINITIONS[ComponentType];
}

const DraggableComponent: React.FC<DraggableComponentProps> = ({ type, definition }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'component',
    item: { type },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const getIcon = () => {
    switch (type) {
      case ComponentType.CONVEYOR:
        return <ArrowRight className="w-8 h-8" />;
      case ComponentType.MINER:
        return <Pickaxe className="w-8 h-8" />;
      case ComponentType.ASSEMBLER:
        return <Cog className="w-8 h-8" />;
      case ComponentType.STORAGE:
        return <Package className="w-8 h-8" />;
      case ComponentType.SPLITTER:
        return <Split className="w-8 h-8" />;
      case ComponentType.MERGER:
        return <Merge className="w-8 h-8" />;
      default:
        return <Cog className="w-8 h-8" />;
    }
  };

  const getColor = () => {
    switch (type) {
      case ComponentType.CONVEYOR:
        return 'bg-yellow-600 hover:bg-yellow-500 border-yellow-500';
      case ComponentType.MINER:
        return 'bg-blue-600 hover:bg-blue-500 border-blue-500';
      case ComponentType.ASSEMBLER:
        return 'bg-green-600 hover:bg-green-500 border-green-500';
      case ComponentType.STORAGE:
        return 'bg-purple-600 hover:bg-purple-500 border-purple-500';
      case ComponentType.SPLITTER:
        return 'bg-orange-600 hover:bg-orange-500 border-orange-500';
      case ComponentType.MERGER:
        return 'bg-red-600 hover:bg-red-500 border-red-500';
      default:
        return 'bg-gray-600 hover:bg-gray-500 border-gray-500';
    }
  };

  return (
    <div
      ref={drag}
      className={clsx(
        'p-4 border-2 rounded-lg cursor-grab transition-all duration-200 text-white',
        getColor(),
        {
          'opacity-50 cursor-grabbing': isDragging,
          'transform scale-105': !isDragging,
        }
      )}
    >
      <div className="flex items-center gap-3">
        <div className="flex-shrink-0">
          {getIcon()}
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-sm truncate">{definition.name}</h3>
          <p className="text-xs opacity-90 line-clamp-2">{definition.description}</p>
          <div className="mt-1 text-xs opacity-75">
            Speed: {definition.speed}
          </div>
        </div>
      </div>
    </div>
  );
};

interface CategorySectionProps {
  title: string;
  components: ComponentType[];
  isExpanded: boolean;
  onToggle: () => void;
}

const CategorySection: React.FC<CategorySectionProps> = ({ 
  title, 
  components, 
  isExpanded, 
  onToggle 
}) => {
  return (
    <div className="mb-4">
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
      >
        <span className="font-semibold text-white">{title}</span>
        {isExpanded ? (
          <ChevronDown className="w-4 h-4 text-gray-300" />
        ) : (
          <ChevronRight className="w-4 h-4 text-gray-300" />
        )}
      </button>
      
      {isExpanded && (
        <div className="mt-2 space-y-2">
          {components.map((type) => (
            <DraggableComponent
              key={type}
              type={type}
              definition={COMPONENT_DEFINITIONS[type]}
            />
          ))}
        </div>
      )}
    </div>
  );
};

const ComponentPalette: React.FC = () => {
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(['Transportation', 'Production'])
  );

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  const categories = [
    {
      title: 'Transportation',
      components: [ComponentType.CONVEYOR, ComponentType.SPLITTER, ComponentType.MERGER],
    },
    {
      title: 'Production',
      components: [ComponentType.MINER, ComponentType.ASSEMBLER],
    },
    {
      title: 'Storage',
      components: [ComponentType.STORAGE],
    },
  ];

  return (
    <div className="h-full p-4 overflow-y-auto">
      <div className="mb-6">
        <h2 className="text-xl font-bold text-white mb-2">Components</h2>
        <p className="text-sm text-gray-300">
          Drag components onto the board to build your factory
        </p>
      </div>

      {categories.map((category) => (
        <CategorySection
          key={category.title}
          title={category.title}
          components={category.components}
          isExpanded={expandedCategories.has(category.title)}
          onToggle={() => toggleCategory(category.title)}
        />
      ))}

      <div className="mt-8 p-4 bg-gray-700 rounded-lg">
        <h3 className="font-semibold text-white mb-2">Tips</h3>
        <ul className="text-xs text-gray-300 space-y-1">
          <li>• Drag components to place them</li>
          <li>• Right-click to rotate</li>
          <li>• Double-click assemblers to set recipes</li>
          <li>• Connect components by placing them adjacent</li>
          <li>• Use splitters to divide resource flows</li>
          <li>• Use mergers to combine resource flows</li>
        </ul>
      </div>
    </div>
  );
};

export default ComponentPalette;
