import { NextRequest, NextResponse } from 'next/server';
import { GameStateSerializer } from '@/ai/gameStateSerializer';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { serializedState } = body;

    if (!serializedState) {
      return NextResponse.json(
        { error: 'Serialized state is required' },
        { status: 400 }
      );
    }

    // Validate the serialized state structure
    const validation = validateSerializedState(serializedState);
    if (!validation.valid) {
      return NextResponse.json(
        { error: 'Invalid serialized state', details: validation.errors },
        { status: 400 }
      );
    }

    // Deserialize the game state
    const gameState = GameStateSerializer.deserialize(serializedState);

    return NextResponse.json({
      success: true,
      data: {
        gameState,
        metadata: {
          componentCount: serializedState.components.length,
          efficiency: serializedState.statistics.efficiency,
          isRunning: serializedState.metadata.isRunning,
          importedAt: Date.now(),
        },
      },
    });
  } catch (error) {
    console.error('Error importing game state:', error);
    return NextResponse.json(
      { error: 'Failed to import game state' },
      { status: 500 }
    );
  }
}

function validateSerializedState(state: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check required top-level properties
  if (!state.metadata) {
    errors.push('Missing metadata');
  } else {
    if (typeof state.metadata.version !== 'string') {
      errors.push('Invalid metadata.version');
    }
    if (typeof state.metadata.timestamp !== 'number') {
      errors.push('Invalid metadata.timestamp');
    }
    if (!state.metadata.gridSize || typeof state.metadata.gridSize.width !== 'number' || typeof state.metadata.gridSize.height !== 'number') {
      errors.push('Invalid metadata.gridSize');
    }
  }

  if (!Array.isArray(state.components)) {
    errors.push('Components must be an array');
  } else {
    // Validate each component
    state.components.forEach((comp: any, index: number) => {
      if (!comp.id || typeof comp.id !== 'string') {
        errors.push(`Component ${index}: Invalid id`);
      }
      if (!comp.type || typeof comp.type !== 'string') {
        errors.push(`Component ${index}: Invalid type`);
      }
      if (!comp.position || typeof comp.position.x !== 'number' || typeof comp.position.y !== 'number') {
        errors.push(`Component ${index}: Invalid position`);
      }
      if (typeof comp.direction !== 'number' || comp.direction < 0 || comp.direction > 3) {
        errors.push(`Component ${index}: Invalid direction`);
      }
      if (!comp.connections || !Array.isArray(comp.connections.inputs) || !Array.isArray(comp.connections.outputs)) {
        errors.push(`Component ${index}: Invalid connections`);
      }
    });
  }

  if (!state.resources || typeof state.resources !== 'object') {
    errors.push('Invalid resources');
  }

  if (!state.statistics) {
    errors.push('Missing statistics');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}
