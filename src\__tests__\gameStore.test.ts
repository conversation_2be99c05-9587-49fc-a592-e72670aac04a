import { renderHook, act } from '@testing-library/react';
import { useGameStore } from '@/store/gameStore';
import { ComponentType, Direction, ResourceType } from '@/types/game';

// Mock the simulation engine
jest.mock('@/engine/simulation', () => ({
  SimulationEngine: jest.fn().mockImplementation(() => ({
    updateSimulation: jest.fn().mockReturnValue({}),
  })),
}));

// Mock the performance analyzer
jest.mock('@/analytics/performanceAnalyzer', () => ({
  PerformanceAnalyzer: jest.fn().mockImplementation(() => ({
    analyzeFactory: jest.fn().mockReturnValue({
      overallEfficiency: 0.8,
      totalThroughput: 100,
      componentAnalytics: new Map(),
      resourceFlows: new Map(),
      bottlenecks: [],
      recommendations: [],
      performanceScore: 80,
    }),
    getHistoricalData: jest.fn().mockReturnValue([]),
  })),
}));

describe('GameStore', () => {
  beforeEach(() => {
    // Reset the store before each test
    const { result } = renderHook(() => useGameStore());
    act(() => {
      result.current.resetGame();
    });
  });

  describe('Component Management', () => {
    it('should add a component to the game', () => {
      const { result } = renderHook(() => useGameStore());

      act(() => {
        const componentId = result.current.addComponent(
          ComponentType.CONVEYOR,
          { x: 0, y: 0 },
          Direction.NORTH
        );
        expect(componentId).toBeTruthy();
      });

      expect(result.current.components.size).toBe(1);
      const component = Array.from(result.current.components.values())[0];
      expect(component.type).toBe(ComponentType.CONVEYOR);
      expect(component.position).toEqual({ x: 0, y: 0 });
      expect(component.direction).toBe(Direction.NORTH);
    });

    it('should not add a component at an invalid position', () => {
      const { result } = renderHook(() => useGameStore());

      act(() => {
        // Try to add component outside grid bounds
        const componentId = result.current.addComponent(
          ComponentType.CONVEYOR,
          { x: 100, y: 100 }
        );
        expect(componentId).toBeNull();
      });

      expect(result.current.components.size).toBe(0);
    });

    it('should not add overlapping components', () => {
      const { result } = renderHook(() => useGameStore());

      act(() => {
        // Add first component
        result.current.addComponent(ComponentType.CONVEYOR, { x: 0, y: 0 });
        // Try to add overlapping component
        const secondId = result.current.addComponent(ComponentType.CONVEYOR, { x: 0, y: 0 });
        expect(secondId).toBeNull();
      });

      expect(result.current.components.size).toBe(1);
    });

    it('should remove a component', () => {
      const { result } = renderHook(() => useGameStore());

      let componentId: string;
      act(() => {
        componentId = result.current.addComponent(ComponentType.CONVEYOR, { x: 0, y: 0 })!;
      });

      expect(result.current.components.size).toBe(1);

      act(() => {
        result.current.removeComponent(componentId);
      });

      expect(result.current.components.size).toBe(0);
    });

    it('should move a component to a valid position', () => {
      const { result } = renderHook(() => useGameStore());

      let componentId: string;
      act(() => {
        componentId = result.current.addComponent(ComponentType.CONVEYOR, { x: 0, y: 0 })!;
      });

      act(() => {
        result.current.moveComponent(componentId, { x: 5, y: 5 });
      });

      const component = result.current.components.get(componentId);
      expect(component?.position).toEqual({ x: 5, y: 5 });
    });

    it('should rotate a component', () => {
      const { result } = renderHook(() => useGameStore());

      let componentId: string;
      act(() => {
        componentId = result.current.addComponent(
          ComponentType.CONVEYOR,
          { x: 0, y: 0 },
          Direction.NORTH
        )!;
      });

      act(() => {
        result.current.rotateComponent(componentId);
      });

      const component = result.current.components.get(componentId);
      expect(component?.direction).toBe(Direction.EAST);

      // Test full rotation
      act(() => {
        result.current.rotateComponent(componentId);
        result.current.rotateComponent(componentId);
        result.current.rotateComponent(componentId);
      });

      const rotatedComponent = result.current.components.get(componentId);
      expect(rotatedComponent?.direction).toBe(Direction.NORTH);
    });
  });

  describe('Component Connections', () => {
    it('should connect two components', () => {
      const { result } = renderHook(() => useGameStore());

      let component1Id: string, component2Id: string;
      act(() => {
        component1Id = result.current.addComponent(ComponentType.MINER, { x: 0, y: 0 })!;
        component2Id = result.current.addComponent(ComponentType.CONVEYOR, { x: 2, y: 0 })!;
      });

      act(() => {
        const connected = result.current.connectComponents(component1Id, component2Id);
        expect(connected).toBe(true);
      });

      const component1 = result.current.components.get(component1Id);
      const component2 = result.current.components.get(component2Id);

      expect(component1?.connections.outputs).toContain(component2Id);
      expect(component2?.connections.inputs).toContain(component1Id);
    });

    it('should not connect a component to itself', () => {
      const { result } = renderHook(() => useGameStore());

      let componentId: string;
      act(() => {
        componentId = result.current.addComponent(ComponentType.CONVEYOR, { x: 0, y: 0 })!;
      });

      act(() => {
        const connected = result.current.connectComponents(componentId, componentId);
        expect(connected).toBe(false);
      });
    });

    it('should disconnect components', () => {
      const { result } = renderHook(() => useGameStore());

      let component1Id: string, component2Id: string;
      act(() => {
        component1Id = result.current.addComponent(ComponentType.MINER, { x: 0, y: 0 })!;
        component2Id = result.current.addComponent(ComponentType.CONVEYOR, { x: 2, y: 0 })!;
        result.current.connectComponents(component1Id, component2Id);
      });

      act(() => {
        result.current.disconnectComponents(component1Id, component2Id);
      });

      const component1 = result.current.components.get(component1Id);
      const component2 = result.current.components.get(component2Id);

      expect(component1?.connections.outputs).not.toContain(component2Id);
      expect(component2?.connections.inputs).not.toContain(component1Id);
    });
  });

  describe('Recipe Management', () => {
    it('should set a recipe for an assembler', () => {
      const { result } = renderHook(() => useGameStore());

      let assemblerId: string;
      act(() => {
        assemblerId = result.current.addComponent(ComponentType.ASSEMBLER, { x: 0, y: 0 })!;
      });

      act(() => {
        result.current.setComponentRecipe(assemblerId, 'iron_plate');
      });

      const assembler = result.current.components.get(assemblerId);
      expect(assembler?.recipe?.id).toBe('iron_plate');
    });
  });

  describe('Simulation Control', () => {
    it('should toggle simulation state', () => {
      const { result } = renderHook(() => useGameStore());

      expect(result.current.isRunning).toBe(false);

      act(() => {
        result.current.toggleSimulation();
      });

      expect(result.current.isRunning).toBe(true);

      act(() => {
        result.current.toggleSimulation();
      });

      expect(result.current.isRunning).toBe(false);
    });
  });

  describe('Game State Serialization', () => {
    it('should export game state as JSON', () => {
      const { result } = renderHook(() => useGameStore());

      act(() => {
        result.current.addComponent(ComponentType.CONVEYOR, { x: 0, y: 0 });
      });

      const exportedState = result.current.exportGameState();
      expect(exportedState).toBeTruthy();
      
      const parsed = JSON.parse(exportedState);
      expect(parsed.components).toBeDefined();
      expect(parsed.gridSize).toBeDefined();
      expect(parsed.resources).toBeDefined();
    });

    it('should import game state from JSON', () => {
      const { result } = renderHook(() => useGameStore());

      // First export a state
      act(() => {
        result.current.addComponent(ComponentType.CONVEYOR, { x: 0, y: 0 });
      });

      const exportedState = result.current.exportGameState();

      // Reset and import
      act(() => {
        result.current.resetGame();
        const imported = result.current.importGameState(exportedState);
        expect(imported).toBe(true);
      });

      expect(result.current.components.size).toBe(1);
    });

    it('should handle invalid JSON import gracefully', () => {
      const { result } = renderHook(() => useGameStore());

      act(() => {
        const imported = result.current.importGameState('invalid json');
        expect(imported).toBe(false);
      });
    });
  });

  describe('Resource Management', () => {
    it('should initialize with default resources', () => {
      const { result } = renderHook(() => useGameStore());

      expect(result.current.resources.get(ResourceType.IRON_ORE)).toBe(1000);
      expect(result.current.resources.get(ResourceType.COPPER_ORE)).toBe(1000);
      expect(result.current.resources.get(ResourceType.IRON_PLATE)).toBe(100);
    });
  });

  describe('Performance Analytics', () => {
    it('should provide factory analytics', () => {
      const { result } = renderHook(() => useGameStore());

      const analytics = result.current.getFactoryAnalytics();
      expect(analytics).toBeDefined();
      expect(analytics.overallEfficiency).toBeDefined();
      expect(analytics.totalThroughput).toBeDefined();
      expect(analytics.performanceScore).toBeDefined();
    });

    it('should provide historical data', () => {
      const { result } = renderHook(() => useGameStore());

      const historicalData = result.current.getHistoricalData('efficiency');
      expect(Array.isArray(historicalData)).toBe(true);
    });
  });
});
