import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import FactoryGame from '@/components/FactoryGame';

// Mock the game store
const mockUseGameStore = jest.fn();
jest.mock('@/store/gameStore', () => ({
  useGameStore: () => mockUseGameStore(),
}));

// Mock react-dnd
jest.mock('react-dnd', () => ({
  DndProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useDrop: () => [{ isOver: false }, jest.fn()],
  useDrag: () => [{ isDragging: false }, jest.fn()],
}));

jest.mock('react-dnd-html5-backend', () => ({
  HTML5Backend: {},
}));

// Mock child components
jest.mock('@/components/GameBoard', () => {
  return function MockGameBoard() {
    return <div data-testid="game-board">Game Board</div>;
  };
});

jest.mock('@/components/ComponentPalette', () => {
  return function MockComponentPalette() {
    return <div data-testid="component-palette">Component Palette</div>;
  };
});

jest.mock('@/components/StatsPanel', () => {
  return function MockStatsPanel() {
    return <div data-testid="stats-panel">Stats Panel</div>;
  };
});

describe('FactoryGame', () => {
  const mockGameState = {
    isRunning: false,
    toggleSimulation: jest.fn(),
    components: new Map(),
    resources: new Map(),
    statistics: {
      totalProduction: new Map(),
      totalConsumption: new Map(),
      efficiency: 0,
      bottlenecks: [],
    },
  };

  beforeEach(() => {
    mockUseGameStore.mockReturnValue(mockGameState);
    jest.clearAllMocks();
  });

  it('should render all main components', () => {
    render(<FactoryGame />);

    expect(screen.getByText('Factory Builder')).toBeInTheDocument();
    expect(screen.getByTestId('game-board')).toBeInTheDocument();
    expect(screen.getByTestId('component-palette')).toBeInTheDocument();
    expect(screen.getByTestId('stats-panel')).toBeInTheDocument();
  });

  it('should display start button when simulation is not running', () => {
    render(<FactoryGame />);

    const startButton = screen.getByRole('button', { name: /start simulation/i });
    expect(startButton).toBeInTheDocument();
    expect(startButton).toHaveClass('bg-green-600');
  });

  it('should display pause button when simulation is running', () => {
    mockUseGameStore.mockReturnValue({
      ...mockGameState,
      isRunning: true,
    });

    render(<FactoryGame />);

    const pauseButton = screen.getByRole('button', { name: /pause simulation/i });
    expect(pauseButton).toBeInTheDocument();
    expect(pauseButton).toHaveClass('bg-red-600');
  });

  it('should call toggleSimulation when button is clicked', () => {
    render(<FactoryGame />);

    const button = screen.getByRole('button', { name: /start simulation/i });
    fireEvent.click(button);

    expect(mockGameState.toggleSimulation).toHaveBeenCalledTimes(1);
  });

  it('should have proper layout structure', () => {
    render(<FactoryGame />);

    // Check header
    const header = screen.getByRole('banner');
    expect(header).toBeInTheDocument();
    expect(header).toHaveClass('bg-gray-800');

    // Check main content area
    const mainContent = screen.getByTestId('game-board').parentElement;
    expect(mainContent).toHaveClass('flex-1');
  });

  it('should be accessible', () => {
    render(<FactoryGame />);

    // Check for proper heading structure
    expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
    
    // Check for button accessibility
    const button = screen.getByRole('button', { name: /start simulation/i });
    expect(button).toBeInTheDocument();
  });
});
