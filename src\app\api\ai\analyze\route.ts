import { NextRequest, NextResponse } from 'next/server';
import { GameStateSerializer, SerializedGameState } from '@/ai/gameStateSerializer';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { gameState, analytics } = body;

    if (!gameState) {
      return NextResponse.json(
        { error: 'Game state is required' },
        { status: 400 }
      );
    }

    // Serialize the game state for AI analysis
    const serialized = GameStateSerializer.serialize(gameState, analytics);
    
    // Generate AI prompt
    const prompt = GameStateSerializer.generateAIPrompt(serialized);

    return NextResponse.json({
      success: true,
      data: {
        serializedState: serialized,
        aiPrompt: prompt,
        metadata: {
          componentCount: serialized.components.length,
          efficiency: serialized.statistics.efficiency,
          isRunning: serialized.metadata.isRunning,
          timestamp: serialized.metadata.timestamp,
        },
      },
    });
  } catch (error) {
    console.error('Error analyzing game state:', error);
    return NextResponse.json(
      { error: 'Failed to analyze game state' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Factory Builder AI Analysis API',
    version: '1.0.0',
    endpoints: {
      analyze: {
        method: 'POST',
        description: 'Analyze factory state and generate AI prompt',
        parameters: {
          gameState: 'Required. The current game state object',
          analytics: 'Optional. Factory analytics data',
        },
      },
      import: {
        method: 'POST',
        description: 'Import and validate serialized game state',
        parameters: {
          serializedState: 'Required. Serialized game state JSON',
        },
      },
    },
    gameInfo: {
      componentTypes: [
        'conveyor - Transport items between components',
        'miner - Extract raw resources',
        'assembler - Craft items from materials',
        'storage - Store items',
        'splitter - Split input into multiple outputs',
        'merger - Merge multiple inputs into one output',
      ],
      resourceTypes: [
        'iron_ore - Raw iron ore',
        'copper_ore - Raw copper ore',
        'coal - Raw coal',
        'iron_plate - Processed iron',
        'copper_plate - Processed copper',
        'gear - Iron gear wheel',
        'circuit - Electronic circuit',
      ],
      recipes: [
        'iron_plate: 1 iron_ore → 1 iron_plate (3.2s)',
        'copper_plate: 1 copper_ore → 1 copper_plate (3.2s)',
        'gear: 2 iron_plate → 1 gear (0.5s)',
        'circuit: 1 iron_plate + 3 copper_plate → 1 circuit (0.5s)',
      ],
    },
  });
}
