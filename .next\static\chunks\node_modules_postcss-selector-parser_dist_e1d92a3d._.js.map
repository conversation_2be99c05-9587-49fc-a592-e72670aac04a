{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/util/unesc.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = unesc;\n// Many thanks for this post which made this migration much easier.\n// https://mathiasbynens.be/notes/css-escapes\n\n/**\n * \n * @param {string} str \n * @returns {[string, number]|undefined}\n */\nfunction gobbleHex(str) {\n  var lower = str.toLowerCase();\n  var hex = '';\n  var spaceTerminated = false;\n  for (var i = 0; i < 6 && lower[i] !== undefined; i++) {\n    var code = lower.charCodeAt(i);\n    // check to see if we are dealing with a valid hex char [a-f|0-9]\n    var valid = code >= 97 && code <= 102 || code >= 48 && code <= 57;\n    // https://drafts.csswg.org/css-syntax/#consume-escaped-code-point\n    spaceTerminated = code === 32;\n    if (!valid) {\n      break;\n    }\n    hex += lower[i];\n  }\n  if (hex.length === 0) {\n    return undefined;\n  }\n  var codePoint = parseInt(hex, 16);\n  var isSurrogate = codePoint >= 0xD800 && codePoint <= 0xDFFF;\n  // Add special case for\n  // \"If this number is zero, or is for a surrogate, or is greater than the maximum allowed code point\"\n  // https://drafts.csswg.org/css-syntax/#maximum-allowed-code-point\n  if (isSurrogate || codePoint === 0x0000 || codePoint > 0x10FFFF) {\n    return [\"\\uFFFD\", hex.length + (spaceTerminated ? 1 : 0)];\n  }\n  return [String.fromCodePoint(codePoint), hex.length + (spaceTerminated ? 1 : 0)];\n}\nvar CONTAINS_ESCAPE = /\\\\/;\nfunction unesc(str) {\n  var needToProcess = CONTAINS_ESCAPE.test(str);\n  if (!needToProcess) {\n    return str;\n  }\n  var ret = \"\";\n  for (var i = 0; i < str.length; i++) {\n    if (str[i] === \"\\\\\") {\n      var gobbled = gobbleHex(str.slice(i + 1, i + 7));\n      if (gobbled !== undefined) {\n        ret += gobbled[0];\n        i += gobbled[1];\n        continue;\n      }\n\n      // Retain a pair of \\\\ if double escaped `\\\\\\\\`\n      // https://github.com/postcss/postcss-selector-parser/commit/268c9a7656fb53f543dc620aa5b73a30ec3ff20e\n      if (str[i + 1] === \"\\\\\") {\n        ret += \"\\\\\";\n        i++;\n        continue;\n      }\n\n      // if \\\\ is at the end of the string retain it\n      // https://github.com/postcss/postcss-selector-parser/commit/01a6b346e3612ce1ab20219acc26abdc259ccefb\n      if (str.length === i + 1) {\n        ret += str[i];\n      }\n      continue;\n    }\n    ret += str[i];\n  }\n  return ret;\n}\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG;AACrB,mEAAmE;AACnE,6CAA6C;AAE7C;;;;CAIC,GACD,SAAS,UAAU,GAAG;IACpB,IAAI,QAAQ,IAAI,WAAW;IAC3B,IAAI,MAAM;IACV,IAAI,kBAAkB;IACtB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,CAAC,EAAE,KAAK,WAAW,IAAK;QACpD,IAAI,OAAO,MAAM,UAAU,CAAC;QAC5B,iEAAiE;QACjE,IAAI,QAAQ,QAAQ,MAAM,QAAQ,OAAO,QAAQ,MAAM,QAAQ;QAC/D,kEAAkE;QAClE,kBAAkB,SAAS;QAC3B,IAAI,CAAC,OAAO;YACV;QACF;QACA,OAAO,KAAK,CAAC,EAAE;IACjB;IACA,IAAI,IAAI,MAAM,KAAK,GAAG;QACpB,OAAO;IACT;IACA,IAAI,YAAY,SAAS,KAAK;IAC9B,IAAI,cAAc,aAAa,UAAU,aAAa;IACtD,uBAAuB;IACvB,qGAAqG;IACrG,kEAAkE;IAClE,IAAI,eAAe,cAAc,UAAU,YAAY,UAAU;QAC/D,OAAO;YAAC;YAAU,IAAI,MAAM,GAAG,CAAC,kBAAkB,IAAI,CAAC;SAAE;IAC3D;IACA,OAAO;QAAC,OAAO,aAAa,CAAC;QAAY,IAAI,MAAM,GAAG,CAAC,kBAAkB,IAAI,CAAC;KAAE;AAClF;AACA,IAAI,kBAAkB;AACtB,SAAS,MAAM,GAAG;IAChB,IAAI,gBAAgB,gBAAgB,IAAI,CAAC;IACzC,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IACA,IAAI,MAAM;IACV,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACnC,IAAI,GAAG,CAAC,EAAE,KAAK,MAAM;YACnB,IAAI,UAAU,UAAU,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI;YAC7C,IAAI,YAAY,WAAW;gBACzB,OAAO,OAAO,CAAC,EAAE;gBACjB,KAAK,OAAO,CAAC,EAAE;gBACf;YACF;YAEA,+CAA+C;YAC/C,qGAAqG;YACrG,IAAI,GAAG,CAAC,IAAI,EAAE,KAAK,MAAM;gBACvB,OAAO;gBACP;gBACA;YACF;YAEA,8CAA8C;YAC9C,qGAAqG;YACrG,IAAI,IAAI,MAAM,KAAK,IAAI,GAAG;gBACxB,OAAO,GAAG,CAAC,EAAE;YACf;YACA;QACF;QACA,OAAO,GAAG,CAAC,EAAE;IACf;IACA,OAAO;AACT;AACA,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/util/getProp.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = getProp;\nfunction getProp(obj) {\n  for (var _len = arguments.length, props = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    props[_key - 1] = arguments[_key];\n  }\n  while (props.length > 0) {\n    var prop = props.shift();\n    if (!obj[prop]) {\n      return undefined;\n    }\n    obj = obj[prop];\n  }\n  return obj;\n}\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG;AACrB,SAAS,QAAQ,GAAG;IAClB,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,QAAQ,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,GAAG,OAAO,MAAM,OAAQ;QAC3G,KAAK,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,KAAK;IACnC;IACA,MAAO,MAAM,MAAM,GAAG,EAAG;QACvB,IAAI,OAAO,MAAM,KAAK;QACtB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;YACd,OAAO;QACT;QACA,MAAM,GAAG,CAAC,KAAK;IACjB;IACA,OAAO;AACT;AACA,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/util/ensureObject.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = ensureObject;\nfunction ensureObject(obj) {\n  for (var _len = arguments.length, props = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    props[_key - 1] = arguments[_key];\n  }\n  while (props.length > 0) {\n    var prop = props.shift();\n    if (!obj[prop]) {\n      obj[prop] = {};\n    }\n    obj = obj[prop];\n  }\n}\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG;AACrB,SAAS,aAAa,GAAG;IACvB,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,QAAQ,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,GAAG,OAAO,MAAM,OAAQ;QAC3G,KAAK,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,KAAK;IACnC;IACA,MAAO,MAAM,MAAM,GAAG,EAAG;QACvB,IAAI,OAAO,MAAM,KAAK;QACtB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;YACd,GAAG,CAAC,KAAK,GAAG,CAAC;QACf;QACA,MAAM,GAAG,CAAC,KAAK;IACjB;AACF;AACA,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/util/stripComments.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = stripComments;\nfunction stripComments(str) {\n  var s = \"\";\n  var commentStart = str.indexOf(\"/*\");\n  var lastEnd = 0;\n  while (commentStart >= 0) {\n    s = s + str.slice(lastEnd, commentStart);\n    var commentEnd = str.indexOf(\"*/\", commentStart + 2);\n    if (commentEnd < 0) {\n      return s;\n    }\n    lastEnd = commentEnd + 2;\n    commentStart = str.indexOf(\"/*\", lastEnd);\n  }\n  s = s + str.slice(lastEnd);\n  return s;\n}\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG;AACrB,SAAS,cAAc,GAAG;IACxB,IAAI,IAAI;IACR,IAAI,eAAe,IAAI,OAAO,CAAC;IAC/B,IAAI,UAAU;IACd,MAAO,gBAAgB,EAAG;QACxB,IAAI,IAAI,IAAI,KAAK,CAAC,SAAS;QAC3B,IAAI,aAAa,IAAI,OAAO,CAAC,MAAM,eAAe;QAClD,IAAI,aAAa,GAAG;YAClB,OAAO;QACT;QACA,UAAU,aAAa;QACvB,eAAe,IAAI,OAAO,CAAC,MAAM;IACnC;IACA,IAAI,IAAI,IAAI,KAAK,CAAC;IAClB,OAAO;AACT;AACA,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/util/index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.unesc = exports.stripComments = exports.getProp = exports.ensureObject = void 0;\nvar _unesc = _interopRequireDefault(require(\"./unesc\"));\nexports.unesc = _unesc[\"default\"];\nvar _getProp = _interopRequireDefault(require(\"./getProp\"));\nexports.getProp = _getProp[\"default\"];\nvar _ensureObject = _interopRequireDefault(require(\"./ensureObject\"));\nexports.ensureObject = _ensureObject[\"default\"];\nvar _stripComments = _interopRequireDefault(require(\"./stripComments\"));\nexports.stripComments = _stripComments[\"default\"];\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,KAAK,GAAG,QAAQ,aAAa,GAAG,QAAQ,OAAO,GAAG,QAAQ,YAAY,GAAG,KAAK;AACtF,IAAI,SAAS;AACb,QAAQ,KAAK,GAAG,MAAM,CAAC,UAAU;AACjC,IAAI,WAAW;AACf,QAAQ,OAAO,GAAG,QAAQ,CAAC,UAAU;AACrC,IAAI,gBAAgB;AACpB,QAAQ,YAAY,GAAG,aAAa,CAAC,UAAU;AAC/C,IAAI,iBAAiB;AACrB,QAAQ,aAAa,GAAG,cAAc,CAAC,UAAU;AACjD,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/node.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _util = require(\"../util\");\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nvar cloneNode = function cloneNode(obj, parent) {\n  if (typeof obj !== 'object' || obj === null) {\n    return obj;\n  }\n  var cloned = new obj.constructor();\n  for (var i in obj) {\n    if (!obj.hasOwnProperty(i)) {\n      continue;\n    }\n    var value = obj[i];\n    var type = typeof value;\n    if (i === 'parent' && type === 'object') {\n      if (parent) {\n        cloned[i] = parent;\n      }\n    } else if (value instanceof Array) {\n      cloned[i] = value.map(function (j) {\n        return cloneNode(j, cloned);\n      });\n    } else {\n      cloned[i] = cloneNode(value, cloned);\n    }\n  }\n  return cloned;\n};\nvar Node = /*#__PURE__*/function () {\n  function Node(opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    Object.assign(this, opts);\n    this.spaces = this.spaces || {};\n    this.spaces.before = this.spaces.before || '';\n    this.spaces.after = this.spaces.after || '';\n  }\n  var _proto = Node.prototype;\n  _proto.remove = function remove() {\n    if (this.parent) {\n      this.parent.removeChild(this);\n    }\n    this.parent = undefined;\n    return this;\n  };\n  _proto.replaceWith = function replaceWith() {\n    if (this.parent) {\n      for (var index in arguments) {\n        this.parent.insertBefore(this, arguments[index]);\n      }\n      this.remove();\n    }\n    return this;\n  };\n  _proto.next = function next() {\n    return this.parent.at(this.parent.index(this) + 1);\n  };\n  _proto.prev = function prev() {\n    return this.parent.at(this.parent.index(this) - 1);\n  };\n  _proto.clone = function clone(overrides) {\n    if (overrides === void 0) {\n      overrides = {};\n    }\n    var cloned = cloneNode(this);\n    for (var name in overrides) {\n      cloned[name] = overrides[name];\n    }\n    return cloned;\n  }\n\n  /**\n   * Some non-standard syntax doesn't follow normal escaping rules for css.\n   * This allows non standard syntax to be appended to an existing property\n   * by specifying the escaped value. By specifying the escaped value,\n   * illegal characters are allowed to be directly inserted into css output.\n   * @param {string} name the property to set\n   * @param {any} value the unescaped value of the property\n   * @param {string} valueEscaped optional. the escaped value of the property.\n   */;\n  _proto.appendToPropertyAndEscape = function appendToPropertyAndEscape(name, value, valueEscaped) {\n    if (!this.raws) {\n      this.raws = {};\n    }\n    var originalValue = this[name];\n    var originalEscaped = this.raws[name];\n    this[name] = originalValue + value; // this may trigger a setter that updates raws, so it has to be set first.\n    if (originalEscaped || valueEscaped !== value) {\n      this.raws[name] = (originalEscaped || originalValue) + valueEscaped;\n    } else {\n      delete this.raws[name]; // delete any escaped value that was created by the setter.\n    }\n  }\n\n  /**\n   * Some non-standard syntax doesn't follow normal escaping rules for css.\n   * This allows the escaped value to be specified directly, allowing illegal\n   * characters to be directly inserted into css output.\n   * @param {string} name the property to set\n   * @param {any} value the unescaped value of the property\n   * @param {string} valueEscaped the escaped value of the property.\n   */;\n  _proto.setPropertyAndEscape = function setPropertyAndEscape(name, value, valueEscaped) {\n    if (!this.raws) {\n      this.raws = {};\n    }\n    this[name] = value; // this may trigger a setter that updates raws, so it has to be set first.\n    this.raws[name] = valueEscaped;\n  }\n\n  /**\n   * When you want a value to passed through to CSS directly. This method\n   * deletes the corresponding raw value causing the stringifier to fallback\n   * to the unescaped value.\n   * @param {string} name the property to set.\n   * @param {any} value The value that is both escaped and unescaped.\n   */;\n  _proto.setPropertyWithoutEscape = function setPropertyWithoutEscape(name, value) {\n    this[name] = value; // this may trigger a setter that updates raws, so it has to be set first.\n    if (this.raws) {\n      delete this.raws[name];\n    }\n  }\n\n  /**\n   *\n   * @param {number} line The number (starting with 1)\n   * @param {number} column The column number (starting with 1)\n   */;\n  _proto.isAtPosition = function isAtPosition(line, column) {\n    if (this.source && this.source.start && this.source.end) {\n      if (this.source.start.line > line) {\n        return false;\n      }\n      if (this.source.end.line < line) {\n        return false;\n      }\n      if (this.source.start.line === line && this.source.start.column > column) {\n        return false;\n      }\n      if (this.source.end.line === line && this.source.end.column < column) {\n        return false;\n      }\n      return true;\n    }\n    return undefined;\n  };\n  _proto.stringifyProperty = function stringifyProperty(name) {\n    return this.raws && this.raws[name] || this[name];\n  };\n  _proto.valueToString = function valueToString() {\n    return String(this.stringifyProperty(\"value\"));\n  };\n  _proto.toString = function toString() {\n    return [this.rawSpaceBefore, this.valueToString(), this.rawSpaceAfter].join('');\n  };\n  _createClass(Node, [{\n    key: \"rawSpaceBefore\",\n    get: function get() {\n      var rawSpace = this.raws && this.raws.spaces && this.raws.spaces.before;\n      if (rawSpace === undefined) {\n        rawSpace = this.spaces && this.spaces.before;\n      }\n      return rawSpace || \"\";\n    },\n    set: function set(raw) {\n      (0, _util.ensureObject)(this, \"raws\", \"spaces\");\n      this.raws.spaces.before = raw;\n    }\n  }, {\n    key: \"rawSpaceAfter\",\n    get: function get() {\n      var rawSpace = this.raws && this.raws.spaces && this.raws.spaces.after;\n      if (rawSpace === undefined) {\n        rawSpace = this.spaces.after;\n      }\n      return rawSpace || \"\";\n    },\n    set: function set(raw) {\n      (0, _util.ensureObject)(this, \"raws\", \"spaces\");\n      this.raws.spaces.after = raw;\n    }\n  }]);\n  return Node;\n}();\nexports[\"default\"] = Node;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI;AACJ,SAAS,kBAAkB,MAAM,EAAE,KAAK;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QAAE,IAAI,aAAa,KAAK,CAAC,EAAE;QAAE,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI;QAAO,WAAW,YAAY,GAAG;QAAM,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG;QAAM,OAAO,cAAc,CAAC,QAAQ,WAAW,GAAG,EAAE;IAAa;AAAE;AAC5T,SAAS,aAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IAAI,IAAI,YAAY,kBAAkB,YAAY,SAAS,EAAE;IAAa,IAAI,aAAa,kBAAkB,aAAa;IAAc,OAAO,cAAc,CAAC,aAAa,aAAa;QAAE,UAAU;IAAM;IAAI,OAAO;AAAa;AAC5R,IAAI,YAAY,SAAS,UAAU,GAAG,EAAE,MAAM;IAC5C,IAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;QAC3C,OAAO;IACT;IACA,IAAI,SAAS,IAAI,IAAI,WAAW;IAChC,IAAK,IAAI,KAAK,IAAK;QACjB,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI;YAC1B;QACF;QACA,IAAI,QAAQ,GAAG,CAAC,EAAE;QAClB,IAAI,OAAO,OAAO;QAClB,IAAI,MAAM,YAAY,SAAS,UAAU;YACvC,IAAI,QAAQ;gBACV,MAAM,CAAC,EAAE,GAAG;YACd;QACF,OAAO,IAAI,iBAAiB,OAAO;YACjC,MAAM,CAAC,EAAE,GAAG,MAAM,GAAG,CAAC,SAAU,CAAC;gBAC/B,OAAO,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,EAAE,GAAG,UAAU,OAAO;QAC/B;IACF;IACA,OAAO;AACT;AACA,IAAI,OAAO,WAAW,GAAE;IACtB,SAAS,KAAK,IAAI;QAChB,IAAI,SAAS,KAAK,GAAG;YACnB,OAAO,CAAC;QACV;QACA,OAAO,MAAM,CAAC,IAAI,EAAE;QACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI;QAC3C,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI;IAC3C;IACA,IAAI,SAAS,KAAK,SAAS;IAC3B,OAAO,MAAM,GAAG,SAAS;QACvB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI;QAC9B;QACA,IAAI,CAAC,MAAM,GAAG;QACd,OAAO,IAAI;IACb;IACA,OAAO,WAAW,GAAG,SAAS;QAC5B,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAK,IAAI,SAAS,UAAW;gBAC3B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,MAAM;YACjD;YACA,IAAI,CAAC,MAAM;QACb;QACA,OAAO,IAAI;IACb;IACA,OAAO,IAAI,GAAG,SAAS;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI;IAClD;IACA,OAAO,IAAI,GAAG,SAAS;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI;IAClD;IACA,OAAO,KAAK,GAAG,SAAS,MAAM,SAAS;QACrC,IAAI,cAAc,KAAK,GAAG;YACxB,YAAY,CAAC;QACf;QACA,IAAI,SAAS,UAAU,IAAI;QAC3B,IAAK,IAAI,QAAQ,UAAW;YAC1B,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAChC;QACA,OAAO;IACT;IAWA,OAAO,yBAAyB,GAAG,SAAS,0BAA0B,IAAI,EAAE,KAAK,EAAE,YAAY;QAC7F,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACd,IAAI,CAAC,IAAI,GAAG,CAAC;QACf;QACA,IAAI,gBAAgB,IAAI,CAAC,KAAK;QAC9B,IAAI,kBAAkB,IAAI,CAAC,IAAI,CAAC,KAAK;QACrC,IAAI,CAAC,KAAK,GAAG,gBAAgB,OAAO,0EAA0E;QAC9G,IAAI,mBAAmB,iBAAiB,OAAO;YAC7C,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,mBAAmB,aAAa,IAAI;QACzD,OAAO;YACL,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,2DAA2D;QACrF;IACF;IAUA,OAAO,oBAAoB,GAAG,SAAS,qBAAqB,IAAI,EAAE,KAAK,EAAE,YAAY;QACnF,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACd,IAAI,CAAC,IAAI,GAAG,CAAC;QACf;QACA,IAAI,CAAC,KAAK,GAAG,OAAO,0EAA0E;QAC9F,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG;IACpB;IASA,OAAO,wBAAwB,GAAG,SAAS,yBAAyB,IAAI,EAAE,KAAK;QAC7E,IAAI,CAAC,KAAK,GAAG,OAAO,0EAA0E;QAC9F,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;QACxB;IACF;IAOA,OAAO,YAAY,GAAG,SAAS,aAAa,IAAI,EAAE,MAAM;QACtD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YACvD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,MAAM;gBACjC,OAAO;YACT;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,GAAG,MAAM;gBAC/B,OAAO;YACT;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,QAAQ;gBACxE,OAAO;YACT;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,QAAQ;gBACpE,OAAO;YACT;YACA,OAAO;QACT;QACA,OAAO;IACT;IACA,OAAO,iBAAiB,GAAG,SAAS,kBAAkB,IAAI;QACxD,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK;IACnD;IACA,OAAO,aAAa,GAAG,SAAS;QAC9B,OAAO,OAAO,IAAI,CAAC,iBAAiB,CAAC;IACvC;IACA,OAAO,QAAQ,GAAG,SAAS;QACzB,OAAO;YAAC,IAAI,CAAC,cAAc;YAAE,IAAI,CAAC,aAAa;YAAI,IAAI,CAAC,aAAa;SAAC,CAAC,IAAI,CAAC;IAC9E;IACA,aAAa,MAAM;QAAC;YAClB,KAAK;YACL,KAAK,SAAS;gBACZ,IAAI,WAAW,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;gBACvE,IAAI,aAAa,WAAW;oBAC1B,WAAW,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM;gBAC9C;gBACA,OAAO,YAAY;YACrB;YACA,KAAK,SAAS,IAAI,GAAG;gBACnB,CAAC,GAAG,MAAM,YAAY,EAAE,IAAI,EAAE,QAAQ;gBACtC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;YAC5B;QACF;QAAG;YACD,KAAK;YACL,KAAK,SAAS;gBACZ,IAAI,WAAW,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK;gBACtE,IAAI,aAAa,WAAW;oBAC1B,WAAW,IAAI,CAAC,MAAM,CAAC,KAAK;gBAC9B;gBACA,OAAO,YAAY;YACrB;YACA,KAAK,SAAS,IAAI,GAAG;gBACnB,CAAC,GAAG,MAAM,YAAY,EAAE,IAAI,EAAE,QAAQ;gBACtC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG;YAC3B;QACF;KAAE;IACF,OAAO;AACT;AACA,OAAO,CAAC,UAAU,GAAG;AACrB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/types.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.UNIVERSAL = exports.TAG = exports.STRING = exports.SELECTOR = exports.ROOT = exports.PSEUDO = exports.NESTING = exports.ID = exports.COMMENT = exports.COMBINATOR = exports.CLASS = exports.ATTRIBUTE = void 0;\nvar TAG = 'tag';\nexports.TAG = TAG;\nvar STRING = 'string';\nexports.STRING = STRING;\nvar SELECTOR = 'selector';\nexports.SELECTOR = SELECTOR;\nvar ROOT = 'root';\nexports.ROOT = ROOT;\nvar PSEUDO = 'pseudo';\nexports.PSEUDO = PSEUDO;\nvar NESTING = 'nesting';\nexports.NESTING = NESTING;\nvar ID = 'id';\nexports.ID = ID;\nvar COMMENT = 'comment';\nexports.COMMENT = COMMENT;\nvar COMBINATOR = 'combinator';\nexports.COMBINATOR = COMBINATOR;\nvar CLASS = 'class';\nexports.CLASS = CLASS;\nvar ATTRIBUTE = 'attribute';\nexports.ATTRIBUTE = ATTRIBUTE;\nvar UNIVERSAL = 'universal';\nexports.UNIVERSAL = UNIVERSAL;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,SAAS,GAAG,QAAQ,GAAG,GAAG,QAAQ,MAAM,GAAG,QAAQ,QAAQ,GAAG,QAAQ,IAAI,GAAG,QAAQ,MAAM,GAAG,QAAQ,OAAO,GAAG,QAAQ,EAAE,GAAG,QAAQ,OAAO,GAAG,QAAQ,UAAU,GAAG,QAAQ,KAAK,GAAG,QAAQ,SAAS,GAAG,KAAK;AACrN,IAAI,MAAM;AACV,QAAQ,GAAG,GAAG;AACd,IAAI,SAAS;AACb,QAAQ,MAAM,GAAG;AACjB,IAAI,WAAW;AACf,QAAQ,QAAQ,GAAG;AACnB,IAAI,OAAO;AACX,QAAQ,IAAI,GAAG;AACf,IAAI,SAAS;AACb,QAAQ,MAAM,GAAG;AACjB,IAAI,UAAU;AACd,QAAQ,OAAO,GAAG;AAClB,IAAI,KAAK;AACT,QAAQ,EAAE,GAAG;AACb,IAAI,UAAU;AACd,QAAQ,OAAO,GAAG;AAClB,IAAI,aAAa;AACjB,QAAQ,UAAU,GAAG;AACrB,IAAI,QAAQ;AACZ,QAAQ,KAAK,GAAG;AAChB,IAAI,YAAY;AAChB,QAAQ,SAAS,GAAG;AACpB,IAAI,YAAY;AAChB,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/container.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _node = _interopRequireDefault(require(\"./node\"));\nvar types = _interopRequireWildcard(require(\"./types\"));\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nvar Container = /*#__PURE__*/function (_Node) {\n  _inheritsLoose(Container, _Node);\n  function Container(opts) {\n    var _this;\n    _this = _Node.call(this, opts) || this;\n    if (!_this.nodes) {\n      _this.nodes = [];\n    }\n    return _this;\n  }\n  var _proto = Container.prototype;\n  _proto.append = function append(selector) {\n    selector.parent = this;\n    this.nodes.push(selector);\n    return this;\n  };\n  _proto.prepend = function prepend(selector) {\n    selector.parent = this;\n    this.nodes.unshift(selector);\n    return this;\n  };\n  _proto.at = function at(index) {\n    return this.nodes[index];\n  };\n  _proto.index = function index(child) {\n    if (typeof child === 'number') {\n      return child;\n    }\n    return this.nodes.indexOf(child);\n  };\n  _proto.removeChild = function removeChild(child) {\n    child = this.index(child);\n    this.at(child).parent = undefined;\n    this.nodes.splice(child, 1);\n    var index;\n    for (var id in this.indexes) {\n      index = this.indexes[id];\n      if (index >= child) {\n        this.indexes[id] = index - 1;\n      }\n    }\n    return this;\n  };\n  _proto.removeAll = function removeAll() {\n    for (var _iterator = _createForOfIteratorHelperLoose(this.nodes), _step; !(_step = _iterator()).done;) {\n      var node = _step.value;\n      node.parent = undefined;\n    }\n    this.nodes = [];\n    return this;\n  };\n  _proto.empty = function empty() {\n    return this.removeAll();\n  };\n  _proto.insertAfter = function insertAfter(oldNode, newNode) {\n    newNode.parent = this;\n    var oldIndex = this.index(oldNode);\n    this.nodes.splice(oldIndex + 1, 0, newNode);\n    newNode.parent = this;\n    var index;\n    for (var id in this.indexes) {\n      index = this.indexes[id];\n      if (oldIndex <= index) {\n        this.indexes[id] = index + 1;\n      }\n    }\n    return this;\n  };\n  _proto.insertBefore = function insertBefore(oldNode, newNode) {\n    newNode.parent = this;\n    var oldIndex = this.index(oldNode);\n    this.nodes.splice(oldIndex, 0, newNode);\n    newNode.parent = this;\n    var index;\n    for (var id in this.indexes) {\n      index = this.indexes[id];\n      if (index <= oldIndex) {\n        this.indexes[id] = index + 1;\n      }\n    }\n    return this;\n  };\n  _proto._findChildAtPosition = function _findChildAtPosition(line, col) {\n    var found = undefined;\n    this.each(function (node) {\n      if (node.atPosition) {\n        var foundChild = node.atPosition(line, col);\n        if (foundChild) {\n          found = foundChild;\n          return false;\n        }\n      } else if (node.isAtPosition(line, col)) {\n        found = node;\n        return false;\n      }\n    });\n    return found;\n  }\n\n  /**\n   * Return the most specific node at the line and column number given.\n   * The source location is based on the original parsed location, locations aren't\n   * updated as selector nodes are mutated.\n   * \n   * Note that this location is relative to the location of the first character\n   * of the selector, and not the location of the selector in the overall document\n   * when used in conjunction with postcss.\n   *\n   * If not found, returns undefined.\n   * @param {number} line The line number of the node to find. (1-based index)\n   * @param {number} col  The column number of the node to find. (1-based index)\n   */;\n  _proto.atPosition = function atPosition(line, col) {\n    if (this.isAtPosition(line, col)) {\n      return this._findChildAtPosition(line, col) || this;\n    } else {\n      return undefined;\n    }\n  };\n  _proto._inferEndPosition = function _inferEndPosition() {\n    if (this.last && this.last.source && this.last.source.end) {\n      this.source = this.source || {};\n      this.source.end = this.source.end || {};\n      Object.assign(this.source.end, this.last.source.end);\n    }\n  };\n  _proto.each = function each(callback) {\n    if (!this.lastEach) {\n      this.lastEach = 0;\n    }\n    if (!this.indexes) {\n      this.indexes = {};\n    }\n    this.lastEach++;\n    var id = this.lastEach;\n    this.indexes[id] = 0;\n    if (!this.length) {\n      return undefined;\n    }\n    var index, result;\n    while (this.indexes[id] < this.length) {\n      index = this.indexes[id];\n      result = callback(this.at(index), index);\n      if (result === false) {\n        break;\n      }\n      this.indexes[id] += 1;\n    }\n    delete this.indexes[id];\n    if (result === false) {\n      return false;\n    }\n  };\n  _proto.walk = function walk(callback) {\n    return this.each(function (node, i) {\n      var result = callback(node, i);\n      if (result !== false && node.length) {\n        result = node.walk(callback);\n      }\n      if (result === false) {\n        return false;\n      }\n    });\n  };\n  _proto.walkAttributes = function walkAttributes(callback) {\n    var _this2 = this;\n    return this.walk(function (selector) {\n      if (selector.type === types.ATTRIBUTE) {\n        return callback.call(_this2, selector);\n      }\n    });\n  };\n  _proto.walkClasses = function walkClasses(callback) {\n    var _this3 = this;\n    return this.walk(function (selector) {\n      if (selector.type === types.CLASS) {\n        return callback.call(_this3, selector);\n      }\n    });\n  };\n  _proto.walkCombinators = function walkCombinators(callback) {\n    var _this4 = this;\n    return this.walk(function (selector) {\n      if (selector.type === types.COMBINATOR) {\n        return callback.call(_this4, selector);\n      }\n    });\n  };\n  _proto.walkComments = function walkComments(callback) {\n    var _this5 = this;\n    return this.walk(function (selector) {\n      if (selector.type === types.COMMENT) {\n        return callback.call(_this5, selector);\n      }\n    });\n  };\n  _proto.walkIds = function walkIds(callback) {\n    var _this6 = this;\n    return this.walk(function (selector) {\n      if (selector.type === types.ID) {\n        return callback.call(_this6, selector);\n      }\n    });\n  };\n  _proto.walkNesting = function walkNesting(callback) {\n    var _this7 = this;\n    return this.walk(function (selector) {\n      if (selector.type === types.NESTING) {\n        return callback.call(_this7, selector);\n      }\n    });\n  };\n  _proto.walkPseudos = function walkPseudos(callback) {\n    var _this8 = this;\n    return this.walk(function (selector) {\n      if (selector.type === types.PSEUDO) {\n        return callback.call(_this8, selector);\n      }\n    });\n  };\n  _proto.walkTags = function walkTags(callback) {\n    var _this9 = this;\n    return this.walk(function (selector) {\n      if (selector.type === types.TAG) {\n        return callback.call(_this9, selector);\n      }\n    });\n  };\n  _proto.walkUniversals = function walkUniversals(callback) {\n    var _this10 = this;\n    return this.walk(function (selector) {\n      if (selector.type === types.UNIVERSAL) {\n        return callback.call(_this10, selector);\n      }\n    });\n  };\n  _proto.split = function split(callback) {\n    var _this11 = this;\n    var current = [];\n    return this.reduce(function (memo, node, index) {\n      var split = callback.call(_this11, node);\n      current.push(node);\n      if (split) {\n        memo.push(current);\n        current = [];\n      } else if (index === _this11.length - 1) {\n        memo.push(current);\n      }\n      return memo;\n    }, []);\n  };\n  _proto.map = function map(callback) {\n    return this.nodes.map(callback);\n  };\n  _proto.reduce = function reduce(callback, memo) {\n    return this.nodes.reduce(callback, memo);\n  };\n  _proto.every = function every(callback) {\n    return this.nodes.every(callback);\n  };\n  _proto.some = function some(callback) {\n    return this.nodes.some(callback);\n  };\n  _proto.filter = function filter(callback) {\n    return this.nodes.filter(callback);\n  };\n  _proto.sort = function sort(callback) {\n    return this.nodes.sort(callback);\n  };\n  _proto.toString = function toString() {\n    return this.map(String).join('');\n  };\n  _createClass(Container, [{\n    key: \"first\",\n    get: function get() {\n      return this.at(0);\n    }\n  }, {\n    key: \"last\",\n    get: function get() {\n      return this.at(this.length - 1);\n    }\n  }, {\n    key: \"length\",\n    get: function get() {\n      return this.nodes.length;\n    }\n  }]);\n  return Container;\n}(_node[\"default\"]);\nexports[\"default\"] = Container;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,SAAS,yBAAyB,WAAW;IAAI,IAAI,OAAO,YAAY,YAAY,OAAO;IAAM,IAAI,oBAAoB,IAAI;IAAW,IAAI,mBAAmB,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAS,yBAAyB,WAAW;QAAI,OAAO,cAAc,mBAAmB;IAAmB,CAAC,EAAE;AAAc;AAC9U,SAAS,wBAAwB,GAAG,EAAE,WAAW;IAAI,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE;QAAE,OAAO;IAAK;IAAE,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY;QAAE,OAAO;YAAE,WAAW;QAAI;IAAG;IAAE,IAAI,QAAQ,yBAAyB;IAAc,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM;QAAE,OAAO,MAAM,GAAG,CAAC;IAAM;IAAE,IAAI,SAAS,CAAC;IAAG,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,OAAO,IAAK;QAAE,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YAAE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAAM,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG;gBAAE,OAAO,cAAc,CAAC,QAAQ,KAAK;YAAO,OAAO;gBAAE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;YAAE;QAAE;IAAE;IAAE,MAAM,CAAC,UAAU,GAAG;IAAK,IAAI,OAAO;QAAE,MAAM,GAAG,CAAC,KAAK;IAAS;IAAE,OAAO;AAAQ;AACxyB,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,SAAS,gCAAgC,CAAC,EAAE,cAAc;IAAI,IAAI,KAAK,OAAO,WAAW,eAAe,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa;IAAE,IAAI,IAAI,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC;IAAK,IAAI,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,4BAA4B,EAAE,KAAK,kBAAkB,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU;QAAE,IAAI,IAAI,IAAI;QAAI,IAAI,IAAI;QAAG,OAAO;YAAc,IAAI,KAAK,EAAE,MAAM,EAAE,OAAO;gBAAE,MAAM;YAAK;YAAG,OAAO;gBAAE,MAAM;gBAAO,OAAO,CAAC,CAAC,IAAI;YAAC;QAAG;IAAG;IAAE,MAAM,IAAI,UAAU;AAA0I;AAC3lB,SAAS,4BAA4B,CAAC,EAAE,MAAM;IAAI,IAAI,CAAC,GAAG;IAAQ,IAAI,OAAO,MAAM,UAAU,OAAO,kBAAkB,GAAG;IAAS,IAAI,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;IAAI,IAAI,MAAM,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,CAAC,IAAI;IAAE,IAAI,MAAM,SAAS,MAAM,OAAO,OAAO,MAAM,IAAI,CAAC;IAAI,IAAI,MAAM,eAAe,2CAA2C,IAAI,CAAC,IAAI,OAAO,kBAAkB,GAAG;AAAS;AAC/Z,SAAS,kBAAkB,GAAG,EAAE,GAAG;IAAI,IAAI,OAAO,QAAQ,MAAM,IAAI,MAAM,EAAE,MAAM,IAAI,MAAM;IAAE,IAAK,IAAI,IAAI,GAAG,OAAO,IAAI,MAAM,MAAM,IAAI,KAAK,IAAK;QAAE,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;IAAE;IAAE,OAAO;AAAM;AACtL,SAAS,kBAAkB,MAAM,EAAE,KAAK;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QAAE,IAAI,aAAa,KAAK,CAAC,EAAE;QAAE,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI;QAAO,WAAW,YAAY,GAAG;QAAM,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG;QAAM,OAAO,cAAc,CAAC,QAAQ,WAAW,GAAG,EAAE;IAAa;AAAE;AAC5T,SAAS,aAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IAAI,IAAI,YAAY,kBAAkB,YAAY,SAAS,EAAE;IAAa,IAAI,aAAa,kBAAkB,aAAa;IAAc,OAAO,cAAc,CAAC,aAAa,aAAa;QAAE,UAAU;IAAM;IAAI,OAAO;AAAa;AAC5R,SAAS,eAAe,QAAQ,EAAE,UAAU;IAAI,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,WAAW,SAAS;IAAG,SAAS,SAAS,CAAC,WAAW,GAAG;IAAU,gBAAgB,UAAU;AAAa;AAC5L,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAAI,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAS,gBAAgB,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;QAAG,OAAO;IAAG;IAAG,OAAO,gBAAgB,GAAG;AAAI;AACvM,IAAI,YAAY,WAAW,GAAE,SAAU,KAAK;IAC1C,eAAe,WAAW;IAC1B,SAAS,UAAU,IAAI;QACrB,IAAI;QACJ,QAAQ,MAAM,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI;QACtC,IAAI,CAAC,MAAM,KAAK,EAAE;YAChB,MAAM,KAAK,GAAG,EAAE;QAClB;QACA,OAAO;IACT;IACA,IAAI,SAAS,UAAU,SAAS;IAChC,OAAO,MAAM,GAAG,SAAS,OAAO,QAAQ;QACtC,SAAS,MAAM,GAAG,IAAI;QACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAChB,OAAO,IAAI;IACb;IACA,OAAO,OAAO,GAAG,SAAS,QAAQ,QAAQ;QACxC,SAAS,MAAM,GAAG,IAAI;QACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;QACnB,OAAO,IAAI;IACb;IACA,OAAO,EAAE,GAAG,SAAS,GAAG,KAAK;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IAC1B;IACA,OAAO,KAAK,GAAG,SAAS,MAAM,KAAK;QACjC,IAAI,OAAO,UAAU,UAAU;YAC7B,OAAO;QACT;QACA,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAC5B;IACA,OAAO,WAAW,GAAG,SAAS,YAAY,KAAK;QAC7C,QAAQ,IAAI,CAAC,KAAK,CAAC;QACnB,IAAI,CAAC,EAAE,CAAC,OAAO,MAAM,GAAG;QACxB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;QACzB,IAAI;QACJ,IAAK,IAAI,MAAM,IAAI,CAAC,OAAO,CAAE;YAC3B,QAAQ,IAAI,CAAC,OAAO,CAAC,GAAG;YACxB,IAAI,SAAS,OAAO;gBAClB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,QAAQ;YAC7B;QACF;QACA,OAAO,IAAI;IACb;IACA,OAAO,SAAS,GAAG,SAAS;QAC1B,IAAK,IAAI,YAAY,gCAAgC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC,QAAQ,WAAW,EAAE,IAAI,EAAG;YACrG,IAAI,OAAO,MAAM,KAAK;YACtB,KAAK,MAAM,GAAG;QAChB;QACA,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,OAAO,IAAI;IACb;IACA,OAAO,KAAK,GAAG,SAAS;QACtB,OAAO,IAAI,CAAC,SAAS;IACvB;IACA,OAAO,WAAW,GAAG,SAAS,YAAY,OAAO,EAAE,OAAO;QACxD,QAAQ,MAAM,GAAG,IAAI;QACrB,IAAI,WAAW,IAAI,CAAC,KAAK,CAAC;QAC1B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,GAAG,GAAG;QACnC,QAAQ,MAAM,GAAG,IAAI;QACrB,IAAI;QACJ,IAAK,IAAI,MAAM,IAAI,CAAC,OAAO,CAAE;YAC3B,QAAQ,IAAI,CAAC,OAAO,CAAC,GAAG;YACxB,IAAI,YAAY,OAAO;gBACrB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,QAAQ;YAC7B;QACF;QACA,OAAO,IAAI;IACb;IACA,OAAO,YAAY,GAAG,SAAS,aAAa,OAAO,EAAE,OAAO;QAC1D,QAAQ,MAAM,GAAG,IAAI;QACrB,IAAI,WAAW,IAAI,CAAC,KAAK,CAAC;QAC1B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,GAAG;QAC/B,QAAQ,MAAM,GAAG,IAAI;QACrB,IAAI;QACJ,IAAK,IAAI,MAAM,IAAI,CAAC,OAAO,CAAE;YAC3B,QAAQ,IAAI,CAAC,OAAO,CAAC,GAAG;YACxB,IAAI,SAAS,UAAU;gBACrB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,QAAQ;YAC7B;QACF;QACA,OAAO,IAAI;IACb;IACA,OAAO,oBAAoB,GAAG,SAAS,qBAAqB,IAAI,EAAE,GAAG;QACnE,IAAI,QAAQ;QACZ,IAAI,CAAC,IAAI,CAAC,SAAU,IAAI;YACtB,IAAI,KAAK,UAAU,EAAE;gBACnB,IAAI,aAAa,KAAK,UAAU,CAAC,MAAM;gBACvC,IAAI,YAAY;oBACd,QAAQ;oBACR,OAAO;gBACT;YACF,OAAO,IAAI,KAAK,YAAY,CAAC,MAAM,MAAM;gBACvC,QAAQ;gBACR,OAAO;YACT;QACF;QACA,OAAO;IACT;IAeA,OAAO,UAAU,GAAG,SAAS,WAAW,IAAI,EAAE,GAAG;QAC/C,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,MAAM;YAChC,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,QAAQ,IAAI;QACrD,OAAO;YACL,OAAO;QACT;IACF;IACA,OAAO,iBAAiB,GAAG,SAAS;QAClC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YACzD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,CAAC;YAC9B,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;YACtC,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG;QACrD;IACF;IACA,OAAO,IAAI,GAAG,SAAS,KAAK,QAAQ;QAClC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG;QAClB;QACA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG,CAAC;QAClB;QACA,IAAI,CAAC,QAAQ;QACb,IAAI,KAAK,IAAI,CAAC,QAAQ;QACtB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG;QACnB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,OAAO;QACT;QACA,IAAI,OAAO;QACX,MAAO,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAE;YACrC,QAAQ,IAAI,CAAC,OAAO,CAAC,GAAG;YACxB,SAAS,SAAS,IAAI,CAAC,EAAE,CAAC,QAAQ;YAClC,IAAI,WAAW,OAAO;gBACpB;YACF;YACA,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI;QACtB;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG;QACvB,IAAI,WAAW,OAAO;YACpB,OAAO;QACT;IACF;IACA,OAAO,IAAI,GAAG,SAAS,KAAK,QAAQ;QAClC,OAAO,IAAI,CAAC,IAAI,CAAC,SAAU,IAAI,EAAE,CAAC;YAChC,IAAI,SAAS,SAAS,MAAM;YAC5B,IAAI,WAAW,SAAS,KAAK,MAAM,EAAE;gBACnC,SAAS,KAAK,IAAI,CAAC;YACrB;YACA,IAAI,WAAW,OAAO;gBACpB,OAAO;YACT;QACF;IACF;IACA,OAAO,cAAc,GAAG,SAAS,eAAe,QAAQ;QACtD,IAAI,SAAS,IAAI;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAU,QAAQ;YACjC,IAAI,SAAS,IAAI,KAAK,MAAM,SAAS,EAAE;gBACrC,OAAO,SAAS,IAAI,CAAC,QAAQ;YAC/B;QACF;IACF;IACA,OAAO,WAAW,GAAG,SAAS,YAAY,QAAQ;QAChD,IAAI,SAAS,IAAI;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAU,QAAQ;YACjC,IAAI,SAAS,IAAI,KAAK,MAAM,KAAK,EAAE;gBACjC,OAAO,SAAS,IAAI,CAAC,QAAQ;YAC/B;QACF;IACF;IACA,OAAO,eAAe,GAAG,SAAS,gBAAgB,QAAQ;QACxD,IAAI,SAAS,IAAI;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAU,QAAQ;YACjC,IAAI,SAAS,IAAI,KAAK,MAAM,UAAU,EAAE;gBACtC,OAAO,SAAS,IAAI,CAAC,QAAQ;YAC/B;QACF;IACF;IACA,OAAO,YAAY,GAAG,SAAS,aAAa,QAAQ;QAClD,IAAI,SAAS,IAAI;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAU,QAAQ;YACjC,IAAI,SAAS,IAAI,KAAK,MAAM,OAAO,EAAE;gBACnC,OAAO,SAAS,IAAI,CAAC,QAAQ;YAC/B;QACF;IACF;IACA,OAAO,OAAO,GAAG,SAAS,QAAQ,QAAQ;QACxC,IAAI,SAAS,IAAI;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAU,QAAQ;YACjC,IAAI,SAAS,IAAI,KAAK,MAAM,EAAE,EAAE;gBAC9B,OAAO,SAAS,IAAI,CAAC,QAAQ;YAC/B;QACF;IACF;IACA,OAAO,WAAW,GAAG,SAAS,YAAY,QAAQ;QAChD,IAAI,SAAS,IAAI;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAU,QAAQ;YACjC,IAAI,SAAS,IAAI,KAAK,MAAM,OAAO,EAAE;gBACnC,OAAO,SAAS,IAAI,CAAC,QAAQ;YAC/B;QACF;IACF;IACA,OAAO,WAAW,GAAG,SAAS,YAAY,QAAQ;QAChD,IAAI,SAAS,IAAI;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAU,QAAQ;YACjC,IAAI,SAAS,IAAI,KAAK,MAAM,MAAM,EAAE;gBAClC,OAAO,SAAS,IAAI,CAAC,QAAQ;YAC/B;QACF;IACF;IACA,OAAO,QAAQ,GAAG,SAAS,SAAS,QAAQ;QAC1C,IAAI,SAAS,IAAI;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAU,QAAQ;YACjC,IAAI,SAAS,IAAI,KAAK,MAAM,GAAG,EAAE;gBAC/B,OAAO,SAAS,IAAI,CAAC,QAAQ;YAC/B;QACF;IACF;IACA,OAAO,cAAc,GAAG,SAAS,eAAe,QAAQ;QACtD,IAAI,UAAU,IAAI;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAU,QAAQ;YACjC,IAAI,SAAS,IAAI,KAAK,MAAM,SAAS,EAAE;gBACrC,OAAO,SAAS,IAAI,CAAC,SAAS;YAChC;QACF;IACF;IACA,OAAO,KAAK,GAAG,SAAS,MAAM,QAAQ;QACpC,IAAI,UAAU,IAAI;QAClB,IAAI,UAAU,EAAE;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAU,IAAI,EAAE,IAAI,EAAE,KAAK;YAC5C,IAAI,QAAQ,SAAS,IAAI,CAAC,SAAS;YACnC,QAAQ,IAAI,CAAC;YACb,IAAI,OAAO;gBACT,KAAK,IAAI,CAAC;gBACV,UAAU,EAAE;YACd,OAAO,IAAI,UAAU,QAAQ,MAAM,GAAG,GAAG;gBACvC,KAAK,IAAI,CAAC;YACZ;YACA,OAAO;QACT,GAAG,EAAE;IACP;IACA,OAAO,GAAG,GAAG,SAAS,IAAI,QAAQ;QAChC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;IACxB;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,QAAQ,EAAE,IAAI;QAC5C,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU;IACrC;IACA,OAAO,KAAK,GAAG,SAAS,MAAM,QAAQ;QACpC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1B;IACA,OAAO,IAAI,GAAG,SAAS,KAAK,QAAQ;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,QAAQ;QACtC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B;IACA,OAAO,IAAI,GAAG,SAAS,KAAK,QAAQ;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACzB;IACA,OAAO,QAAQ,GAAG,SAAS;QACzB,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC;IAC/B;IACA,aAAa,WAAW;QAAC;YACvB,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,EAAE,CAAC;YACjB;QACF;QAAG;YACD,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG;YAC/B;QACF;QAAG;YACD,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;YAC1B;QACF;KAAE;IACF,OAAO;AACT,EAAE,KAAK,CAAC,UAAU;AAClB,OAAO,CAAC,UAAU,GAAG;AACrB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 792, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/root.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _container = _interopRequireDefault(require(\"./container\"));\nvar _types = require(\"./types\");\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nvar Root = /*#__PURE__*/function (_Container) {\n  _inheritsLoose(Root, _Container);\n  function Root(opts) {\n    var _this;\n    _this = _Container.call(this, opts) || this;\n    _this.type = _types.ROOT;\n    return _this;\n  }\n  var _proto = Root.prototype;\n  _proto.toString = function toString() {\n    var str = this.reduce(function (memo, selector) {\n      memo.push(String(selector));\n      return memo;\n    }, []).join(',');\n    return this.trailingComma ? str + ',' : str;\n  };\n  _proto.error = function error(message, options) {\n    if (this._error) {\n      return this._error(message, options);\n    } else {\n      return new Error(message);\n    }\n  };\n  _createClass(Root, [{\n    key: \"errorGenerator\",\n    set: function set(handler) {\n      this._error = handler;\n    }\n  }]);\n  return Root;\n}(_container[\"default\"]);\nexports[\"default\"] = Root;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,aAAa;AACjB,IAAI;AACJ,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,SAAS,kBAAkB,MAAM,EAAE,KAAK;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QAAE,IAAI,aAAa,KAAK,CAAC,EAAE;QAAE,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI;QAAO,WAAW,YAAY,GAAG;QAAM,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG;QAAM,OAAO,cAAc,CAAC,QAAQ,WAAW,GAAG,EAAE;IAAa;AAAE;AAC5T,SAAS,aAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IAAI,IAAI,YAAY,kBAAkB,YAAY,SAAS,EAAE;IAAa,IAAI,aAAa,kBAAkB,aAAa;IAAc,OAAO,cAAc,CAAC,aAAa,aAAa;QAAE,UAAU;IAAM;IAAI,OAAO;AAAa;AAC5R,SAAS,eAAe,QAAQ,EAAE,UAAU;IAAI,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,WAAW,SAAS;IAAG,SAAS,SAAS,CAAC,WAAW,GAAG;IAAU,gBAAgB,UAAU;AAAa;AAC5L,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAAI,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAS,gBAAgB,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;QAAG,OAAO;IAAG;IAAG,OAAO,gBAAgB,GAAG;AAAI;AACvM,IAAI,OAAO,WAAW,GAAE,SAAU,UAAU;IAC1C,eAAe,MAAM;IACrB,SAAS,KAAK,IAAI;QAChB,IAAI;QACJ,QAAQ,WAAW,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI;QAC3C,MAAM,IAAI,GAAG,OAAO,IAAI;QACxB,OAAO;IACT;IACA,IAAI,SAAS,KAAK,SAAS;IAC3B,OAAO,QAAQ,GAAG,SAAS;QACzB,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,SAAU,IAAI,EAAE,QAAQ;YAC5C,KAAK,IAAI,CAAC,OAAO;YACjB,OAAO;QACT,GAAG,EAAE,EAAE,IAAI,CAAC;QACZ,OAAO,IAAI,CAAC,aAAa,GAAG,MAAM,MAAM;IAC1C;IACA,OAAO,KAAK,GAAG,SAAS,MAAM,OAAO,EAAE,OAAO;QAC5C,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS;QAC9B,OAAO;YACL,OAAO,IAAI,MAAM;QACnB;IACF;IACA,aAAa,MAAM;QAAC;YAClB,KAAK;YACL,KAAK,SAAS,IAAI,OAAO;gBACvB,IAAI,CAAC,MAAM,GAAG;YAChB;QACF;KAAE;IACF,OAAO;AACT,EAAE,UAAU,CAAC,UAAU;AACvB,OAAO,CAAC,UAAU,GAAG;AACrB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 871, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/selector.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _container = _interopRequireDefault(require(\"./container\"));\nvar _types = require(\"./types\");\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nvar Selector = /*#__PURE__*/function (_Container) {\n  _inheritsLoose(Selector, _Container);\n  function Selector(opts) {\n    var _this;\n    _this = _Container.call(this, opts) || this;\n    _this.type = _types.SELECTOR;\n    return _this;\n  }\n  return Selector;\n}(_container[\"default\"]);\nexports[\"default\"] = Selector;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,aAAa;AACjB,IAAI;AACJ,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,SAAS,eAAe,QAAQ,EAAE,UAAU;IAAI,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,WAAW,SAAS;IAAG,SAAS,SAAS,CAAC,WAAW,GAAG;IAAU,gBAAgB,UAAU;AAAa;AAC5L,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAAI,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAS,gBAAgB,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;QAAG,OAAO;IAAG;IAAG,OAAO,gBAAgB,GAAG;AAAI;AACvM,IAAI,WAAW,WAAW,GAAE,SAAU,UAAU;IAC9C,eAAe,UAAU;IACzB,SAAS,SAAS,IAAI;QACpB,IAAI;QACJ,QAAQ,WAAW,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI;QAC3C,MAAM,IAAI,GAAG,OAAO,QAAQ;QAC5B,OAAO;IACT;IACA,OAAO;AACT,EAAE,UAAU,CAAC,UAAU;AACvB,OAAO,CAAC,UAAU,GAAG;AACrB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 910, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/className.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _cssesc = _interopRequireDefault(require(\"cssesc\"));\nvar _util = require(\"../util\");\nvar _node = _interopRequireDefault(require(\"./node\"));\nvar _types = require(\"./types\");\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nvar ClassName = /*#__PURE__*/function (_Node) {\n  _inheritsLoose(ClassName, _Node);\n  function ClassName(opts) {\n    var _this;\n    _this = _Node.call(this, opts) || this;\n    _this.type = _types.CLASS;\n    _this._constructed = true;\n    return _this;\n  }\n  var _proto = ClassName.prototype;\n  _proto.valueToString = function valueToString() {\n    return '.' + _Node.prototype.valueToString.call(this);\n  };\n  _createClass(ClassName, [{\n    key: \"value\",\n    get: function get() {\n      return this._value;\n    },\n    set: function set(v) {\n      if (this._constructed) {\n        var escaped = (0, _cssesc[\"default\"])(v, {\n          isIdentifier: true\n        });\n        if (escaped !== v) {\n          (0, _util.ensureObject)(this, \"raws\");\n          this.raws.value = escaped;\n        } else if (this.raws) {\n          delete this.raws.value;\n        }\n      }\n      this._value = v;\n    }\n  }]);\n  return ClassName;\n}(_node[\"default\"]);\nexports[\"default\"] = ClassName;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,UAAU;AACd,IAAI;AACJ,IAAI,QAAQ;AACZ,IAAI;AACJ,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,SAAS,kBAAkB,MAAM,EAAE,KAAK;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QAAE,IAAI,aAAa,KAAK,CAAC,EAAE;QAAE,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI;QAAO,WAAW,YAAY,GAAG;QAAM,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG;QAAM,OAAO,cAAc,CAAC,QAAQ,WAAW,GAAG,EAAE;IAAa;AAAE;AAC5T,SAAS,aAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IAAI,IAAI,YAAY,kBAAkB,YAAY,SAAS,EAAE;IAAa,IAAI,aAAa,kBAAkB,aAAa;IAAc,OAAO,cAAc,CAAC,aAAa,aAAa;QAAE,UAAU;IAAM;IAAI,OAAO;AAAa;AAC5R,SAAS,eAAe,QAAQ,EAAE,UAAU;IAAI,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,WAAW,SAAS;IAAG,SAAS,SAAS,CAAC,WAAW,GAAG;IAAU,gBAAgB,UAAU;AAAa;AAC5L,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAAI,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAS,gBAAgB,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;QAAG,OAAO;IAAG;IAAG,OAAO,gBAAgB,GAAG;AAAI;AACvM,IAAI,YAAY,WAAW,GAAE,SAAU,KAAK;IAC1C,eAAe,WAAW;IAC1B,SAAS,UAAU,IAAI;QACrB,IAAI;QACJ,QAAQ,MAAM,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI;QACtC,MAAM,IAAI,GAAG,OAAO,KAAK;QACzB,MAAM,YAAY,GAAG;QACrB,OAAO;IACT;IACA,IAAI,SAAS,UAAU,SAAS;IAChC,OAAO,aAAa,GAAG,SAAS;QAC9B,OAAO,MAAM,MAAM,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI;IACtD;IACA,aAAa,WAAW;QAAC;YACvB,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,MAAM;YACpB;YACA,KAAK,SAAS,IAAI,CAAC;gBACjB,IAAI,IAAI,CAAC,YAAY,EAAE;oBACrB,IAAI,UAAU,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE,GAAG;wBACvC,cAAc;oBAChB;oBACA,IAAI,YAAY,GAAG;wBACjB,CAAC,GAAG,MAAM,YAAY,EAAE,IAAI,EAAE;wBAC9B,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG;oBACpB,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE;wBACpB,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;oBACxB;gBACF;gBACA,IAAI,CAAC,MAAM,GAAG;YAChB;QACF;KAAE;IACF,OAAO;AACT,EAAE,KAAK,CAAC,UAAU;AAClB,OAAO,CAAC,UAAU,GAAG;AACrB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 995, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/comment.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _node = _interopRequireDefault(require(\"./node\"));\nvar _types = require(\"./types\");\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nvar Comment = /*#__PURE__*/function (_Node) {\n  _inheritsLoose(Comment, _Node);\n  function Comment(opts) {\n    var _this;\n    _this = _Node.call(this, opts) || this;\n    _this.type = _types.COMMENT;\n    return _this;\n  }\n  return Comment;\n}(_node[\"default\"]);\nexports[\"default\"] = Comment;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,QAAQ;AACZ,IAAI;AACJ,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,SAAS,eAAe,QAAQ,EAAE,UAAU;IAAI,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,WAAW,SAAS;IAAG,SAAS,SAAS,CAAC,WAAW,GAAG;IAAU,gBAAgB,UAAU;AAAa;AAC5L,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAAI,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAS,gBAAgB,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;QAAG,OAAO;IAAG;IAAG,OAAO,gBAAgB,GAAG;AAAI;AACvM,IAAI,UAAU,WAAW,GAAE,SAAU,KAAK;IACxC,eAAe,SAAS;IACxB,SAAS,QAAQ,IAAI;QACnB,IAAI;QACJ,QAAQ,MAAM,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI;QACtC,MAAM,IAAI,GAAG,OAAO,OAAO;QAC3B,OAAO;IACT;IACA,OAAO;AACT,EAAE,KAAK,CAAC,UAAU;AAClB,OAAO,CAAC,UAAU,GAAG;AACrB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/id.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _node = _interopRequireDefault(require(\"./node\"));\nvar _types = require(\"./types\");\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nvar ID = /*#__PURE__*/function (_Node) {\n  _inheritsLoose(ID, _Node);\n  function ID(opts) {\n    var _this;\n    _this = _Node.call(this, opts) || this;\n    _this.type = _types.ID;\n    return _this;\n  }\n  var _proto = ID.prototype;\n  _proto.valueToString = function valueToString() {\n    return '#' + _Node.prototype.valueToString.call(this);\n  };\n  return ID;\n}(_node[\"default\"]);\nexports[\"default\"] = ID;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,QAAQ;AACZ,IAAI;AACJ,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,SAAS,eAAe,QAAQ,EAAE,UAAU;IAAI,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,WAAW,SAAS;IAAG,SAAS,SAAS,CAAC,WAAW,GAAG;IAAU,gBAAgB,UAAU;AAAa;AAC5L,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAAI,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAS,gBAAgB,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;QAAG,OAAO;IAAG;IAAG,OAAO,gBAAgB,GAAG;AAAI;AACvM,IAAI,KAAK,WAAW,GAAE,SAAU,KAAK;IACnC,eAAe,IAAI;IACnB,SAAS,GAAG,IAAI;QACd,IAAI;QACJ,QAAQ,MAAM,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI;QACtC,MAAM,IAAI,GAAG,OAAO,EAAE;QACtB,OAAO;IACT;IACA,IAAI,SAAS,GAAG,SAAS;IACzB,OAAO,aAAa,GAAG,SAAS;QAC9B,OAAO,MAAM,MAAM,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI;IACtD;IACA,OAAO;AACT,EAAE,KAAK,CAAC,UAAU;AAClB,OAAO,CAAC,UAAU,GAAG;AACrB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1077, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/namespace.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _cssesc = _interopRequireDefault(require(\"cssesc\"));\nvar _util = require(\"../util\");\nvar _node = _interopRequireDefault(require(\"./node\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nvar Namespace = /*#__PURE__*/function (_Node) {\n  _inheritsLoose(Namespace, _Node);\n  function Namespace() {\n    return _Node.apply(this, arguments) || this;\n  }\n  var _proto = Namespace.prototype;\n  _proto.qualifiedName = function qualifiedName(value) {\n    if (this.namespace) {\n      return this.namespaceString + \"|\" + value;\n    } else {\n      return value;\n    }\n  };\n  _proto.valueToString = function valueToString() {\n    return this.qualifiedName(_Node.prototype.valueToString.call(this));\n  };\n  _createClass(Namespace, [{\n    key: \"namespace\",\n    get: function get() {\n      return this._namespace;\n    },\n    set: function set(namespace) {\n      if (namespace === true || namespace === \"*\" || namespace === \"&\") {\n        this._namespace = namespace;\n        if (this.raws) {\n          delete this.raws.namespace;\n        }\n        return;\n      }\n      var escaped = (0, _cssesc[\"default\"])(namespace, {\n        isIdentifier: true\n      });\n      this._namespace = namespace;\n      if (escaped !== namespace) {\n        (0, _util.ensureObject)(this, \"raws\");\n        this.raws.namespace = escaped;\n      } else if (this.raws) {\n        delete this.raws.namespace;\n      }\n    }\n  }, {\n    key: \"ns\",\n    get: function get() {\n      return this._namespace;\n    },\n    set: function set(namespace) {\n      this.namespace = namespace;\n    }\n  }, {\n    key: \"namespaceString\",\n    get: function get() {\n      if (this.namespace) {\n        var ns = this.stringifyProperty(\"namespace\");\n        if (ns === true) {\n          return '';\n        } else {\n          return ns;\n        }\n      } else {\n        return '';\n      }\n    }\n  }]);\n  return Namespace;\n}(_node[\"default\"]);\nexports[\"default\"] = Namespace;\n;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,UAAU;AACd,IAAI;AACJ,IAAI,QAAQ;AACZ,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,SAAS,kBAAkB,MAAM,EAAE,KAAK;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QAAE,IAAI,aAAa,KAAK,CAAC,EAAE;QAAE,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI;QAAO,WAAW,YAAY,GAAG;QAAM,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG;QAAM,OAAO,cAAc,CAAC,QAAQ,WAAW,GAAG,EAAE;IAAa;AAAE;AAC5T,SAAS,aAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IAAI,IAAI,YAAY,kBAAkB,YAAY,SAAS,EAAE;IAAa,IAAI,aAAa,kBAAkB,aAAa;IAAc,OAAO,cAAc,CAAC,aAAa,aAAa;QAAE,UAAU;IAAM;IAAI,OAAO;AAAa;AAC5R,SAAS,eAAe,QAAQ,EAAE,UAAU;IAAI,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,WAAW,SAAS;IAAG,SAAS,SAAS,CAAC,WAAW,GAAG;IAAU,gBAAgB,UAAU;AAAa;AAC5L,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAAI,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAS,gBAAgB,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;QAAG,OAAO;IAAG;IAAG,OAAO,gBAAgB,GAAG;AAAI;AACvM,IAAI,YAAY,WAAW,GAAE,SAAU,KAAK;IAC1C,eAAe,WAAW;IAC1B,SAAS;QACP,OAAO,MAAM,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IAC7C;IACA,IAAI,SAAS,UAAU,SAAS;IAChC,OAAO,aAAa,GAAG,SAAS,cAAc,KAAK;QACjD,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,eAAe,GAAG,MAAM;QACtC,OAAO;YACL,OAAO;QACT;IACF;IACA,OAAO,aAAa,GAAG,SAAS;QAC9B,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI;IACnE;IACA,aAAa,WAAW;QAAC;YACvB,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,UAAU;YACxB;YACA,KAAK,SAAS,IAAI,SAAS;gBACzB,IAAI,cAAc,QAAQ,cAAc,OAAO,cAAc,KAAK;oBAChE,IAAI,CAAC,UAAU,GAAG;oBAClB,IAAI,IAAI,CAAC,IAAI,EAAE;wBACb,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS;oBAC5B;oBACA;gBACF;gBACA,IAAI,UAAU,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE,WAAW;oBAC/C,cAAc;gBAChB;gBACA,IAAI,CAAC,UAAU,GAAG;gBAClB,IAAI,YAAY,WAAW;oBACzB,CAAC,GAAG,MAAM,YAAY,EAAE,IAAI,EAAE;oBAC9B,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG;gBACxB,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE;oBACpB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS;gBAC5B;YACF;QACF;QAAG;YACD,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,UAAU;YACxB;YACA,KAAK,SAAS,IAAI,SAAS;gBACzB,IAAI,CAAC,SAAS,GAAG;YACnB;QACF;QAAG;YACD,KAAK;YACL,KAAK,SAAS;gBACZ,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,IAAI,KAAK,IAAI,CAAC,iBAAiB,CAAC;oBAChC,IAAI,OAAO,MAAM;wBACf,OAAO;oBACT,OAAO;wBACL,OAAO;oBACT;gBACF,OAAO;oBACL,OAAO;gBACT;YACF;QACF;KAAE;IACF,OAAO;AACT,EAAE,KAAK,CAAC,UAAU;AAClB,OAAO,CAAC,UAAU,GAAG;;AAErB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/tag.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _namespace = _interopRequireDefault(require(\"./namespace\"));\nvar _types = require(\"./types\");\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nvar Tag = /*#__PURE__*/function (_Namespace) {\n  _inheritsLoose(Tag, _Namespace);\n  function Tag(opts) {\n    var _this;\n    _this = _Namespace.call(this, opts) || this;\n    _this.type = _types.TAG;\n    return _this;\n  }\n  return Tag;\n}(_namespace[\"default\"]);\nexports[\"default\"] = Tag;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,aAAa;AACjB,IAAI;AACJ,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,SAAS,eAAe,QAAQ,EAAE,UAAU;IAAI,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,WAAW,SAAS;IAAG,SAAS,SAAS,CAAC,WAAW,GAAG;IAAU,gBAAgB,UAAU;AAAa;AAC5L,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAAI,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAS,gBAAgB,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;QAAG,OAAO;IAAG;IAAG,OAAO,gBAAgB,GAAG;AAAI;AACvM,IAAI,MAAM,WAAW,GAAE,SAAU,UAAU;IACzC,eAAe,KAAK;IACpB,SAAS,IAAI,IAAI;QACf,IAAI;QACJ,QAAQ,WAAW,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI;QAC3C,MAAM,IAAI,GAAG,OAAO,GAAG;QACvB,OAAO;IACT;IACA,OAAO;AACT,EAAE,UAAU,CAAC,UAAU;AACvB,OAAO,CAAC,UAAU,GAAG;AACrB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/string.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _node = _interopRequireDefault(require(\"./node\"));\nvar _types = require(\"./types\");\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nvar String = /*#__PURE__*/function (_Node) {\n  _inheritsLoose(String, _Node);\n  function String(opts) {\n    var _this;\n    _this = _Node.call(this, opts) || this;\n    _this.type = _types.STRING;\n    return _this;\n  }\n  return String;\n}(_node[\"default\"]);\nexports[\"default\"] = String;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,QAAQ;AACZ,IAAI;AACJ,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,SAAS,eAAe,QAAQ,EAAE,UAAU;IAAI,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,WAAW,SAAS;IAAG,SAAS,SAAS,CAAC,WAAW,GAAG;IAAU,gBAAgB,UAAU;AAAa;AAC5L,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAAI,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAS,gBAAgB,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;QAAG,OAAO;IAAG;IAAG,OAAO,gBAAgB,GAAG;AAAI;AACvM,IAAI,SAAS,WAAW,GAAE,SAAU,KAAK;IACvC,eAAe,QAAQ;IACvB,SAAS,OAAO,IAAI;QAClB,IAAI;QACJ,QAAQ,MAAM,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI;QACtC,MAAM,IAAI,GAAG,OAAO,MAAM;QAC1B,OAAO;IACT;IACA,OAAO;AACT,EAAE,KAAK,CAAC,UAAU;AAClB,OAAO,CAAC,UAAU,GAAG;AACrB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/pseudo.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _container = _interopRequireDefault(require(\"./container\"));\nvar _types = require(\"./types\");\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nvar Pseudo = /*#__PURE__*/function (_Container) {\n  _inheritsLoose(Pseudo, _Container);\n  function Pseudo(opts) {\n    var _this;\n    _this = _Container.call(this, opts) || this;\n    _this.type = _types.PSEUDO;\n    return _this;\n  }\n  var _proto = Pseudo.prototype;\n  _proto.toString = function toString() {\n    var params = this.length ? '(' + this.map(String).join(',') + ')' : '';\n    return [this.rawSpaceBefore, this.stringifyProperty(\"value\"), params, this.rawSpaceAfter].join('');\n  };\n  return Pseudo;\n}(_container[\"default\"]);\nexports[\"default\"] = Pseudo;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,aAAa;AACjB,IAAI;AACJ,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,SAAS,eAAe,QAAQ,EAAE,UAAU;IAAI,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,WAAW,SAAS;IAAG,SAAS,SAAS,CAAC,WAAW,GAAG;IAAU,gBAAgB,UAAU;AAAa;AAC5L,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAAI,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAS,gBAAgB,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;QAAG,OAAO;IAAG;IAAG,OAAO,gBAAgB,GAAG;AAAI;AACvM,IAAI,SAAS,WAAW,GAAE,SAAU,UAAU;IAC5C,eAAe,QAAQ;IACvB,SAAS,OAAO,IAAI;QAClB,IAAI;QACJ,QAAQ,WAAW,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI;QAC3C,MAAM,IAAI,GAAG,OAAO,MAAM;QAC1B,OAAO;IACT;IACA,IAAI,SAAS,OAAO,SAAS;IAC7B,OAAO,QAAQ,GAAG,SAAS;QACzB,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,OAAO,MAAM;QACpE,OAAO;YAAC,IAAI,CAAC,cAAc;YAAE,IAAI,CAAC,iBAAiB,CAAC;YAAU;YAAQ,IAAI,CAAC,aAAa;SAAC,CAAC,IAAI,CAAC;IACjG;IACA,OAAO;AACT,EAAE,UAAU,CAAC,UAAU;AACvB,OAAO,CAAC,UAAU,GAAG;AACrB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1321, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/attribute.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nexports.unescapeValue = unescapeValue;\nvar _cssesc = _interopRequireDefault(require(\"cssesc\"));\nvar _unesc = _interopRequireDefault(require(\"../util/unesc\"));\nvar _namespace = _interopRequireDefault(require(\"./namespace\"));\nvar _types = require(\"./types\");\nvar _CSSESC_QUOTE_OPTIONS;\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nvar deprecate = require(\"util-deprecate\");\nvar WRAPPED_IN_QUOTES = /^('|\")([^]*)\\1$/;\nvar warnOfDeprecatedValueAssignment = deprecate(function () {}, \"Assigning an attribute a value containing characters that might need to be escaped is deprecated. \" + \"Call attribute.setValue() instead.\");\nvar warnOfDeprecatedQuotedAssignment = deprecate(function () {}, \"Assigning attr.quoted is deprecated and has no effect. Assign to attr.quoteMark instead.\");\nvar warnOfDeprecatedConstructor = deprecate(function () {}, \"Constructing an Attribute selector with a value without specifying quoteMark is deprecated. Note: The value should be unescaped now.\");\nfunction unescapeValue(value) {\n  var deprecatedUsage = false;\n  var quoteMark = null;\n  var unescaped = value;\n  var m = unescaped.match(WRAPPED_IN_QUOTES);\n  if (m) {\n    quoteMark = m[1];\n    unescaped = m[2];\n  }\n  unescaped = (0, _unesc[\"default\"])(unescaped);\n  if (unescaped !== value) {\n    deprecatedUsage = true;\n  }\n  return {\n    deprecatedUsage: deprecatedUsage,\n    unescaped: unescaped,\n    quoteMark: quoteMark\n  };\n}\nfunction handleDeprecatedContructorOpts(opts) {\n  if (opts.quoteMark !== undefined) {\n    return opts;\n  }\n  if (opts.value === undefined) {\n    return opts;\n  }\n  warnOfDeprecatedConstructor();\n  var _unescapeValue = unescapeValue(opts.value),\n    quoteMark = _unescapeValue.quoteMark,\n    unescaped = _unescapeValue.unescaped;\n  if (!opts.raws) {\n    opts.raws = {};\n  }\n  if (opts.raws.value === undefined) {\n    opts.raws.value = opts.value;\n  }\n  opts.value = unescaped;\n  opts.quoteMark = quoteMark;\n  return opts;\n}\nvar Attribute = /*#__PURE__*/function (_Namespace) {\n  _inheritsLoose(Attribute, _Namespace);\n  function Attribute(opts) {\n    var _this;\n    if (opts === void 0) {\n      opts = {};\n    }\n    _this = _Namespace.call(this, handleDeprecatedContructorOpts(opts)) || this;\n    _this.type = _types.ATTRIBUTE;\n    _this.raws = _this.raws || {};\n    Object.defineProperty(_this.raws, 'unquoted', {\n      get: deprecate(function () {\n        return _this.value;\n      }, \"attr.raws.unquoted is deprecated. Call attr.value instead.\"),\n      set: deprecate(function () {\n        return _this.value;\n      }, \"Setting attr.raws.unquoted is deprecated and has no effect. attr.value is unescaped by default now.\")\n    });\n    _this._constructed = true;\n    return _this;\n  }\n\n  /**\n   * Returns the Attribute's value quoted such that it would be legal to use\n   * in the value of a css file. The original value's quotation setting\n   * used for stringification is left unchanged. See `setValue(value, options)`\n   * if you want to control the quote settings of a new value for the attribute.\n   *\n   * You can also change the quotation used for the current value by setting quoteMark.\n   *\n   * Options:\n   *   * quoteMark {'\"' | \"'\" | null} - Use this value to quote the value. If this\n   *     option is not set, the original value for quoteMark will be used. If\n   *     indeterminate, a double quote is used. The legal values are:\n   *     * `null` - the value will be unquoted and characters will be escaped as necessary.\n   *     * `'` - the value will be quoted with a single quote and single quotes are escaped.\n   *     * `\"` - the value will be quoted with a double quote and double quotes are escaped.\n   *   * preferCurrentQuoteMark {boolean} - if true, prefer the source quote mark\n   *     over the quoteMark option value.\n   *   * smart {boolean} - if true, will select a quote mark based on the value\n   *     and the other options specified here. See the `smartQuoteMark()`\n   *     method.\n   **/\n  var _proto = Attribute.prototype;\n  _proto.getQuotedValue = function getQuotedValue(options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var quoteMark = this._determineQuoteMark(options);\n    var cssescopts = CSSESC_QUOTE_OPTIONS[quoteMark];\n    var escaped = (0, _cssesc[\"default\"])(this._value, cssescopts);\n    return escaped;\n  };\n  _proto._determineQuoteMark = function _determineQuoteMark(options) {\n    return options.smart ? this.smartQuoteMark(options) : this.preferredQuoteMark(options);\n  }\n\n  /**\n   * Set the unescaped value with the specified quotation options. The value\n   * provided must not include any wrapping quote marks -- those quotes will\n   * be interpreted as part of the value and escaped accordingly.\n   */;\n  _proto.setValue = function setValue(value, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    this._value = value;\n    this._quoteMark = this._determineQuoteMark(options);\n    this._syncRawValue();\n  }\n\n  /**\n   * Intelligently select a quoteMark value based on the value's contents. If\n   * the value is a legal CSS ident, it will not be quoted. Otherwise a quote\n   * mark will be picked that minimizes the number of escapes.\n   *\n   * If there's no clear winner, the quote mark from these options is used,\n   * then the source quote mark (this is inverted if `preferCurrentQuoteMark` is\n   * true). If the quoteMark is unspecified, a double quote is used.\n   *\n   * @param options This takes the quoteMark and preferCurrentQuoteMark options\n   * from the quoteValue method.\n   */;\n  _proto.smartQuoteMark = function smartQuoteMark(options) {\n    var v = this.value;\n    var numSingleQuotes = v.replace(/[^']/g, '').length;\n    var numDoubleQuotes = v.replace(/[^\"]/g, '').length;\n    if (numSingleQuotes + numDoubleQuotes === 0) {\n      var escaped = (0, _cssesc[\"default\"])(v, {\n        isIdentifier: true\n      });\n      if (escaped === v) {\n        return Attribute.NO_QUOTE;\n      } else {\n        var pref = this.preferredQuoteMark(options);\n        if (pref === Attribute.NO_QUOTE) {\n          // pick a quote mark that isn't none and see if it's smaller\n          var quote = this.quoteMark || options.quoteMark || Attribute.DOUBLE_QUOTE;\n          var opts = CSSESC_QUOTE_OPTIONS[quote];\n          var quoteValue = (0, _cssesc[\"default\"])(v, opts);\n          if (quoteValue.length < escaped.length) {\n            return quote;\n          }\n        }\n        return pref;\n      }\n    } else if (numDoubleQuotes === numSingleQuotes) {\n      return this.preferredQuoteMark(options);\n    } else if (numDoubleQuotes < numSingleQuotes) {\n      return Attribute.DOUBLE_QUOTE;\n    } else {\n      return Attribute.SINGLE_QUOTE;\n    }\n  }\n\n  /**\n   * Selects the preferred quote mark based on the options and the current quote mark value.\n   * If you want the quote mark to depend on the attribute value, call `smartQuoteMark(opts)`\n   * instead.\n   */;\n  _proto.preferredQuoteMark = function preferredQuoteMark(options) {\n    var quoteMark = options.preferCurrentQuoteMark ? this.quoteMark : options.quoteMark;\n    if (quoteMark === undefined) {\n      quoteMark = options.preferCurrentQuoteMark ? options.quoteMark : this.quoteMark;\n    }\n    if (quoteMark === undefined) {\n      quoteMark = Attribute.DOUBLE_QUOTE;\n    }\n    return quoteMark;\n  };\n  _proto._syncRawValue = function _syncRawValue() {\n    var rawValue = (0, _cssesc[\"default\"])(this._value, CSSESC_QUOTE_OPTIONS[this.quoteMark]);\n    if (rawValue === this._value) {\n      if (this.raws) {\n        delete this.raws.value;\n      }\n    } else {\n      this.raws.value = rawValue;\n    }\n  };\n  _proto._handleEscapes = function _handleEscapes(prop, value) {\n    if (this._constructed) {\n      var escaped = (0, _cssesc[\"default\"])(value, {\n        isIdentifier: true\n      });\n      if (escaped !== value) {\n        this.raws[prop] = escaped;\n      } else {\n        delete this.raws[prop];\n      }\n    }\n  };\n  _proto._spacesFor = function _spacesFor(name) {\n    var attrSpaces = {\n      before: '',\n      after: ''\n    };\n    var spaces = this.spaces[name] || {};\n    var rawSpaces = this.raws.spaces && this.raws.spaces[name] || {};\n    return Object.assign(attrSpaces, spaces, rawSpaces);\n  };\n  _proto._stringFor = function _stringFor(name, spaceName, concat) {\n    if (spaceName === void 0) {\n      spaceName = name;\n    }\n    if (concat === void 0) {\n      concat = defaultAttrConcat;\n    }\n    var attrSpaces = this._spacesFor(spaceName);\n    return concat(this.stringifyProperty(name), attrSpaces);\n  }\n\n  /**\n   * returns the offset of the attribute part specified relative to the\n   * start of the node of the output string.\n   *\n   * * \"ns\" - alias for \"namespace\"\n   * * \"namespace\" - the namespace if it exists.\n   * * \"attribute\" - the attribute name\n   * * \"attributeNS\" - the start of the attribute or its namespace\n   * * \"operator\" - the match operator of the attribute\n   * * \"value\" - The value (string or identifier)\n   * * \"insensitive\" - the case insensitivity flag;\n   * @param part One of the possible values inside an attribute.\n   * @returns -1 if the name is invalid or the value doesn't exist in this attribute.\n   */;\n  _proto.offsetOf = function offsetOf(name) {\n    var count = 1;\n    var attributeSpaces = this._spacesFor(\"attribute\");\n    count += attributeSpaces.before.length;\n    if (name === \"namespace\" || name === \"ns\") {\n      return this.namespace ? count : -1;\n    }\n    if (name === \"attributeNS\") {\n      return count;\n    }\n    count += this.namespaceString.length;\n    if (this.namespace) {\n      count += 1;\n    }\n    if (name === \"attribute\") {\n      return count;\n    }\n    count += this.stringifyProperty(\"attribute\").length;\n    count += attributeSpaces.after.length;\n    var operatorSpaces = this._spacesFor(\"operator\");\n    count += operatorSpaces.before.length;\n    var operator = this.stringifyProperty(\"operator\");\n    if (name === \"operator\") {\n      return operator ? count : -1;\n    }\n    count += operator.length;\n    count += operatorSpaces.after.length;\n    var valueSpaces = this._spacesFor(\"value\");\n    count += valueSpaces.before.length;\n    var value = this.stringifyProperty(\"value\");\n    if (name === \"value\") {\n      return value ? count : -1;\n    }\n    count += value.length;\n    count += valueSpaces.after.length;\n    var insensitiveSpaces = this._spacesFor(\"insensitive\");\n    count += insensitiveSpaces.before.length;\n    if (name === \"insensitive\") {\n      return this.insensitive ? count : -1;\n    }\n    return -1;\n  };\n  _proto.toString = function toString() {\n    var _this2 = this;\n    var selector = [this.rawSpaceBefore, '['];\n    selector.push(this._stringFor('qualifiedAttribute', 'attribute'));\n    if (this.operator && (this.value || this.value === '')) {\n      selector.push(this._stringFor('operator'));\n      selector.push(this._stringFor('value'));\n      selector.push(this._stringFor('insensitiveFlag', 'insensitive', function (attrValue, attrSpaces) {\n        if (attrValue.length > 0 && !_this2.quoted && attrSpaces.before.length === 0 && !(_this2.spaces.value && _this2.spaces.value.after)) {\n          attrSpaces.before = \" \";\n        }\n        return defaultAttrConcat(attrValue, attrSpaces);\n      }));\n    }\n    selector.push(']');\n    selector.push(this.rawSpaceAfter);\n    return selector.join('');\n  };\n  _createClass(Attribute, [{\n    key: \"quoted\",\n    get: function get() {\n      var qm = this.quoteMark;\n      return qm === \"'\" || qm === '\"';\n    },\n    set: function set(value) {\n      warnOfDeprecatedQuotedAssignment();\n    }\n\n    /**\n     * returns a single (`'`) or double (`\"`) quote character if the value is quoted.\n     * returns `null` if the value is not quoted.\n     * returns `undefined` if the quotation state is unknown (this can happen when\n     * the attribute is constructed without specifying a quote mark.)\n     */\n  }, {\n    key: \"quoteMark\",\n    get: function get() {\n      return this._quoteMark;\n    }\n\n    /**\n     * Set the quote mark to be used by this attribute's value.\n     * If the quote mark changes, the raw (escaped) value at `attr.raws.value` of the attribute\n     * value is updated accordingly.\n     *\n     * @param {\"'\" | '\"' | null} quoteMark The quote mark or `null` if the value should be unquoted.\n     */,\n    set: function set(quoteMark) {\n      if (!this._constructed) {\n        this._quoteMark = quoteMark;\n        return;\n      }\n      if (this._quoteMark !== quoteMark) {\n        this._quoteMark = quoteMark;\n        this._syncRawValue();\n      }\n    }\n  }, {\n    key: \"qualifiedAttribute\",\n    get: function get() {\n      return this.qualifiedName(this.raws.attribute || this.attribute);\n    }\n  }, {\n    key: \"insensitiveFlag\",\n    get: function get() {\n      return this.insensitive ? 'i' : '';\n    }\n  }, {\n    key: \"value\",\n    get: function get() {\n      return this._value;\n    },\n    set:\n    /**\n     * Before 3.0, the value had to be set to an escaped value including any wrapped\n     * quote marks. In 3.0, the semantics of `Attribute.value` changed so that the value\n     * is unescaped during parsing and any quote marks are removed.\n     *\n     * Because the ambiguity of this semantic change, if you set `attr.value = newValue`,\n     * a deprecation warning is raised when the new value contains any characters that would\n     * require escaping (including if it contains wrapped quotes).\n     *\n     * Instead, you should call `attr.setValue(newValue, opts)` and pass options that describe\n     * how the new value is quoted.\n     */\n    function set(v) {\n      if (this._constructed) {\n        var _unescapeValue2 = unescapeValue(v),\n          deprecatedUsage = _unescapeValue2.deprecatedUsage,\n          unescaped = _unescapeValue2.unescaped,\n          quoteMark = _unescapeValue2.quoteMark;\n        if (deprecatedUsage) {\n          warnOfDeprecatedValueAssignment();\n        }\n        if (unescaped === this._value && quoteMark === this._quoteMark) {\n          return;\n        }\n        this._value = unescaped;\n        this._quoteMark = quoteMark;\n        this._syncRawValue();\n      } else {\n        this._value = v;\n      }\n    }\n  }, {\n    key: \"insensitive\",\n    get: function get() {\n      return this._insensitive;\n    }\n\n    /**\n     * Set the case insensitive flag.\n     * If the case insensitive flag changes, the raw (escaped) value at `attr.raws.insensitiveFlag`\n     * of the attribute is updated accordingly.\n     *\n     * @param {true | false} insensitive true if the attribute should match case-insensitively.\n     */,\n    set: function set(insensitive) {\n      if (!insensitive) {\n        this._insensitive = false;\n\n        // \"i\" and \"I\" can be used in \"this.raws.insensitiveFlag\" to store the original notation.\n        // When setting `attr.insensitive = false` both should be erased to ensure correct serialization.\n        if (this.raws && (this.raws.insensitiveFlag === 'I' || this.raws.insensitiveFlag === 'i')) {\n          this.raws.insensitiveFlag = undefined;\n        }\n      }\n      this._insensitive = insensitive;\n    }\n  }, {\n    key: \"attribute\",\n    get: function get() {\n      return this._attribute;\n    },\n    set: function set(name) {\n      this._handleEscapes(\"attribute\", name);\n      this._attribute = name;\n    }\n  }]);\n  return Attribute;\n}(_namespace[\"default\"]);\nexports[\"default\"] = Attribute;\nAttribute.NO_QUOTE = null;\nAttribute.SINGLE_QUOTE = \"'\";\nAttribute.DOUBLE_QUOTE = '\"';\nvar CSSESC_QUOTE_OPTIONS = (_CSSESC_QUOTE_OPTIONS = {\n  \"'\": {\n    quotes: 'single',\n    wrap: true\n  },\n  '\"': {\n    quotes: 'double',\n    wrap: true\n  }\n}, _CSSESC_QUOTE_OPTIONS[null] = {\n  isIdentifier: true\n}, _CSSESC_QUOTE_OPTIONS);\nfunction defaultAttrConcat(attrValue, attrSpaces) {\n  return \"\" + attrSpaces.before + attrValue + attrSpaces.after;\n}"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,QAAQ,aAAa,GAAG;AACxB,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,aAAa;AACjB,IAAI;AACJ,IAAI;AACJ,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,SAAS,kBAAkB,MAAM,EAAE,KAAK;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QAAE,IAAI,aAAa,KAAK,CAAC,EAAE;QAAE,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI;QAAO,WAAW,YAAY,GAAG;QAAM,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG;QAAM,OAAO,cAAc,CAAC,QAAQ,WAAW,GAAG,EAAE;IAAa;AAAE;AAC5T,SAAS,aAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IAAI,IAAI,YAAY,kBAAkB,YAAY,SAAS,EAAE;IAAa,IAAI,aAAa,kBAAkB,aAAa;IAAc,OAAO,cAAc,CAAC,aAAa,aAAa;QAAE,UAAU;IAAM;IAAI,OAAO;AAAa;AAC5R,SAAS,eAAe,QAAQ,EAAE,UAAU;IAAI,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,WAAW,SAAS;IAAG,SAAS,SAAS,CAAC,WAAW,GAAG;IAAU,gBAAgB,UAAU;AAAa;AAC5L,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAAI,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAS,gBAAgB,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;QAAG,OAAO;IAAG;IAAG,OAAO,gBAAgB,GAAG;AAAI;AACvM,IAAI;AACJ,IAAI,oBAAoB;AACxB,IAAI,kCAAkC,UAAU,YAAa,GAAG,uGAAuG;AACvK,IAAI,mCAAmC,UAAU,YAAa,GAAG;AACjE,IAAI,8BAA8B,UAAU,YAAa,GAAG;AAC5D,SAAS,cAAc,KAAK;IAC1B,IAAI,kBAAkB;IACtB,IAAI,YAAY;IAChB,IAAI,YAAY;IAChB,IAAI,IAAI,UAAU,KAAK,CAAC;IACxB,IAAI,GAAG;QACL,YAAY,CAAC,CAAC,EAAE;QAChB,YAAY,CAAC,CAAC,EAAE;IAClB;IACA,YAAY,CAAC,GAAG,MAAM,CAAC,UAAU,EAAE;IACnC,IAAI,cAAc,OAAO;QACvB,kBAAkB;IACpB;IACA,OAAO;QACL,iBAAiB;QACjB,WAAW;QACX,WAAW;IACb;AACF;AACA,SAAS,+BAA+B,IAAI;IAC1C,IAAI,KAAK,SAAS,KAAK,WAAW;QAChC,OAAO;IACT;IACA,IAAI,KAAK,KAAK,KAAK,WAAW;QAC5B,OAAO;IACT;IACA;IACA,IAAI,iBAAiB,cAAc,KAAK,KAAK,GAC3C,YAAY,eAAe,SAAS,EACpC,YAAY,eAAe,SAAS;IACtC,IAAI,CAAC,KAAK,IAAI,EAAE;QACd,KAAK,IAAI,GAAG,CAAC;IACf;IACA,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,WAAW;QACjC,KAAK,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK;IAC9B;IACA,KAAK,KAAK,GAAG;IACb,KAAK,SAAS,GAAG;IACjB,OAAO;AACT;AACA,IAAI,YAAY,WAAW,GAAE,SAAU,UAAU;IAC/C,eAAe,WAAW;IAC1B,SAAS,UAAU,IAAI;QACrB,IAAI;QACJ,IAAI,SAAS,KAAK,GAAG;YACnB,OAAO,CAAC;QACV;QACA,QAAQ,WAAW,IAAI,CAAC,IAAI,EAAE,+BAA+B,UAAU,IAAI;QAC3E,MAAM,IAAI,GAAG,OAAO,SAAS;QAC7B,MAAM,IAAI,GAAG,MAAM,IAAI,IAAI,CAAC;QAC5B,OAAO,cAAc,CAAC,MAAM,IAAI,EAAE,YAAY;YAC5C,KAAK,UAAU;gBACb,OAAO,MAAM,KAAK;YACpB,GAAG;YACH,KAAK,UAAU;gBACb,OAAO,MAAM,KAAK;YACpB,GAAG;QACL;QACA,MAAM,YAAY,GAAG;QACrB,OAAO;IACT;IAEA;;;;;;;;;;;;;;;;;;;;IAoBE,GACF,IAAI,SAAS,UAAU,SAAS;IAChC,OAAO,cAAc,GAAG,SAAS,eAAe,OAAO;QACrD,IAAI,YAAY,KAAK,GAAG;YACtB,UAAU,CAAC;QACb;QACA,IAAI,YAAY,IAAI,CAAC,mBAAmB,CAAC;QACzC,IAAI,aAAa,oBAAoB,CAAC,UAAU;QAChD,IAAI,UAAU,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE;QACnD,OAAO;IACT;IACA,OAAO,mBAAmB,GAAG,SAAS,oBAAoB,OAAO;QAC/D,OAAO,QAAQ,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,IAAI,CAAC,kBAAkB,CAAC;IAChF;IAOA,OAAO,QAAQ,GAAG,SAAS,SAAS,KAAK,EAAE,OAAO;QAChD,IAAI,YAAY,KAAK,GAAG;YACtB,UAAU,CAAC;QACb;QACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAC3C,IAAI,CAAC,aAAa;IACpB;IAcA,OAAO,cAAc,GAAG,SAAS,eAAe,OAAO;QACrD,IAAI,IAAI,IAAI,CAAC,KAAK;QAClB,IAAI,kBAAkB,EAAE,OAAO,CAAC,SAAS,IAAI,MAAM;QACnD,IAAI,kBAAkB,EAAE,OAAO,CAAC,SAAS,IAAI,MAAM;QACnD,IAAI,kBAAkB,oBAAoB,GAAG;YAC3C,IAAI,UAAU,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE,GAAG;gBACvC,cAAc;YAChB;YACA,IAAI,YAAY,GAAG;gBACjB,OAAO,UAAU,QAAQ;YAC3B,OAAO;gBACL,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC;gBACnC,IAAI,SAAS,UAAU,QAAQ,EAAE;oBAC/B,4DAA4D;oBAC5D,IAAI,QAAQ,IAAI,CAAC,SAAS,IAAI,QAAQ,SAAS,IAAI,UAAU,YAAY;oBACzE,IAAI,OAAO,oBAAoB,CAAC,MAAM;oBACtC,IAAI,aAAa,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE,GAAG;oBAC5C,IAAI,WAAW,MAAM,GAAG,QAAQ,MAAM,EAAE;wBACtC,OAAO;oBACT;gBACF;gBACA,OAAO;YACT;QACF,OAAO,IAAI,oBAAoB,iBAAiB;YAC9C,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACjC,OAAO,IAAI,kBAAkB,iBAAiB;YAC5C,OAAO,UAAU,YAAY;QAC/B,OAAO;YACL,OAAO,UAAU,YAAY;QAC/B;IACF;IAOA,OAAO,kBAAkB,GAAG,SAAS,mBAAmB,OAAO;QAC7D,IAAI,YAAY,QAAQ,sBAAsB,GAAG,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS;QACnF,IAAI,cAAc,WAAW;YAC3B,YAAY,QAAQ,sBAAsB,GAAG,QAAQ,SAAS,GAAG,IAAI,CAAC,SAAS;QACjF;QACA,IAAI,cAAc,WAAW;YAC3B,YAAY,UAAU,YAAY;QACpC;QACA,OAAO;IACT;IACA,OAAO,aAAa,GAAG,SAAS;QAC9B,IAAI,WAAW,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC;QACxF,IAAI,aAAa,IAAI,CAAC,MAAM,EAAE;YAC5B,IAAI,IAAI,CAAC,IAAI,EAAE;gBACb,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;YACxB;QACF,OAAO;YACL,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG;QACpB;IACF;IACA,OAAO,cAAc,GAAG,SAAS,eAAe,IAAI,EAAE,KAAK;QACzD,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,UAAU,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE,OAAO;gBAC3C,cAAc;YAChB;YACA,IAAI,YAAY,OAAO;gBACrB,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG;YACpB,OAAO;gBACL,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;YACxB;QACF;IACF;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,IAAI;QAC1C,IAAI,aAAa;YACf,QAAQ;YACR,OAAO;QACT;QACA,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC;QACnC,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC;QAC/D,OAAO,OAAO,MAAM,CAAC,YAAY,QAAQ;IAC3C;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,IAAI,EAAE,SAAS,EAAE,MAAM;QAC7D,IAAI,cAAc,KAAK,GAAG;YACxB,YAAY;QACd;QACA,IAAI,WAAW,KAAK,GAAG;YACrB,SAAS;QACX;QACA,IAAI,aAAa,IAAI,CAAC,UAAU,CAAC;QACjC,OAAO,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO;IAC9C;IAgBA,OAAO,QAAQ,GAAG,SAAS,SAAS,IAAI;QACtC,IAAI,QAAQ;QACZ,IAAI,kBAAkB,IAAI,CAAC,UAAU,CAAC;QACtC,SAAS,gBAAgB,MAAM,CAAC,MAAM;QACtC,IAAI,SAAS,eAAe,SAAS,MAAM;YACzC,OAAO,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QACnC;QACA,IAAI,SAAS,eAAe;YAC1B,OAAO;QACT;QACA,SAAS,IAAI,CAAC,eAAe,CAAC,MAAM;QACpC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,SAAS;QACX;QACA,IAAI,SAAS,aAAa;YACxB,OAAO;QACT;QACA,SAAS,IAAI,CAAC,iBAAiB,CAAC,aAAa,MAAM;QACnD,SAAS,gBAAgB,KAAK,CAAC,MAAM;QACrC,IAAI,iBAAiB,IAAI,CAAC,UAAU,CAAC;QACrC,SAAS,eAAe,MAAM,CAAC,MAAM;QACrC,IAAI,WAAW,IAAI,CAAC,iBAAiB,CAAC;QACtC,IAAI,SAAS,YAAY;YACvB,OAAO,WAAW,QAAQ,CAAC;QAC7B;QACA,SAAS,SAAS,MAAM;QACxB,SAAS,eAAe,KAAK,CAAC,MAAM;QACpC,IAAI,cAAc,IAAI,CAAC,UAAU,CAAC;QAClC,SAAS,YAAY,MAAM,CAAC,MAAM;QAClC,IAAI,QAAQ,IAAI,CAAC,iBAAiB,CAAC;QACnC,IAAI,SAAS,SAAS;YACpB,OAAO,QAAQ,QAAQ,CAAC;QAC1B;QACA,SAAS,MAAM,MAAM;QACrB,SAAS,YAAY,KAAK,CAAC,MAAM;QACjC,IAAI,oBAAoB,IAAI,CAAC,UAAU,CAAC;QACxC,SAAS,kBAAkB,MAAM,CAAC,MAAM;QACxC,IAAI,SAAS,eAAe;YAC1B,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;QACrC;QACA,OAAO,CAAC;IACV;IACA,OAAO,QAAQ,GAAG,SAAS;QACzB,IAAI,SAAS,IAAI;QACjB,IAAI,WAAW;YAAC,IAAI,CAAC,cAAc;YAAE;SAAI;QACzC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,sBAAsB;QACpD,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,GAAG;YACtD,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YAC9B,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;YAC9B,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,eAAe,SAAU,SAAS,EAAE,UAAU;gBAC7F,IAAI,UAAU,MAAM,GAAG,KAAK,CAAC,OAAO,MAAM,IAAI,WAAW,MAAM,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,OAAO,MAAM,CAAC,KAAK,IAAI,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG;oBACnI,WAAW,MAAM,GAAG;gBACtB;gBACA,OAAO,kBAAkB,WAAW;YACtC;QACF;QACA,SAAS,IAAI,CAAC;QACd,SAAS,IAAI,CAAC,IAAI,CAAC,aAAa;QAChC,OAAO,SAAS,IAAI,CAAC;IACvB;IACA,aAAa,WAAW;QAAC;YACvB,KAAK;YACL,KAAK,SAAS;gBACZ,IAAI,KAAK,IAAI,CAAC,SAAS;gBACvB,OAAO,OAAO,OAAO,OAAO;YAC9B;YACA,KAAK,SAAS,IAAI,KAAK;gBACrB;YACF;QAQF;QAAG;YACD,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,UAAU;YACxB;YASA,KAAK,SAAS,IAAI,SAAS;gBACzB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;oBACtB,IAAI,CAAC,UAAU,GAAG;oBAClB;gBACF;gBACA,IAAI,IAAI,CAAC,UAAU,KAAK,WAAW;oBACjC,IAAI,CAAC,UAAU,GAAG;oBAClB,IAAI,CAAC,aAAa;gBACpB;YACF;QACF;QAAG;YACD,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS;YACjE;QACF;QAAG;YACD,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,WAAW,GAAG,MAAM;YAClC;QACF;QAAG;YACD,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,MAAM;YACpB;YACA,KACA;;;;;;;;;;;KAWC,GACD,SAAS,IAAI,CAAC;gBACZ,IAAI,IAAI,CAAC,YAAY,EAAE;oBACrB,IAAI,kBAAkB,cAAc,IAClC,kBAAkB,gBAAgB,eAAe,EACjD,YAAY,gBAAgB,SAAS,EACrC,YAAY,gBAAgB,SAAS;oBACvC,IAAI,iBAAiB;wBACnB;oBACF;oBACA,IAAI,cAAc,IAAI,CAAC,MAAM,IAAI,cAAc,IAAI,CAAC,UAAU,EAAE;wBAC9D;oBACF;oBACA,IAAI,CAAC,MAAM,GAAG;oBACd,IAAI,CAAC,UAAU,GAAG;oBAClB,IAAI,CAAC,aAAa;gBACpB,OAAO;oBACL,IAAI,CAAC,MAAM,GAAG;gBAChB;YACF;QACF;QAAG;YACD,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,YAAY;YAC1B;YASA,KAAK,SAAS,IAAI,WAAW;gBAC3B,IAAI,CAAC,aAAa;oBAChB,IAAI,CAAC,YAAY,GAAG;oBAEpB,yFAAyF;oBACzF,iGAAiG;oBACjG,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe,KAAK,GAAG,GAAG;wBACzF,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG;oBAC9B;gBACF;gBACA,IAAI,CAAC,YAAY,GAAG;YACtB;QACF;QAAG;YACD,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,UAAU;YACxB;YACA,KAAK,SAAS,IAAI,IAAI;gBACpB,IAAI,CAAC,cAAc,CAAC,aAAa;gBACjC,IAAI,CAAC,UAAU,GAAG;YACpB;QACF;KAAE;IACF,OAAO;AACT,EAAE,UAAU,CAAC,UAAU;AACvB,OAAO,CAAC,UAAU,GAAG;AACrB,UAAU,QAAQ,GAAG;AACrB,UAAU,YAAY,GAAG;AACzB,UAAU,YAAY,GAAG;AACzB,IAAI,uBAAuB,CAAC,wBAAwB;IAClD,KAAK;QACH,QAAQ;QACR,MAAM;IACR;IACA,KAAK;QACH,QAAQ;QACR,MAAM;IACR;AACF,GAAG,qBAAqB,CAAC,KAAK,GAAG;IAC/B,cAAc;AAChB,GAAG,qBAAqB;AACxB,SAAS,kBAAkB,SAAS,EAAE,UAAU;IAC9C,OAAO,KAAK,WAAW,MAAM,GAAG,YAAY,WAAW,KAAK;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1740, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/universal.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _namespace = _interopRequireDefault(require(\"./namespace\"));\nvar _types = require(\"./types\");\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nvar Universal = /*#__PURE__*/function (_Namespace) {\n  _inheritsLoose(Universal, _Namespace);\n  function Universal(opts) {\n    var _this;\n    _this = _Namespace.call(this, opts) || this;\n    _this.type = _types.UNIVERSAL;\n    _this.value = '*';\n    return _this;\n  }\n  return Universal;\n}(_namespace[\"default\"]);\nexports[\"default\"] = Universal;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,aAAa;AACjB,IAAI;AACJ,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,SAAS,eAAe,QAAQ,EAAE,UAAU;IAAI,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,WAAW,SAAS;IAAG,SAAS,SAAS,CAAC,WAAW,GAAG;IAAU,gBAAgB,UAAU;AAAa;AAC5L,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAAI,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAS,gBAAgB,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;QAAG,OAAO;IAAG;IAAG,OAAO,gBAAgB,GAAG;AAAI;AACvM,IAAI,YAAY,WAAW,GAAE,SAAU,UAAU;IAC/C,eAAe,WAAW;IAC1B,SAAS,UAAU,IAAI;QACrB,IAAI;QACJ,QAAQ,WAAW,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI;QAC3C,MAAM,IAAI,GAAG,OAAO,SAAS;QAC7B,MAAM,KAAK,GAAG;QACd,OAAO;IACT;IACA,OAAO;AACT,EAAE,UAAU,CAAC,UAAU;AACvB,OAAO,CAAC,UAAU,GAAG;AACrB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1780, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/combinator.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _node = _interopRequireDefault(require(\"./node\"));\nvar _types = require(\"./types\");\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nvar Combinator = /*#__PURE__*/function (_Node) {\n  _inheritsLoose(Combinator, _Node);\n  function Combinator(opts) {\n    var _this;\n    _this = _Node.call(this, opts) || this;\n    _this.type = _types.COMBINATOR;\n    return _this;\n  }\n  return Combinator;\n}(_node[\"default\"]);\nexports[\"default\"] = Combinator;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,QAAQ;AACZ,IAAI;AACJ,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,SAAS,eAAe,QAAQ,EAAE,UAAU;IAAI,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,WAAW,SAAS;IAAG,SAAS,SAAS,CAAC,WAAW,GAAG;IAAU,gBAAgB,UAAU;AAAa;AAC5L,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAAI,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAS,gBAAgB,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;QAAG,OAAO;IAAG;IAAG,OAAO,gBAAgB,GAAG;AAAI;AACvM,IAAI,aAAa,WAAW,GAAE,SAAU,KAAK;IAC3C,eAAe,YAAY;IAC3B,SAAS,WAAW,IAAI;QACtB,IAAI;QACJ,QAAQ,MAAM,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI;QACtC,MAAM,IAAI,GAAG,OAAO,UAAU;QAC9B,OAAO;IACT;IACA,OAAO;AACT,EAAE,KAAK,CAAC,UAAU;AAClB,OAAO,CAAC,UAAU,GAAG;AACrB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1819, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/nesting.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _node = _interopRequireDefault(require(\"./node\"));\nvar _types = require(\"./types\");\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nvar Nesting = /*#__PURE__*/function (_Node) {\n  _inheritsLoose(Nesting, _Node);\n  function Nesting(opts) {\n    var _this;\n    _this = _Node.call(this, opts) || this;\n    _this.type = _types.NESTING;\n    _this.value = '&';\n    return _this;\n  }\n  return Nesting;\n}(_node[\"default\"]);\nexports[\"default\"] = Nesting;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,QAAQ;AACZ,IAAI;AACJ,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,SAAS,eAAe,QAAQ,EAAE,UAAU;IAAI,SAAS,SAAS,GAAG,OAAO,MAAM,CAAC,WAAW,SAAS;IAAG,SAAS,SAAS,CAAC,WAAW,GAAG;IAAU,gBAAgB,UAAU;AAAa;AAC5L,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAAI,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAS,gBAAgB,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;QAAG,OAAO;IAAG;IAAG,OAAO,gBAAgB,GAAG;AAAI;AACvM,IAAI,UAAU,WAAW,GAAE,SAAU,KAAK;IACxC,eAAe,SAAS;IACxB,SAAS,QAAQ,IAAI;QACnB,IAAI;QACJ,QAAQ,MAAM,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI;QACtC,MAAM,IAAI,GAAG,OAAO,OAAO;QAC3B,MAAM,KAAK,GAAG;QACd,OAAO;IACT;IACA,OAAO;AACT,EAAE,KAAK,CAAC,UAAU;AAClB,OAAO,CAAC,UAAU,GAAG;AACrB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1859, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/sortAscending.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = sortAscending;\nfunction sortAscending(list) {\n  return list.sort(function (a, b) {\n    return a - b;\n  });\n}\n;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG;AACrB,SAAS,cAAc,IAAI;IACzB,OAAO,KAAK,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;QAC7B,OAAO,IAAI;IACb;AACF;;AAEA,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1874, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/tokenTypes.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.word = exports.tilde = exports.tab = exports.str = exports.space = exports.slash = exports.singleQuote = exports.semicolon = exports.plus = exports.pipe = exports.openSquare = exports.openParenthesis = exports.newline = exports.greaterThan = exports.feed = exports.equals = exports.doubleQuote = exports.dollar = exports.cr = exports.comment = exports.comma = exports.combinator = exports.colon = exports.closeSquare = exports.closeParenthesis = exports.caret = exports.bang = exports.backslash = exports.at = exports.asterisk = exports.ampersand = void 0;\nvar ampersand = 38; // `&`.charCodeAt(0);\nexports.ampersand = ampersand;\nvar asterisk = 42; // `*`.charCodeAt(0);\nexports.asterisk = asterisk;\nvar at = 64; // `@`.charCodeAt(0);\nexports.at = at;\nvar comma = 44; // `,`.charCodeAt(0);\nexports.comma = comma;\nvar colon = 58; // `:`.charCodeAt(0);\nexports.colon = colon;\nvar semicolon = 59; // `;`.charCodeAt(0);\nexports.semicolon = semicolon;\nvar openParenthesis = 40; // `(`.charCodeAt(0);\nexports.openParenthesis = openParenthesis;\nvar closeParenthesis = 41; // `)`.charCodeAt(0);\nexports.closeParenthesis = closeParenthesis;\nvar openSquare = 91; // `[`.charCodeAt(0);\nexports.openSquare = openSquare;\nvar closeSquare = 93; // `]`.charCodeAt(0);\nexports.closeSquare = closeSquare;\nvar dollar = 36; // `$`.charCodeAt(0);\nexports.dollar = dollar;\nvar tilde = 126; // `~`.charCodeAt(0);\nexports.tilde = tilde;\nvar caret = 94; // `^`.charCodeAt(0);\nexports.caret = caret;\nvar plus = 43; // `+`.charCodeAt(0);\nexports.plus = plus;\nvar equals = 61; // `=`.charCodeAt(0);\nexports.equals = equals;\nvar pipe = 124; // `|`.charCodeAt(0);\nexports.pipe = pipe;\nvar greaterThan = 62; // `>`.charCodeAt(0);\nexports.greaterThan = greaterThan;\nvar space = 32; // ` `.charCodeAt(0);\nexports.space = space;\nvar singleQuote = 39; // `'`.charCodeAt(0);\nexports.singleQuote = singleQuote;\nvar doubleQuote = 34; // `\"`.charCodeAt(0);\nexports.doubleQuote = doubleQuote;\nvar slash = 47; // `/`.charCodeAt(0);\nexports.slash = slash;\nvar bang = 33; // `!`.charCodeAt(0);\nexports.bang = bang;\nvar backslash = 92; // '\\\\'.charCodeAt(0);\nexports.backslash = backslash;\nvar cr = 13; // '\\r'.charCodeAt(0);\nexports.cr = cr;\nvar feed = 12; // '\\f'.charCodeAt(0);\nexports.feed = feed;\nvar newline = 10; // '\\n'.charCodeAt(0);\nexports.newline = newline;\nvar tab = 9; // '\\t'.charCodeAt(0);\n\n// Expose aliases primarily for readability.\nexports.tab = tab;\nvar str = singleQuote;\n\n// No good single character representation!\nexports.str = str;\nvar comment = -1;\nexports.comment = comment;\nvar word = -2;\nexports.word = word;\nvar combinator = -3;\nexports.combinator = combinator;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,IAAI,GAAG,QAAQ,KAAK,GAAG,QAAQ,GAAG,GAAG,QAAQ,GAAG,GAAG,QAAQ,KAAK,GAAG,QAAQ,KAAK,GAAG,QAAQ,WAAW,GAAG,QAAQ,SAAS,GAAG,QAAQ,IAAI,GAAG,QAAQ,IAAI,GAAG,QAAQ,UAAU,GAAG,QAAQ,eAAe,GAAG,QAAQ,OAAO,GAAG,QAAQ,WAAW,GAAG,QAAQ,IAAI,GAAG,QAAQ,MAAM,GAAG,QAAQ,WAAW,GAAG,QAAQ,MAAM,GAAG,QAAQ,EAAE,GAAG,QAAQ,OAAO,GAAG,QAAQ,KAAK,GAAG,QAAQ,UAAU,GAAG,QAAQ,KAAK,GAAG,QAAQ,WAAW,GAAG,QAAQ,gBAAgB,GAAG,QAAQ,KAAK,GAAG,QAAQ,IAAI,GAAG,QAAQ,SAAS,GAAG,QAAQ,EAAE,GAAG,QAAQ,QAAQ,GAAG,QAAQ,SAAS,GAAG,KAAK;AACljB,IAAI,YAAY,IAAI,qBAAqB;AACzC,QAAQ,SAAS,GAAG;AACpB,IAAI,WAAW,IAAI,qBAAqB;AACxC,QAAQ,QAAQ,GAAG;AACnB,IAAI,KAAK,IAAI,qBAAqB;AAClC,QAAQ,EAAE,GAAG;AACb,IAAI,QAAQ,IAAI,qBAAqB;AACrC,QAAQ,KAAK,GAAG;AAChB,IAAI,QAAQ,IAAI,qBAAqB;AACrC,QAAQ,KAAK,GAAG;AAChB,IAAI,YAAY,IAAI,qBAAqB;AACzC,QAAQ,SAAS,GAAG;AACpB,IAAI,kBAAkB,IAAI,qBAAqB;AAC/C,QAAQ,eAAe,GAAG;AAC1B,IAAI,mBAAmB,IAAI,qBAAqB;AAChD,QAAQ,gBAAgB,GAAG;AAC3B,IAAI,aAAa,IAAI,qBAAqB;AAC1C,QAAQ,UAAU,GAAG;AACrB,IAAI,cAAc,IAAI,qBAAqB;AAC3C,QAAQ,WAAW,GAAG;AACtB,IAAI,SAAS,IAAI,qBAAqB;AACtC,QAAQ,MAAM,GAAG;AACjB,IAAI,QAAQ,KAAK,qBAAqB;AACtC,QAAQ,KAAK,GAAG;AAChB,IAAI,QAAQ,IAAI,qBAAqB;AACrC,QAAQ,KAAK,GAAG;AAChB,IAAI,OAAO,IAAI,qBAAqB;AACpC,QAAQ,IAAI,GAAG;AACf,IAAI,SAAS,IAAI,qBAAqB;AACtC,QAAQ,MAAM,GAAG;AACjB,IAAI,OAAO,KAAK,qBAAqB;AACrC,QAAQ,IAAI,GAAG;AACf,IAAI,cAAc,IAAI,qBAAqB;AAC3C,QAAQ,WAAW,GAAG;AACtB,IAAI,QAAQ,IAAI,qBAAqB;AACrC,QAAQ,KAAK,GAAG;AAChB,IAAI,cAAc,IAAI,qBAAqB;AAC3C,QAAQ,WAAW,GAAG;AACtB,IAAI,cAAc,IAAI,qBAAqB;AAC3C,QAAQ,WAAW,GAAG;AACtB,IAAI,QAAQ,IAAI,qBAAqB;AACrC,QAAQ,KAAK,GAAG;AAChB,IAAI,OAAO,IAAI,qBAAqB;AACpC,QAAQ,IAAI,GAAG;AACf,IAAI,YAAY,IAAI,sBAAsB;AAC1C,QAAQ,SAAS,GAAG;AACpB,IAAI,KAAK,IAAI,sBAAsB;AACnC,QAAQ,EAAE,GAAG;AACb,IAAI,OAAO,IAAI,sBAAsB;AACrC,QAAQ,IAAI,GAAG;AACf,IAAI,UAAU,IAAI,sBAAsB;AACxC,QAAQ,OAAO,GAAG;AAClB,IAAI,MAAM,GAAG,sBAAsB;AAEnC,4CAA4C;AAC5C,QAAQ,GAAG,GAAG;AACd,IAAI,MAAM;AAEV,2CAA2C;AAC3C,QAAQ,GAAG,GAAG;AACd,IAAI,UAAU,CAAC;AACf,QAAQ,OAAO,GAAG;AAClB,IAAI,OAAO,CAAC;AACZ,QAAQ,IAAI,GAAG;AACf,IAAI,aAAa,CAAC;AAClB,QAAQ,UAAU,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1946, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/tokenize.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.FIELDS = void 0;\nexports[\"default\"] = tokenize;\nvar t = _interopRequireWildcard(require(\"./tokenTypes\"));\nvar _unescapable, _wordDelimiters;\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\nvar unescapable = (_unescapable = {}, _unescapable[t.tab] = true, _unescapable[t.newline] = true, _unescapable[t.cr] = true, _unescapable[t.feed] = true, _unescapable);\nvar wordDelimiters = (_wordDelimiters = {}, _wordDelimiters[t.space] = true, _wordDelimiters[t.tab] = true, _wordDelimiters[t.newline] = true, _wordDelimiters[t.cr] = true, _wordDelimiters[t.feed] = true, _wordDelimiters[t.ampersand] = true, _wordDelimiters[t.asterisk] = true, _wordDelimiters[t.bang] = true, _wordDelimiters[t.comma] = true, _wordDelimiters[t.colon] = true, _wordDelimiters[t.semicolon] = true, _wordDelimiters[t.openParenthesis] = true, _wordDelimiters[t.closeParenthesis] = true, _wordDelimiters[t.openSquare] = true, _wordDelimiters[t.closeSquare] = true, _wordDelimiters[t.singleQuote] = true, _wordDelimiters[t.doubleQuote] = true, _wordDelimiters[t.plus] = true, _wordDelimiters[t.pipe] = true, _wordDelimiters[t.tilde] = true, _wordDelimiters[t.greaterThan] = true, _wordDelimiters[t.equals] = true, _wordDelimiters[t.dollar] = true, _wordDelimiters[t.caret] = true, _wordDelimiters[t.slash] = true, _wordDelimiters);\nvar hex = {};\nvar hexChars = \"0123456789abcdefABCDEF\";\nfor (var i = 0; i < hexChars.length; i++) {\n  hex[hexChars.charCodeAt(i)] = true;\n}\n\n/**\n *  Returns the last index of the bar css word\n * @param {string} css The string in which the word begins\n * @param {number} start The index into the string where word's first letter occurs\n */\nfunction consumeWord(css, start) {\n  var next = start;\n  var code;\n  do {\n    code = css.charCodeAt(next);\n    if (wordDelimiters[code]) {\n      return next - 1;\n    } else if (code === t.backslash) {\n      next = consumeEscape(css, next) + 1;\n    } else {\n      // All other characters are part of the word\n      next++;\n    }\n  } while (next < css.length);\n  return next - 1;\n}\n\n/**\n *  Returns the last index of the escape sequence\n * @param {string} css The string in which the sequence begins\n * @param {number} start The index into the string where escape character (`\\`) occurs.\n */\nfunction consumeEscape(css, start) {\n  var next = start;\n  var code = css.charCodeAt(next + 1);\n  if (unescapable[code]) {\n    // just consume the escape char\n  } else if (hex[code]) {\n    var hexDigits = 0;\n    // consume up to 6 hex chars\n    do {\n      next++;\n      hexDigits++;\n      code = css.charCodeAt(next + 1);\n    } while (hex[code] && hexDigits < 6);\n    // if fewer than 6 hex chars, a trailing space ends the escape\n    if (hexDigits < 6 && code === t.space) {\n      next++;\n    }\n  } else {\n    // the next char is part of the current word\n    next++;\n  }\n  return next;\n}\nvar FIELDS = {\n  TYPE: 0,\n  START_LINE: 1,\n  START_COL: 2,\n  END_LINE: 3,\n  END_COL: 4,\n  START_POS: 5,\n  END_POS: 6\n};\nexports.FIELDS = FIELDS;\nfunction tokenize(input) {\n  var tokens = [];\n  var css = input.css.valueOf();\n  var _css = css,\n    length = _css.length;\n  var offset = -1;\n  var line = 1;\n  var start = 0;\n  var end = 0;\n  var code, content, endColumn, endLine, escaped, escapePos, last, lines, next, nextLine, nextOffset, quote, tokenType;\n  function unclosed(what, fix) {\n    if (input.safe) {\n      // fyi: this is never set to true.\n      css += fix;\n      next = css.length - 1;\n    } else {\n      throw input.error('Unclosed ' + what, line, start - offset, start);\n    }\n  }\n  while (start < length) {\n    code = css.charCodeAt(start);\n    if (code === t.newline) {\n      offset = start;\n      line += 1;\n    }\n    switch (code) {\n      case t.space:\n      case t.tab:\n      case t.newline:\n      case t.cr:\n      case t.feed:\n        next = start;\n        do {\n          next += 1;\n          code = css.charCodeAt(next);\n          if (code === t.newline) {\n            offset = next;\n            line += 1;\n          }\n        } while (code === t.space || code === t.newline || code === t.tab || code === t.cr || code === t.feed);\n        tokenType = t.space;\n        endLine = line;\n        endColumn = next - offset - 1;\n        end = next;\n        break;\n      case t.plus:\n      case t.greaterThan:\n      case t.tilde:\n      case t.pipe:\n        next = start;\n        do {\n          next += 1;\n          code = css.charCodeAt(next);\n        } while (code === t.plus || code === t.greaterThan || code === t.tilde || code === t.pipe);\n        tokenType = t.combinator;\n        endLine = line;\n        endColumn = start - offset;\n        end = next;\n        break;\n\n      // Consume these characters as single tokens.\n      case t.asterisk:\n      case t.ampersand:\n      case t.bang:\n      case t.comma:\n      case t.equals:\n      case t.dollar:\n      case t.caret:\n      case t.openSquare:\n      case t.closeSquare:\n      case t.colon:\n      case t.semicolon:\n      case t.openParenthesis:\n      case t.closeParenthesis:\n        next = start;\n        tokenType = code;\n        endLine = line;\n        endColumn = start - offset;\n        end = next + 1;\n        break;\n      case t.singleQuote:\n      case t.doubleQuote:\n        quote = code === t.singleQuote ? \"'\" : '\"';\n        next = start;\n        do {\n          escaped = false;\n          next = css.indexOf(quote, next + 1);\n          if (next === -1) {\n            unclosed('quote', quote);\n          }\n          escapePos = next;\n          while (css.charCodeAt(escapePos - 1) === t.backslash) {\n            escapePos -= 1;\n            escaped = !escaped;\n          }\n        } while (escaped);\n        tokenType = t.str;\n        endLine = line;\n        endColumn = start - offset;\n        end = next + 1;\n        break;\n      default:\n        if (code === t.slash && css.charCodeAt(start + 1) === t.asterisk) {\n          next = css.indexOf('*/', start + 2) + 1;\n          if (next === 0) {\n            unclosed('comment', '*/');\n          }\n          content = css.slice(start, next + 1);\n          lines = content.split('\\n');\n          last = lines.length - 1;\n          if (last > 0) {\n            nextLine = line + last;\n            nextOffset = next - lines[last].length;\n          } else {\n            nextLine = line;\n            nextOffset = offset;\n          }\n          tokenType = t.comment;\n          line = nextLine;\n          endLine = nextLine;\n          endColumn = next - nextOffset;\n        } else if (code === t.slash) {\n          next = start;\n          tokenType = code;\n          endLine = line;\n          endColumn = start - offset;\n          end = next + 1;\n        } else {\n          next = consumeWord(css, start);\n          tokenType = t.word;\n          endLine = line;\n          endColumn = next - offset;\n        }\n        end = next + 1;\n        break;\n    }\n\n    // Ensure that the token structure remains consistent\n    tokens.push([tokenType,\n    // [0] Token type\n    line,\n    // [1] Starting line\n    start - offset,\n    // [2] Starting column\n    endLine,\n    // [3] Ending line\n    endColumn,\n    // [4] Ending column\n    start,\n    // [5] Start position / Source index\n    end // [6] End position\n    ]);\n\n    // Reset offset for the next token\n    if (nextOffset) {\n      offset = nextOffset;\n      nextOffset = null;\n    }\n    start = end;\n  }\n  return tokens;\n}"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,MAAM,GAAG,KAAK;AACtB,OAAO,CAAC,UAAU,GAAG;AACrB,IAAI,IAAI;AACR,IAAI,cAAc;AAClB,SAAS,yBAAyB,WAAW;IAAI,IAAI,OAAO,YAAY,YAAY,OAAO;IAAM,IAAI,oBAAoB,IAAI;IAAW,IAAI,mBAAmB,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAS,yBAAyB,WAAW;QAAI,OAAO,cAAc,mBAAmB;IAAmB,CAAC,EAAE;AAAc;AAC9U,SAAS,wBAAwB,GAAG,EAAE,WAAW;IAAI,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE;QAAE,OAAO;IAAK;IAAE,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY;QAAE,OAAO;YAAE,WAAW;QAAI;IAAG;IAAE,IAAI,QAAQ,yBAAyB;IAAc,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM;QAAE,OAAO,MAAM,GAAG,CAAC;IAAM;IAAE,IAAI,SAAS,CAAC;IAAG,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,OAAO,IAAK;QAAE,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YAAE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAAM,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG;gBAAE,OAAO,cAAc,CAAC,QAAQ,KAAK;YAAO,OAAO;gBAAE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;YAAE;QAAE;IAAE;IAAE,MAAM,CAAC,UAAU,GAAG;IAAK,IAAI,OAAO;QAAE,MAAM,GAAG,CAAC,KAAK;IAAS;IAAE,OAAO;AAAQ;AACxyB,IAAI,cAAc,CAAC,eAAe,CAAC,GAAG,YAAY,CAAC,EAAE,GAAG,CAAC,GAAG,MAAM,YAAY,CAAC,EAAE,OAAO,CAAC,GAAG,MAAM,YAAY,CAAC,EAAE,EAAE,CAAC,GAAG,MAAM,YAAY,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,YAAY;AACtK,IAAI,iBAAiB,CAAC,kBAAkB,CAAC,GAAG,eAAe,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,GAAG,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,OAAO,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,EAAE,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,SAAS,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,QAAQ,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,SAAS,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,eAAe,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,gBAAgB,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,UAAU,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,WAAW,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,WAAW,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,WAAW,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,WAAW,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,MAAM,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,MAAM,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,eAAe,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,eAAe;AAC56B,IAAI,MAAM,CAAC;AACX,IAAI,WAAW;AACf,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;IACxC,GAAG,CAAC,SAAS,UAAU,CAAC,GAAG,GAAG;AAChC;AAEA;;;;CAIC,GACD,SAAS,YAAY,GAAG,EAAE,KAAK;IAC7B,IAAI,OAAO;IACX,IAAI;IACJ,GAAG;QACD,OAAO,IAAI,UAAU,CAAC;QACtB,IAAI,cAAc,CAAC,KAAK,EAAE;YACxB,OAAO,OAAO;QAChB,OAAO,IAAI,SAAS,EAAE,SAAS,EAAE;YAC/B,OAAO,cAAc,KAAK,QAAQ;QACpC,OAAO;YACL,4CAA4C;YAC5C;QACF;IACF,QAAS,OAAO,IAAI,MAAM,CAAE;IAC5B,OAAO,OAAO;AAChB;AAEA;;;;CAIC,GACD,SAAS,cAAc,GAAG,EAAE,KAAK;IAC/B,IAAI,OAAO;IACX,IAAI,OAAO,IAAI,UAAU,CAAC,OAAO;IACjC,IAAI,WAAW,CAAC,KAAK,EAAE;IACrB,+BAA+B;IACjC,OAAO,IAAI,GAAG,CAAC,KAAK,EAAE;QACpB,IAAI,YAAY;QAChB,4BAA4B;QAC5B,GAAG;YACD;YACA;YACA,OAAO,IAAI,UAAU,CAAC,OAAO;QAC/B,QAAS,GAAG,CAAC,KAAK,IAAI,YAAY,EAAG;QACrC,8DAA8D;QAC9D,IAAI,YAAY,KAAK,SAAS,EAAE,KAAK,EAAE;YACrC;QACF;IACF,OAAO;QACL,4CAA4C;QAC5C;IACF;IACA,OAAO;AACT;AACA,IAAI,SAAS;IACX,MAAM;IACN,YAAY;IACZ,WAAW;IACX,UAAU;IACV,SAAS;IACT,WAAW;IACX,SAAS;AACX;AACA,QAAQ,MAAM,GAAG;AACjB,SAAS,SAAS,KAAK;IACrB,IAAI,SAAS,EAAE;IACf,IAAI,MAAM,MAAM,GAAG,CAAC,OAAO;IAC3B,IAAI,OAAO,KACT,SAAS,KAAK,MAAM;IACtB,IAAI,SAAS,CAAC;IACd,IAAI,OAAO;IACX,IAAI,QAAQ;IACZ,IAAI,MAAM;IACV,IAAI,MAAM,SAAS,WAAW,SAAS,SAAS,WAAW,MAAM,OAAO,MAAM,UAAU,YAAY,OAAO;IAC3G,SAAS,SAAS,IAAI,EAAE,GAAG;QACzB,IAAI,MAAM,IAAI,EAAE;YACd,kCAAkC;YAClC,OAAO;YACP,OAAO,IAAI,MAAM,GAAG;QACtB,OAAO;YACL,MAAM,MAAM,KAAK,CAAC,cAAc,MAAM,MAAM,QAAQ,QAAQ;QAC9D;IACF;IACA,MAAO,QAAQ,OAAQ;QACrB,OAAO,IAAI,UAAU,CAAC;QACtB,IAAI,SAAS,EAAE,OAAO,EAAE;YACtB,SAAS;YACT,QAAQ;QACV;QACA,OAAQ;YACN,KAAK,EAAE,KAAK;YACZ,KAAK,EAAE,GAAG;YACV,KAAK,EAAE,OAAO;YACd,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,IAAI;gBACT,OAAO;gBACP,GAAG;oBACD,QAAQ;oBACR,OAAO,IAAI,UAAU,CAAC;oBACtB,IAAI,SAAS,EAAE,OAAO,EAAE;wBACtB,SAAS;wBACT,QAAQ;oBACV;gBACF,QAAS,SAAS,EAAE,KAAK,IAAI,SAAS,EAAE,OAAO,IAAI,SAAS,EAAE,GAAG,IAAI,SAAS,EAAE,EAAE,IAAI,SAAS,EAAE,IAAI,CAAE;gBACvG,YAAY,EAAE,KAAK;gBACnB,UAAU;gBACV,YAAY,OAAO,SAAS;gBAC5B,MAAM;gBACN;YACF,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,WAAW;YAClB,KAAK,EAAE,KAAK;YACZ,KAAK,EAAE,IAAI;gBACT,OAAO;gBACP,GAAG;oBACD,QAAQ;oBACR,OAAO,IAAI,UAAU,CAAC;gBACxB,QAAS,SAAS,EAAE,IAAI,IAAI,SAAS,EAAE,WAAW,IAAI,SAAS,EAAE,KAAK,IAAI,SAAS,EAAE,IAAI,CAAE;gBAC3F,YAAY,EAAE,UAAU;gBACxB,UAAU;gBACV,YAAY,QAAQ;gBACpB,MAAM;gBACN;YAEF,6CAA6C;YAC7C,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,SAAS;YAChB,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,KAAK;YACZ,KAAK,EAAE,MAAM;YACb,KAAK,EAAE,MAAM;YACb,KAAK,EAAE,KAAK;YACZ,KAAK,EAAE,UAAU;YACjB,KAAK,EAAE,WAAW;YAClB,KAAK,EAAE,KAAK;YACZ,KAAK,EAAE,SAAS;YAChB,KAAK,EAAE,eAAe;YACtB,KAAK,EAAE,gBAAgB;gBACrB,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,YAAY,QAAQ;gBACpB,MAAM,OAAO;gBACb;YACF,KAAK,EAAE,WAAW;YAClB,KAAK,EAAE,WAAW;gBAChB,QAAQ,SAAS,EAAE,WAAW,GAAG,MAAM;gBACvC,OAAO;gBACP,GAAG;oBACD,UAAU;oBACV,OAAO,IAAI,OAAO,CAAC,OAAO,OAAO;oBACjC,IAAI,SAAS,CAAC,GAAG;wBACf,SAAS,SAAS;oBACpB;oBACA,YAAY;oBACZ,MAAO,IAAI,UAAU,CAAC,YAAY,OAAO,EAAE,SAAS,CAAE;wBACpD,aAAa;wBACb,UAAU,CAAC;oBACb;gBACF,QAAS,QAAS;gBAClB,YAAY,EAAE,GAAG;gBACjB,UAAU;gBACV,YAAY,QAAQ;gBACpB,MAAM,OAAO;gBACb;YACF;gBACE,IAAI,SAAS,EAAE,KAAK,IAAI,IAAI,UAAU,CAAC,QAAQ,OAAO,EAAE,QAAQ,EAAE;oBAChE,OAAO,IAAI,OAAO,CAAC,MAAM,QAAQ,KAAK;oBACtC,IAAI,SAAS,GAAG;wBACd,SAAS,WAAW;oBACtB;oBACA,UAAU,IAAI,KAAK,CAAC,OAAO,OAAO;oBAClC,QAAQ,QAAQ,KAAK,CAAC;oBACtB,OAAO,MAAM,MAAM,GAAG;oBACtB,IAAI,OAAO,GAAG;wBACZ,WAAW,OAAO;wBAClB,aAAa,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM;oBACxC,OAAO;wBACL,WAAW;wBACX,aAAa;oBACf;oBACA,YAAY,EAAE,OAAO;oBACrB,OAAO;oBACP,UAAU;oBACV,YAAY,OAAO;gBACrB,OAAO,IAAI,SAAS,EAAE,KAAK,EAAE;oBAC3B,OAAO;oBACP,YAAY;oBACZ,UAAU;oBACV,YAAY,QAAQ;oBACpB,MAAM,OAAO;gBACf,OAAO;oBACL,OAAO,YAAY,KAAK;oBACxB,YAAY,EAAE,IAAI;oBAClB,UAAU;oBACV,YAAY,OAAO;gBACrB;gBACA,MAAM,OAAO;gBACb;QACJ;QAEA,qDAAqD;QACrD,OAAO,IAAI,CAAC;YAAC;YACb,iBAAiB;YACjB;YACA,oBAAoB;YACpB,QAAQ;YACR,sBAAsB;YACtB;YACA,kBAAkB;YAClB;YACA,oBAAoB;YACpB;YACA,oCAAoC;YACpC,IAAI,mBAAmB;SACtB;QAED,kCAAkC;QAClC,IAAI,YAAY;YACd,SAAS;YACT,aAAa;QACf;QACA,QAAQ;IACV;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/parser.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _root = _interopRequireDefault(require(\"./selectors/root\"));\nvar _selector = _interopRequireDefault(require(\"./selectors/selector\"));\nvar _className = _interopRequireDefault(require(\"./selectors/className\"));\nvar _comment = _interopRequireDefault(require(\"./selectors/comment\"));\nvar _id = _interopRequireDefault(require(\"./selectors/id\"));\nvar _tag = _interopRequireDefault(require(\"./selectors/tag\"));\nvar _string = _interopRequireDefault(require(\"./selectors/string\"));\nvar _pseudo = _interopRequireDefault(require(\"./selectors/pseudo\"));\nvar _attribute = _interopRequireWildcard(require(\"./selectors/attribute\"));\nvar _universal = _interopRequireDefault(require(\"./selectors/universal\"));\nvar _combinator = _interopRequireDefault(require(\"./selectors/combinator\"));\nvar _nesting = _interopRequireDefault(require(\"./selectors/nesting\"));\nvar _sortAscending = _interopRequireDefault(require(\"./sortAscending\"));\nvar _tokenize = _interopRequireWildcard(require(\"./tokenize\"));\nvar tokens = _interopRequireWildcard(require(\"./tokenTypes\"));\nvar types = _interopRequireWildcard(require(\"./selectors/types\"));\nvar _util = require(\"./util\");\nvar _WHITESPACE_TOKENS, _Object$assign;\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nvar WHITESPACE_TOKENS = (_WHITESPACE_TOKENS = {}, _WHITESPACE_TOKENS[tokens.space] = true, _WHITESPACE_TOKENS[tokens.cr] = true, _WHITESPACE_TOKENS[tokens.feed] = true, _WHITESPACE_TOKENS[tokens.newline] = true, _WHITESPACE_TOKENS[tokens.tab] = true, _WHITESPACE_TOKENS);\nvar WHITESPACE_EQUIV_TOKENS = Object.assign({}, WHITESPACE_TOKENS, (_Object$assign = {}, _Object$assign[tokens.comment] = true, _Object$assign));\nfunction tokenStart(token) {\n  return {\n    line: token[_tokenize.FIELDS.START_LINE],\n    column: token[_tokenize.FIELDS.START_COL]\n  };\n}\nfunction tokenEnd(token) {\n  return {\n    line: token[_tokenize.FIELDS.END_LINE],\n    column: token[_tokenize.FIELDS.END_COL]\n  };\n}\nfunction getSource(startLine, startColumn, endLine, endColumn) {\n  return {\n    start: {\n      line: startLine,\n      column: startColumn\n    },\n    end: {\n      line: endLine,\n      column: endColumn\n    }\n  };\n}\nfunction getTokenSource(token) {\n  return getSource(token[_tokenize.FIELDS.START_LINE], token[_tokenize.FIELDS.START_COL], token[_tokenize.FIELDS.END_LINE], token[_tokenize.FIELDS.END_COL]);\n}\nfunction getTokenSourceSpan(startToken, endToken) {\n  if (!startToken) {\n    return undefined;\n  }\n  return getSource(startToken[_tokenize.FIELDS.START_LINE], startToken[_tokenize.FIELDS.START_COL], endToken[_tokenize.FIELDS.END_LINE], endToken[_tokenize.FIELDS.END_COL]);\n}\nfunction unescapeProp(node, prop) {\n  var value = node[prop];\n  if (typeof value !== \"string\") {\n    return;\n  }\n  if (value.indexOf(\"\\\\\") !== -1) {\n    (0, _util.ensureObject)(node, 'raws');\n    node[prop] = (0, _util.unesc)(value);\n    if (node.raws[prop] === undefined) {\n      node.raws[prop] = value;\n    }\n  }\n  return node;\n}\nfunction indexesOf(array, item) {\n  var i = -1;\n  var indexes = [];\n  while ((i = array.indexOf(item, i + 1)) !== -1) {\n    indexes.push(i);\n  }\n  return indexes;\n}\nfunction uniqs() {\n  var list = Array.prototype.concat.apply([], arguments);\n  return list.filter(function (item, i) {\n    return i === list.indexOf(item);\n  });\n}\nvar Parser = /*#__PURE__*/function () {\n  function Parser(rule, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    this.rule = rule;\n    this.options = Object.assign({\n      lossy: false,\n      safe: false\n    }, options);\n    this.position = 0;\n    this.css = typeof this.rule === 'string' ? this.rule : this.rule.selector;\n    this.tokens = (0, _tokenize[\"default\"])({\n      css: this.css,\n      error: this._errorGenerator(),\n      safe: this.options.safe\n    });\n    var rootSource = getTokenSourceSpan(this.tokens[0], this.tokens[this.tokens.length - 1]);\n    this.root = new _root[\"default\"]({\n      source: rootSource\n    });\n    this.root.errorGenerator = this._errorGenerator();\n    var selector = new _selector[\"default\"]({\n      source: {\n        start: {\n          line: 1,\n          column: 1\n        }\n      },\n      sourceIndex: 0\n    });\n    this.root.append(selector);\n    this.current = selector;\n    this.loop();\n  }\n  var _proto = Parser.prototype;\n  _proto._errorGenerator = function _errorGenerator() {\n    var _this = this;\n    return function (message, errorOptions) {\n      if (typeof _this.rule === 'string') {\n        return new Error(message);\n      }\n      return _this.rule.error(message, errorOptions);\n    };\n  };\n  _proto.attribute = function attribute() {\n    var attr = [];\n    var startingToken = this.currToken;\n    this.position++;\n    while (this.position < this.tokens.length && this.currToken[_tokenize.FIELDS.TYPE] !== tokens.closeSquare) {\n      attr.push(this.currToken);\n      this.position++;\n    }\n    if (this.currToken[_tokenize.FIELDS.TYPE] !== tokens.closeSquare) {\n      return this.expected('closing square bracket', this.currToken[_tokenize.FIELDS.START_POS]);\n    }\n    var len = attr.length;\n    var node = {\n      source: getSource(startingToken[1], startingToken[2], this.currToken[3], this.currToken[4]),\n      sourceIndex: startingToken[_tokenize.FIELDS.START_POS]\n    };\n    if (len === 1 && !~[tokens.word].indexOf(attr[0][_tokenize.FIELDS.TYPE])) {\n      return this.expected('attribute', attr[0][_tokenize.FIELDS.START_POS]);\n    }\n    var pos = 0;\n    var spaceBefore = '';\n    var commentBefore = '';\n    var lastAdded = null;\n    var spaceAfterMeaningfulToken = false;\n    while (pos < len) {\n      var token = attr[pos];\n      var content = this.content(token);\n      var next = attr[pos + 1];\n      switch (token[_tokenize.FIELDS.TYPE]) {\n        case tokens.space:\n          // if (\n          //     len === 1 ||\n          //     pos === 0 && this.content(next) === '|'\n          // ) {\n          //     return this.expected('attribute', token[TOKEN.START_POS], content);\n          // }\n          spaceAfterMeaningfulToken = true;\n          if (this.options.lossy) {\n            break;\n          }\n          if (lastAdded) {\n            (0, _util.ensureObject)(node, 'spaces', lastAdded);\n            var prevContent = node.spaces[lastAdded].after || '';\n            node.spaces[lastAdded].after = prevContent + content;\n            var existingComment = (0, _util.getProp)(node, 'raws', 'spaces', lastAdded, 'after') || null;\n            if (existingComment) {\n              node.raws.spaces[lastAdded].after = existingComment + content;\n            }\n          } else {\n            spaceBefore = spaceBefore + content;\n            commentBefore = commentBefore + content;\n          }\n          break;\n        case tokens.asterisk:\n          if (next[_tokenize.FIELDS.TYPE] === tokens.equals) {\n            node.operator = content;\n            lastAdded = 'operator';\n          } else if ((!node.namespace || lastAdded === \"namespace\" && !spaceAfterMeaningfulToken) && next) {\n            if (spaceBefore) {\n              (0, _util.ensureObject)(node, 'spaces', 'attribute');\n              node.spaces.attribute.before = spaceBefore;\n              spaceBefore = '';\n            }\n            if (commentBefore) {\n              (0, _util.ensureObject)(node, 'raws', 'spaces', 'attribute');\n              node.raws.spaces.attribute.before = spaceBefore;\n              commentBefore = '';\n            }\n            node.namespace = (node.namespace || \"\") + content;\n            var rawValue = (0, _util.getProp)(node, 'raws', 'namespace') || null;\n            if (rawValue) {\n              node.raws.namespace += content;\n            }\n            lastAdded = 'namespace';\n          }\n          spaceAfterMeaningfulToken = false;\n          break;\n        case tokens.dollar:\n          if (lastAdded === \"value\") {\n            var oldRawValue = (0, _util.getProp)(node, 'raws', 'value');\n            node.value += \"$\";\n            if (oldRawValue) {\n              node.raws.value = oldRawValue + \"$\";\n            }\n            break;\n          }\n        // Falls through\n        case tokens.caret:\n          if (next[_tokenize.FIELDS.TYPE] === tokens.equals) {\n            node.operator = content;\n            lastAdded = 'operator';\n          }\n          spaceAfterMeaningfulToken = false;\n          break;\n        case tokens.combinator:\n          if (content === '~' && next[_tokenize.FIELDS.TYPE] === tokens.equals) {\n            node.operator = content;\n            lastAdded = 'operator';\n          }\n          if (content !== '|') {\n            spaceAfterMeaningfulToken = false;\n            break;\n          }\n          if (next[_tokenize.FIELDS.TYPE] === tokens.equals) {\n            node.operator = content;\n            lastAdded = 'operator';\n          } else if (!node.namespace && !node.attribute) {\n            node.namespace = true;\n          }\n          spaceAfterMeaningfulToken = false;\n          break;\n        case tokens.word:\n          if (next && this.content(next) === '|' && attr[pos + 2] && attr[pos + 2][_tokenize.FIELDS.TYPE] !== tokens.equals &&\n          // this look-ahead probably fails with comment nodes involved.\n          !node.operator && !node.namespace) {\n            node.namespace = content;\n            lastAdded = 'namespace';\n          } else if (!node.attribute || lastAdded === \"attribute\" && !spaceAfterMeaningfulToken) {\n            if (spaceBefore) {\n              (0, _util.ensureObject)(node, 'spaces', 'attribute');\n              node.spaces.attribute.before = spaceBefore;\n              spaceBefore = '';\n            }\n            if (commentBefore) {\n              (0, _util.ensureObject)(node, 'raws', 'spaces', 'attribute');\n              node.raws.spaces.attribute.before = commentBefore;\n              commentBefore = '';\n            }\n            node.attribute = (node.attribute || \"\") + content;\n            var _rawValue = (0, _util.getProp)(node, 'raws', 'attribute') || null;\n            if (_rawValue) {\n              node.raws.attribute += content;\n            }\n            lastAdded = 'attribute';\n          } else if (!node.value && node.value !== \"\" || lastAdded === \"value\" && !(spaceAfterMeaningfulToken || node.quoteMark)) {\n            var _unescaped = (0, _util.unesc)(content);\n            var _oldRawValue = (0, _util.getProp)(node, 'raws', 'value') || '';\n            var oldValue = node.value || '';\n            node.value = oldValue + _unescaped;\n            node.quoteMark = null;\n            if (_unescaped !== content || _oldRawValue) {\n              (0, _util.ensureObject)(node, 'raws');\n              node.raws.value = (_oldRawValue || oldValue) + content;\n            }\n            lastAdded = 'value';\n          } else {\n            var insensitive = content === 'i' || content === \"I\";\n            if ((node.value || node.value === '') && (node.quoteMark || spaceAfterMeaningfulToken)) {\n              node.insensitive = insensitive;\n              if (!insensitive || content === \"I\") {\n                (0, _util.ensureObject)(node, 'raws');\n                node.raws.insensitiveFlag = content;\n              }\n              lastAdded = 'insensitive';\n              if (spaceBefore) {\n                (0, _util.ensureObject)(node, 'spaces', 'insensitive');\n                node.spaces.insensitive.before = spaceBefore;\n                spaceBefore = '';\n              }\n              if (commentBefore) {\n                (0, _util.ensureObject)(node, 'raws', 'spaces', 'insensitive');\n                node.raws.spaces.insensitive.before = commentBefore;\n                commentBefore = '';\n              }\n            } else if (node.value || node.value === '') {\n              lastAdded = 'value';\n              node.value += content;\n              if (node.raws.value) {\n                node.raws.value += content;\n              }\n            }\n          }\n          spaceAfterMeaningfulToken = false;\n          break;\n        case tokens.str:\n          if (!node.attribute || !node.operator) {\n            return this.error(\"Expected an attribute followed by an operator preceding the string.\", {\n              index: token[_tokenize.FIELDS.START_POS]\n            });\n          }\n          var _unescapeValue = (0, _attribute.unescapeValue)(content),\n            unescaped = _unescapeValue.unescaped,\n            quoteMark = _unescapeValue.quoteMark;\n          node.value = unescaped;\n          node.quoteMark = quoteMark;\n          lastAdded = 'value';\n          (0, _util.ensureObject)(node, 'raws');\n          node.raws.value = content;\n          spaceAfterMeaningfulToken = false;\n          break;\n        case tokens.equals:\n          if (!node.attribute) {\n            return this.expected('attribute', token[_tokenize.FIELDS.START_POS], content);\n          }\n          if (node.value) {\n            return this.error('Unexpected \"=\" found; an operator was already defined.', {\n              index: token[_tokenize.FIELDS.START_POS]\n            });\n          }\n          node.operator = node.operator ? node.operator + content : content;\n          lastAdded = 'operator';\n          spaceAfterMeaningfulToken = false;\n          break;\n        case tokens.comment:\n          if (lastAdded) {\n            if (spaceAfterMeaningfulToken || next && next[_tokenize.FIELDS.TYPE] === tokens.space || lastAdded === 'insensitive') {\n              var lastComment = (0, _util.getProp)(node, 'spaces', lastAdded, 'after') || '';\n              var rawLastComment = (0, _util.getProp)(node, 'raws', 'spaces', lastAdded, 'after') || lastComment;\n              (0, _util.ensureObject)(node, 'raws', 'spaces', lastAdded);\n              node.raws.spaces[lastAdded].after = rawLastComment + content;\n            } else {\n              var lastValue = node[lastAdded] || '';\n              var rawLastValue = (0, _util.getProp)(node, 'raws', lastAdded) || lastValue;\n              (0, _util.ensureObject)(node, 'raws');\n              node.raws[lastAdded] = rawLastValue + content;\n            }\n          } else {\n            commentBefore = commentBefore + content;\n          }\n          break;\n        default:\n          return this.error(\"Unexpected \\\"\" + content + \"\\\" found.\", {\n            index: token[_tokenize.FIELDS.START_POS]\n          });\n      }\n      pos++;\n    }\n    unescapeProp(node, \"attribute\");\n    unescapeProp(node, \"namespace\");\n    this.newNode(new _attribute[\"default\"](node));\n    this.position++;\n  }\n\n  /**\n   * return a node containing meaningless garbage up to (but not including) the specified token position.\n   * if the token position is negative, all remaining tokens are consumed.\n   *\n   * This returns an array containing a single string node if all whitespace,\n   * otherwise an array of comment nodes with space before and after.\n   *\n   * These tokens are not added to the current selector, the caller can add them or use them to amend\n   * a previous node's space metadata.\n   *\n   * In lossy mode, this returns only comments.\n   */;\n  _proto.parseWhitespaceEquivalentTokens = function parseWhitespaceEquivalentTokens(stopPosition) {\n    if (stopPosition < 0) {\n      stopPosition = this.tokens.length;\n    }\n    var startPosition = this.position;\n    var nodes = [];\n    var space = \"\";\n    var lastComment = undefined;\n    do {\n      if (WHITESPACE_TOKENS[this.currToken[_tokenize.FIELDS.TYPE]]) {\n        if (!this.options.lossy) {\n          space += this.content();\n        }\n      } else if (this.currToken[_tokenize.FIELDS.TYPE] === tokens.comment) {\n        var spaces = {};\n        if (space) {\n          spaces.before = space;\n          space = \"\";\n        }\n        lastComment = new _comment[\"default\"]({\n          value: this.content(),\n          source: getTokenSource(this.currToken),\n          sourceIndex: this.currToken[_tokenize.FIELDS.START_POS],\n          spaces: spaces\n        });\n        nodes.push(lastComment);\n      }\n    } while (++this.position < stopPosition);\n    if (space) {\n      if (lastComment) {\n        lastComment.spaces.after = space;\n      } else if (!this.options.lossy) {\n        var firstToken = this.tokens[startPosition];\n        var lastToken = this.tokens[this.position - 1];\n        nodes.push(new _string[\"default\"]({\n          value: '',\n          source: getSource(firstToken[_tokenize.FIELDS.START_LINE], firstToken[_tokenize.FIELDS.START_COL], lastToken[_tokenize.FIELDS.END_LINE], lastToken[_tokenize.FIELDS.END_COL]),\n          sourceIndex: firstToken[_tokenize.FIELDS.START_POS],\n          spaces: {\n            before: space,\n            after: ''\n          }\n        }));\n      }\n    }\n    return nodes;\n  }\n\n  /**\n   *\n   * @param {*} nodes\n   */;\n  _proto.convertWhitespaceNodesToSpace = function convertWhitespaceNodesToSpace(nodes, requiredSpace) {\n    var _this2 = this;\n    if (requiredSpace === void 0) {\n      requiredSpace = false;\n    }\n    var space = \"\";\n    var rawSpace = \"\";\n    nodes.forEach(function (n) {\n      var spaceBefore = _this2.lossySpace(n.spaces.before, requiredSpace);\n      var rawSpaceBefore = _this2.lossySpace(n.rawSpaceBefore, requiredSpace);\n      space += spaceBefore + _this2.lossySpace(n.spaces.after, requiredSpace && spaceBefore.length === 0);\n      rawSpace += spaceBefore + n.value + _this2.lossySpace(n.rawSpaceAfter, requiredSpace && rawSpaceBefore.length === 0);\n    });\n    if (rawSpace === space) {\n      rawSpace = undefined;\n    }\n    var result = {\n      space: space,\n      rawSpace: rawSpace\n    };\n    return result;\n  };\n  _proto.isNamedCombinator = function isNamedCombinator(position) {\n    if (position === void 0) {\n      position = this.position;\n    }\n    return this.tokens[position + 0] && this.tokens[position + 0][_tokenize.FIELDS.TYPE] === tokens.slash && this.tokens[position + 1] && this.tokens[position + 1][_tokenize.FIELDS.TYPE] === tokens.word && this.tokens[position + 2] && this.tokens[position + 2][_tokenize.FIELDS.TYPE] === tokens.slash;\n  };\n  _proto.namedCombinator = function namedCombinator() {\n    if (this.isNamedCombinator()) {\n      var nameRaw = this.content(this.tokens[this.position + 1]);\n      var name = (0, _util.unesc)(nameRaw).toLowerCase();\n      var raws = {};\n      if (name !== nameRaw) {\n        raws.value = \"/\" + nameRaw + \"/\";\n      }\n      var node = new _combinator[\"default\"]({\n        value: \"/\" + name + \"/\",\n        source: getSource(this.currToken[_tokenize.FIELDS.START_LINE], this.currToken[_tokenize.FIELDS.START_COL], this.tokens[this.position + 2][_tokenize.FIELDS.END_LINE], this.tokens[this.position + 2][_tokenize.FIELDS.END_COL]),\n        sourceIndex: this.currToken[_tokenize.FIELDS.START_POS],\n        raws: raws\n      });\n      this.position = this.position + 3;\n      return node;\n    } else {\n      this.unexpected();\n    }\n  };\n  _proto.combinator = function combinator() {\n    var _this3 = this;\n    if (this.content() === '|') {\n      return this.namespace();\n    }\n    // We need to decide between a space that's a descendant combinator and meaningless whitespace at the end of a selector.\n    var nextSigTokenPos = this.locateNextMeaningfulToken(this.position);\n    if (nextSigTokenPos < 0 || this.tokens[nextSigTokenPos][_tokenize.FIELDS.TYPE] === tokens.comma || this.tokens[nextSigTokenPos][_tokenize.FIELDS.TYPE] === tokens.closeParenthesis) {\n      var nodes = this.parseWhitespaceEquivalentTokens(nextSigTokenPos);\n      if (nodes.length > 0) {\n        var last = this.current.last;\n        if (last) {\n          var _this$convertWhitespa = this.convertWhitespaceNodesToSpace(nodes),\n            space = _this$convertWhitespa.space,\n            rawSpace = _this$convertWhitespa.rawSpace;\n          if (rawSpace !== undefined) {\n            last.rawSpaceAfter += rawSpace;\n          }\n          last.spaces.after += space;\n        } else {\n          nodes.forEach(function (n) {\n            return _this3.newNode(n);\n          });\n        }\n      }\n      return;\n    }\n    var firstToken = this.currToken;\n    var spaceOrDescendantSelectorNodes = undefined;\n    if (nextSigTokenPos > this.position) {\n      spaceOrDescendantSelectorNodes = this.parseWhitespaceEquivalentTokens(nextSigTokenPos);\n    }\n    var node;\n    if (this.isNamedCombinator()) {\n      node = this.namedCombinator();\n    } else if (this.currToken[_tokenize.FIELDS.TYPE] === tokens.combinator) {\n      node = new _combinator[\"default\"]({\n        value: this.content(),\n        source: getTokenSource(this.currToken),\n        sourceIndex: this.currToken[_tokenize.FIELDS.START_POS]\n      });\n      this.position++;\n    } else if (WHITESPACE_TOKENS[this.currToken[_tokenize.FIELDS.TYPE]]) {\n      // pass\n    } else if (!spaceOrDescendantSelectorNodes) {\n      this.unexpected();\n    }\n    if (node) {\n      if (spaceOrDescendantSelectorNodes) {\n        var _this$convertWhitespa2 = this.convertWhitespaceNodesToSpace(spaceOrDescendantSelectorNodes),\n          _space = _this$convertWhitespa2.space,\n          _rawSpace = _this$convertWhitespa2.rawSpace;\n        node.spaces.before = _space;\n        node.rawSpaceBefore = _rawSpace;\n      }\n    } else {\n      // descendant combinator\n      var _this$convertWhitespa3 = this.convertWhitespaceNodesToSpace(spaceOrDescendantSelectorNodes, true),\n        _space2 = _this$convertWhitespa3.space,\n        _rawSpace2 = _this$convertWhitespa3.rawSpace;\n      if (!_rawSpace2) {\n        _rawSpace2 = _space2;\n      }\n      var spaces = {};\n      var raws = {\n        spaces: {}\n      };\n      if (_space2.endsWith(' ') && _rawSpace2.endsWith(' ')) {\n        spaces.before = _space2.slice(0, _space2.length - 1);\n        raws.spaces.before = _rawSpace2.slice(0, _rawSpace2.length - 1);\n      } else if (_space2.startsWith(' ') && _rawSpace2.startsWith(' ')) {\n        spaces.after = _space2.slice(1);\n        raws.spaces.after = _rawSpace2.slice(1);\n      } else {\n        raws.value = _rawSpace2;\n      }\n      node = new _combinator[\"default\"]({\n        value: ' ',\n        source: getTokenSourceSpan(firstToken, this.tokens[this.position - 1]),\n        sourceIndex: firstToken[_tokenize.FIELDS.START_POS],\n        spaces: spaces,\n        raws: raws\n      });\n    }\n    if (this.currToken && this.currToken[_tokenize.FIELDS.TYPE] === tokens.space) {\n      node.spaces.after = this.optionalSpace(this.content());\n      this.position++;\n    }\n    return this.newNode(node);\n  };\n  _proto.comma = function comma() {\n    if (this.position === this.tokens.length - 1) {\n      this.root.trailingComma = true;\n      this.position++;\n      return;\n    }\n    this.current._inferEndPosition();\n    var selector = new _selector[\"default\"]({\n      source: {\n        start: tokenStart(this.tokens[this.position + 1])\n      },\n      sourceIndex: this.tokens[this.position + 1][_tokenize.FIELDS.START_POS]\n    });\n    this.current.parent.append(selector);\n    this.current = selector;\n    this.position++;\n  };\n  _proto.comment = function comment() {\n    var current = this.currToken;\n    this.newNode(new _comment[\"default\"]({\n      value: this.content(),\n      source: getTokenSource(current),\n      sourceIndex: current[_tokenize.FIELDS.START_POS]\n    }));\n    this.position++;\n  };\n  _proto.error = function error(message, opts) {\n    throw this.root.error(message, opts);\n  };\n  _proto.missingBackslash = function missingBackslash() {\n    return this.error('Expected a backslash preceding the semicolon.', {\n      index: this.currToken[_tokenize.FIELDS.START_POS]\n    });\n  };\n  _proto.missingParenthesis = function missingParenthesis() {\n    return this.expected('opening parenthesis', this.currToken[_tokenize.FIELDS.START_POS]);\n  };\n  _proto.missingSquareBracket = function missingSquareBracket() {\n    return this.expected('opening square bracket', this.currToken[_tokenize.FIELDS.START_POS]);\n  };\n  _proto.unexpected = function unexpected() {\n    return this.error(\"Unexpected '\" + this.content() + \"'. Escaping special characters with \\\\ may help.\", this.currToken[_tokenize.FIELDS.START_POS]);\n  };\n  _proto.unexpectedPipe = function unexpectedPipe() {\n    return this.error(\"Unexpected '|'.\", this.currToken[_tokenize.FIELDS.START_POS]);\n  };\n  _proto.namespace = function namespace() {\n    var before = this.prevToken && this.content(this.prevToken) || true;\n    if (this.nextToken[_tokenize.FIELDS.TYPE] === tokens.word) {\n      this.position++;\n      return this.word(before);\n    } else if (this.nextToken[_tokenize.FIELDS.TYPE] === tokens.asterisk) {\n      this.position++;\n      return this.universal(before);\n    }\n    this.unexpectedPipe();\n  };\n  _proto.nesting = function nesting() {\n    if (this.nextToken) {\n      var nextContent = this.content(this.nextToken);\n      if (nextContent === \"|\") {\n        this.position++;\n        return;\n      }\n    }\n    var current = this.currToken;\n    this.newNode(new _nesting[\"default\"]({\n      value: this.content(),\n      source: getTokenSource(current),\n      sourceIndex: current[_tokenize.FIELDS.START_POS]\n    }));\n    this.position++;\n  };\n  _proto.parentheses = function parentheses() {\n    var last = this.current.last;\n    var unbalanced = 1;\n    this.position++;\n    if (last && last.type === types.PSEUDO) {\n      var selector = new _selector[\"default\"]({\n        source: {\n          start: tokenStart(this.tokens[this.position])\n        },\n        sourceIndex: this.tokens[this.position][_tokenize.FIELDS.START_POS]\n      });\n      var cache = this.current;\n      last.append(selector);\n      this.current = selector;\n      while (this.position < this.tokens.length && unbalanced) {\n        if (this.currToken[_tokenize.FIELDS.TYPE] === tokens.openParenthesis) {\n          unbalanced++;\n        }\n        if (this.currToken[_tokenize.FIELDS.TYPE] === tokens.closeParenthesis) {\n          unbalanced--;\n        }\n        if (unbalanced) {\n          this.parse();\n        } else {\n          this.current.source.end = tokenEnd(this.currToken);\n          this.current.parent.source.end = tokenEnd(this.currToken);\n          this.position++;\n        }\n      }\n      this.current = cache;\n    } else {\n      // I think this case should be an error. It's used to implement a basic parse of media queries\n      // but I don't think it's a good idea.\n      var parenStart = this.currToken;\n      var parenValue = \"(\";\n      var parenEnd;\n      while (this.position < this.tokens.length && unbalanced) {\n        if (this.currToken[_tokenize.FIELDS.TYPE] === tokens.openParenthesis) {\n          unbalanced++;\n        }\n        if (this.currToken[_tokenize.FIELDS.TYPE] === tokens.closeParenthesis) {\n          unbalanced--;\n        }\n        parenEnd = this.currToken;\n        parenValue += this.parseParenthesisToken(this.currToken);\n        this.position++;\n      }\n      if (last) {\n        last.appendToPropertyAndEscape(\"value\", parenValue, parenValue);\n      } else {\n        this.newNode(new _string[\"default\"]({\n          value: parenValue,\n          source: getSource(parenStart[_tokenize.FIELDS.START_LINE], parenStart[_tokenize.FIELDS.START_COL], parenEnd[_tokenize.FIELDS.END_LINE], parenEnd[_tokenize.FIELDS.END_COL]),\n          sourceIndex: parenStart[_tokenize.FIELDS.START_POS]\n        }));\n      }\n    }\n    if (unbalanced) {\n      return this.expected('closing parenthesis', this.currToken[_tokenize.FIELDS.START_POS]);\n    }\n  };\n  _proto.pseudo = function pseudo() {\n    var _this4 = this;\n    var pseudoStr = '';\n    var startingToken = this.currToken;\n    while (this.currToken && this.currToken[_tokenize.FIELDS.TYPE] === tokens.colon) {\n      pseudoStr += this.content();\n      this.position++;\n    }\n    if (!this.currToken) {\n      return this.expected(['pseudo-class', 'pseudo-element'], this.position - 1);\n    }\n    if (this.currToken[_tokenize.FIELDS.TYPE] === tokens.word) {\n      this.splitWord(false, function (first, length) {\n        pseudoStr += first;\n        _this4.newNode(new _pseudo[\"default\"]({\n          value: pseudoStr,\n          source: getTokenSourceSpan(startingToken, _this4.currToken),\n          sourceIndex: startingToken[_tokenize.FIELDS.START_POS]\n        }));\n        if (length > 1 && _this4.nextToken && _this4.nextToken[_tokenize.FIELDS.TYPE] === tokens.openParenthesis) {\n          _this4.error('Misplaced parenthesis.', {\n            index: _this4.nextToken[_tokenize.FIELDS.START_POS]\n          });\n        }\n      });\n    } else {\n      return this.expected(['pseudo-class', 'pseudo-element'], this.currToken[_tokenize.FIELDS.START_POS]);\n    }\n  };\n  _proto.space = function space() {\n    var content = this.content();\n    // Handle space before and after the selector\n    if (this.position === 0 || this.prevToken[_tokenize.FIELDS.TYPE] === tokens.comma || this.prevToken[_tokenize.FIELDS.TYPE] === tokens.openParenthesis || this.current.nodes.every(function (node) {\n      return node.type === 'comment';\n    })) {\n      this.spaces = this.optionalSpace(content);\n      this.position++;\n    } else if (this.position === this.tokens.length - 1 || this.nextToken[_tokenize.FIELDS.TYPE] === tokens.comma || this.nextToken[_tokenize.FIELDS.TYPE] === tokens.closeParenthesis) {\n      this.current.last.spaces.after = this.optionalSpace(content);\n      this.position++;\n    } else {\n      this.combinator();\n    }\n  };\n  _proto.string = function string() {\n    var current = this.currToken;\n    this.newNode(new _string[\"default\"]({\n      value: this.content(),\n      source: getTokenSource(current),\n      sourceIndex: current[_tokenize.FIELDS.START_POS]\n    }));\n    this.position++;\n  };\n  _proto.universal = function universal(namespace) {\n    var nextToken = this.nextToken;\n    if (nextToken && this.content(nextToken) === '|') {\n      this.position++;\n      return this.namespace();\n    }\n    var current = this.currToken;\n    this.newNode(new _universal[\"default\"]({\n      value: this.content(),\n      source: getTokenSource(current),\n      sourceIndex: current[_tokenize.FIELDS.START_POS]\n    }), namespace);\n    this.position++;\n  };\n  _proto.splitWord = function splitWord(namespace, firstCallback) {\n    var _this5 = this;\n    var nextToken = this.nextToken;\n    var word = this.content();\n    while (nextToken && ~[tokens.dollar, tokens.caret, tokens.equals, tokens.word].indexOf(nextToken[_tokenize.FIELDS.TYPE])) {\n      this.position++;\n      var current = this.content();\n      word += current;\n      if (current.lastIndexOf('\\\\') === current.length - 1) {\n        var next = this.nextToken;\n        if (next && next[_tokenize.FIELDS.TYPE] === tokens.space) {\n          word += this.requiredSpace(this.content(next));\n          this.position++;\n        }\n      }\n      nextToken = this.nextToken;\n    }\n    var hasClass = indexesOf(word, '.').filter(function (i) {\n      // Allow escaped dot within class name\n      var escapedDot = word[i - 1] === '\\\\';\n      // Allow decimal numbers percent in @keyframes\n      var isKeyframesPercent = /^\\d+\\.\\d+%$/.test(word);\n      return !escapedDot && !isKeyframesPercent;\n    });\n    var hasId = indexesOf(word, '#').filter(function (i) {\n      return word[i - 1] !== '\\\\';\n    });\n    // Eliminate Sass interpolations from the list of id indexes\n    var interpolations = indexesOf(word, '#{');\n    if (interpolations.length) {\n      hasId = hasId.filter(function (hashIndex) {\n        return !~interpolations.indexOf(hashIndex);\n      });\n    }\n    var indices = (0, _sortAscending[\"default\"])(uniqs([0].concat(hasClass, hasId)));\n    indices.forEach(function (ind, i) {\n      var index = indices[i + 1] || word.length;\n      var value = word.slice(ind, index);\n      if (i === 0 && firstCallback) {\n        return firstCallback.call(_this5, value, indices.length);\n      }\n      var node;\n      var current = _this5.currToken;\n      var sourceIndex = current[_tokenize.FIELDS.START_POS] + indices[i];\n      var source = getSource(current[1], current[2] + ind, current[3], current[2] + (index - 1));\n      if (~hasClass.indexOf(ind)) {\n        var classNameOpts = {\n          value: value.slice(1),\n          source: source,\n          sourceIndex: sourceIndex\n        };\n        node = new _className[\"default\"](unescapeProp(classNameOpts, \"value\"));\n      } else if (~hasId.indexOf(ind)) {\n        var idOpts = {\n          value: value.slice(1),\n          source: source,\n          sourceIndex: sourceIndex\n        };\n        node = new _id[\"default\"](unescapeProp(idOpts, \"value\"));\n      } else {\n        var tagOpts = {\n          value: value,\n          source: source,\n          sourceIndex: sourceIndex\n        };\n        unescapeProp(tagOpts, \"value\");\n        node = new _tag[\"default\"](tagOpts);\n      }\n      _this5.newNode(node, namespace);\n      // Ensure that the namespace is used only once\n      namespace = null;\n    });\n    this.position++;\n  };\n  _proto.word = function word(namespace) {\n    var nextToken = this.nextToken;\n    if (nextToken && this.content(nextToken) === '|') {\n      this.position++;\n      return this.namespace();\n    }\n    return this.splitWord(namespace);\n  };\n  _proto.loop = function loop() {\n    while (this.position < this.tokens.length) {\n      this.parse(true);\n    }\n    this.current._inferEndPosition();\n    return this.root;\n  };\n  _proto.parse = function parse(throwOnParenthesis) {\n    switch (this.currToken[_tokenize.FIELDS.TYPE]) {\n      case tokens.space:\n        this.space();\n        break;\n      case tokens.comment:\n        this.comment();\n        break;\n      case tokens.openParenthesis:\n        this.parentheses();\n        break;\n      case tokens.closeParenthesis:\n        if (throwOnParenthesis) {\n          this.missingParenthesis();\n        }\n        break;\n      case tokens.openSquare:\n        this.attribute();\n        break;\n      case tokens.dollar:\n      case tokens.caret:\n      case tokens.equals:\n      case tokens.word:\n        this.word();\n        break;\n      case tokens.colon:\n        this.pseudo();\n        break;\n      case tokens.comma:\n        this.comma();\n        break;\n      case tokens.asterisk:\n        this.universal();\n        break;\n      case tokens.ampersand:\n        this.nesting();\n        break;\n      case tokens.slash:\n      case tokens.combinator:\n        this.combinator();\n        break;\n      case tokens.str:\n        this.string();\n        break;\n      // These cases throw; no break needed.\n      case tokens.closeSquare:\n        this.missingSquareBracket();\n      case tokens.semicolon:\n        this.missingBackslash();\n      default:\n        this.unexpected();\n    }\n  }\n\n  /**\n   * Helpers\n   */;\n  _proto.expected = function expected(description, index, found) {\n    if (Array.isArray(description)) {\n      var last = description.pop();\n      description = description.join(', ') + \" or \" + last;\n    }\n    var an = /^[aeiou]/.test(description[0]) ? 'an' : 'a';\n    if (!found) {\n      return this.error(\"Expected \" + an + \" \" + description + \".\", {\n        index: index\n      });\n    }\n    return this.error(\"Expected \" + an + \" \" + description + \", found \\\"\" + found + \"\\\" instead.\", {\n      index: index\n    });\n  };\n  _proto.requiredSpace = function requiredSpace(space) {\n    return this.options.lossy ? ' ' : space;\n  };\n  _proto.optionalSpace = function optionalSpace(space) {\n    return this.options.lossy ? '' : space;\n  };\n  _proto.lossySpace = function lossySpace(space, required) {\n    if (this.options.lossy) {\n      return required ? ' ' : '';\n    } else {\n      return space;\n    }\n  };\n  _proto.parseParenthesisToken = function parseParenthesisToken(token) {\n    var content = this.content(token);\n    if (token[_tokenize.FIELDS.TYPE] === tokens.space) {\n      return this.requiredSpace(content);\n    } else {\n      return content;\n    }\n  };\n  _proto.newNode = function newNode(node, namespace) {\n    if (namespace) {\n      if (/^ +$/.test(namespace)) {\n        if (!this.options.lossy) {\n          this.spaces = (this.spaces || '') + namespace;\n        }\n        namespace = true;\n      }\n      node.namespace = namespace;\n      unescapeProp(node, \"namespace\");\n    }\n    if (this.spaces) {\n      node.spaces.before = this.spaces;\n      this.spaces = '';\n    }\n    return this.current.append(node);\n  };\n  _proto.content = function content(token) {\n    if (token === void 0) {\n      token = this.currToken;\n    }\n    return this.css.slice(token[_tokenize.FIELDS.START_POS], token[_tokenize.FIELDS.END_POS]);\n  };\n  /**\n   * returns the index of the next non-whitespace, non-comment token.\n   * returns -1 if no meaningful token is found.\n   */\n  _proto.locateNextMeaningfulToken = function locateNextMeaningfulToken(startPosition) {\n    if (startPosition === void 0) {\n      startPosition = this.position + 1;\n    }\n    var searchPosition = startPosition;\n    while (searchPosition < this.tokens.length) {\n      if (WHITESPACE_EQUIV_TOKENS[this.tokens[searchPosition][_tokenize.FIELDS.TYPE]]) {\n        searchPosition++;\n        continue;\n      } else {\n        return searchPosition;\n      }\n    }\n    return -1;\n  };\n  _createClass(Parser, [{\n    key: \"currToken\",\n    get: function get() {\n      return this.tokens[this.position];\n    }\n  }, {\n    key: \"nextToken\",\n    get: function get() {\n      return this.tokens[this.position + 1];\n    }\n  }, {\n    key: \"prevToken\",\n    get: function get() {\n      return this.tokens[this.position - 1];\n    }\n  }]);\n  return Parser;\n}();\nexports[\"default\"] = Parser;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,QAAQ;AACZ,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,WAAW;AACf,IAAI,MAAM;AACV,IAAI,OAAO;AACX,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,IAAI,iBAAiB;AACrB,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI;AACJ,IAAI,oBAAoB;AACxB,SAAS,yBAAyB,WAAW;IAAI,IAAI,OAAO,YAAY,YAAY,OAAO;IAAM,IAAI,oBAAoB,IAAI;IAAW,IAAI,mBAAmB,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAS,yBAAyB,WAAW;QAAI,OAAO,cAAc,mBAAmB;IAAmB,CAAC,EAAE;AAAc;AAC9U,SAAS,wBAAwB,GAAG,EAAE,WAAW;IAAI,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE;QAAE,OAAO;IAAK;IAAE,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY;QAAE,OAAO;YAAE,WAAW;QAAI;IAAG;IAAE,IAAI,QAAQ,yBAAyB;IAAc,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM;QAAE,OAAO,MAAM,GAAG,CAAC;IAAM;IAAE,IAAI,SAAS,CAAC;IAAG,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,OAAO,IAAK;QAAE,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YAAE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAAM,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG;gBAAE,OAAO,cAAc,CAAC,QAAQ,KAAK;YAAO,OAAO;gBAAE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;YAAE;QAAE;IAAE;IAAE,MAAM,CAAC,UAAU,GAAG;IAAK,IAAI,OAAO;QAAE,MAAM,GAAG,CAAC,KAAK;IAAS;IAAE,OAAO;AAAQ;AACxyB,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,SAAS,kBAAkB,MAAM,EAAE,KAAK;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QAAE,IAAI,aAAa,KAAK,CAAC,EAAE;QAAE,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI;QAAO,WAAW,YAAY,GAAG;QAAM,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG;QAAM,OAAO,cAAc,CAAC,QAAQ,WAAW,GAAG,EAAE;IAAa;AAAE;AAC5T,SAAS,aAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IAAI,IAAI,YAAY,kBAAkB,YAAY,SAAS,EAAE;IAAa,IAAI,aAAa,kBAAkB,aAAa;IAAc,OAAO,cAAc,CAAC,aAAa,aAAa;QAAE,UAAU;IAAM;IAAI,OAAO;AAAa;AAC5R,IAAI,oBAAoB,CAAC,qBAAqB,CAAC,GAAG,kBAAkB,CAAC,OAAO,KAAK,CAAC,GAAG,MAAM,kBAAkB,CAAC,OAAO,EAAE,CAAC,GAAG,MAAM,kBAAkB,CAAC,OAAO,IAAI,CAAC,GAAG,MAAM,kBAAkB,CAAC,OAAO,OAAO,CAAC,GAAG,MAAM,kBAAkB,CAAC,OAAO,GAAG,CAAC,GAAG,MAAM,kBAAkB;AAC7Q,IAAI,0BAA0B,OAAO,MAAM,CAAC,CAAC,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,GAAG,cAAc,CAAC,OAAO,OAAO,CAAC,GAAG,MAAM,cAAc;AAC9I,SAAS,WAAW,KAAK;IACvB,OAAO;QACL,MAAM,KAAK,CAAC,UAAU,MAAM,CAAC,UAAU,CAAC;QACxC,QAAQ,KAAK,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;IAC3C;AACF;AACA,SAAS,SAAS,KAAK;IACrB,OAAO;QACL,MAAM,KAAK,CAAC,UAAU,MAAM,CAAC,QAAQ,CAAC;QACtC,QAAQ,KAAK,CAAC,UAAU,MAAM,CAAC,OAAO,CAAC;IACzC;AACF;AACA,SAAS,UAAU,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS;IAC3D,OAAO;QACL,OAAO;YACL,MAAM;YACN,QAAQ;QACV;QACA,KAAK;YACH,MAAM;YACN,QAAQ;QACV;IACF;AACF;AACA,SAAS,eAAe,KAAK;IAC3B,OAAO,UAAU,KAAK,CAAC,UAAU,MAAM,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC,EAAE,KAAK,CAAC,UAAU,MAAM,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU,MAAM,CAAC,OAAO,CAAC;AAC3J;AACA,SAAS,mBAAmB,UAAU,EAAE,QAAQ;IAC9C,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IACA,OAAO,UAAU,UAAU,CAAC,UAAU,MAAM,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,UAAU,MAAM,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,UAAU,MAAM,CAAC,OAAO,CAAC;AAC3K;AACA,SAAS,aAAa,IAAI,EAAE,IAAI;IAC9B,IAAI,QAAQ,IAAI,CAAC,KAAK;IACtB,IAAI,OAAO,UAAU,UAAU;QAC7B;IACF;IACA,IAAI,MAAM,OAAO,CAAC,UAAU,CAAC,GAAG;QAC9B,CAAC,GAAG,MAAM,YAAY,EAAE,MAAM;QAC9B,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,MAAM,KAAK,EAAE;QAC9B,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,WAAW;YACjC,KAAK,IAAI,CAAC,KAAK,GAAG;QACpB;IACF;IACA,OAAO;AACT;AACA,SAAS,UAAU,KAAK,EAAE,IAAI;IAC5B,IAAI,IAAI,CAAC;IACT,IAAI,UAAU,EAAE;IAChB,MAAO,CAAC,IAAI,MAAM,OAAO,CAAC,MAAM,IAAI,EAAE,MAAM,CAAC,EAAG;QAC9C,QAAQ,IAAI,CAAC;IACf;IACA,OAAO;AACT;AACA,SAAS;IACP,IAAI,OAAO,MAAM,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;IAC5C,OAAO,KAAK,MAAM,CAAC,SAAU,IAAI,EAAE,CAAC;QAClC,OAAO,MAAM,KAAK,OAAO,CAAC;IAC5B;AACF;AACA,IAAI,SAAS,WAAW,GAAE;IACxB,SAAS,OAAO,IAAI,EAAE,OAAO;QAC3B,IAAI,YAAY,KAAK,GAAG;YACtB,UAAU,CAAC;QACb;QACA,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG,OAAO,MAAM,CAAC;YAC3B,OAAO;YACP,MAAM;QACR,GAAG;QACH,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,GAAG,GAAG,OAAO,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QACzE,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,UAAU,EAAE;YACtC,KAAK,IAAI,CAAC,GAAG;YACb,OAAO,IAAI,CAAC,eAAe;YAC3B,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI;QACzB;QACA,IAAI,aAAa,mBAAmB,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE;QACvF,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC;YAC/B,QAAQ;QACV;QACA,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe;QAC/C,IAAI,WAAW,IAAI,SAAS,CAAC,UAAU,CAAC;YACtC,QAAQ;gBACN,OAAO;oBACL,MAAM;oBACN,QAAQ;gBACV;YACF;YACA,aAAa;QACf;QACA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI;IACX;IACA,IAAI,SAAS,OAAO,SAAS;IAC7B,OAAO,eAAe,GAAG,SAAS;QAChC,IAAI,QAAQ,IAAI;QAChB,OAAO,SAAU,OAAO,EAAE,YAAY;YACpC,IAAI,OAAO,MAAM,IAAI,KAAK,UAAU;gBAClC,OAAO,IAAI,MAAM;YACnB;YACA,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS;QACnC;IACF;IACA,OAAO,SAAS,GAAG,SAAS;QAC1B,IAAI,OAAO,EAAE;QACb,IAAI,gBAAgB,IAAI,CAAC,SAAS;QAClC,IAAI,CAAC,QAAQ;QACb,MAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,WAAW,CAAE;YACzG,KAAK,IAAI,CAAC,IAAI,CAAC,SAAS;YACxB,IAAI,CAAC,QAAQ;QACf;QACA,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,WAAW,EAAE;YAChE,OAAO,IAAI,CAAC,QAAQ,CAAC,0BAA0B,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;QAC3F;QACA,IAAI,MAAM,KAAK,MAAM;QACrB,IAAI,OAAO;YACT,QAAQ,UAAU,aAAa,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE;YAC1F,aAAa,aAAa,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;QACxD;QACA,IAAI,QAAQ,KAAK,CAAC,CAAC;YAAC,OAAO,IAAI;SAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,GAAG;YACxE,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,CAAC,EAAE,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;QACvE;QACA,IAAI,MAAM;QACV,IAAI,cAAc;QAClB,IAAI,gBAAgB;QACpB,IAAI,YAAY;QAChB,IAAI,4BAA4B;QAChC,MAAO,MAAM,IAAK;YAChB,IAAI,QAAQ,IAAI,CAAC,IAAI;YACrB,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC;YAC3B,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;YACxB,OAAQ,KAAK,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC;gBAClC,KAAK,OAAO,KAAK;oBACf,OAAO;oBACP,mBAAmB;oBACnB,8CAA8C;oBAC9C,MAAM;oBACN,0EAA0E;oBAC1E,IAAI;oBACJ,4BAA4B;oBAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;wBACtB;oBACF;oBACA,IAAI,WAAW;wBACb,CAAC,GAAG,MAAM,YAAY,EAAE,MAAM,UAAU;wBACxC,IAAI,cAAc,KAAK,MAAM,CAAC,UAAU,CAAC,KAAK,IAAI;wBAClD,KAAK,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,cAAc;wBAC7C,IAAI,kBAAkB,CAAC,GAAG,MAAM,OAAO,EAAE,MAAM,QAAQ,UAAU,WAAW,YAAY;wBACxF,IAAI,iBAAiB;4BACnB,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,kBAAkB;wBACxD;oBACF,OAAO;wBACL,cAAc,cAAc;wBAC5B,gBAAgB,gBAAgB;oBAClC;oBACA;gBACF,KAAK,OAAO,QAAQ;oBAClB,IAAI,IAAI,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,MAAM,EAAE;wBACjD,KAAK,QAAQ,GAAG;wBAChB,YAAY;oBACd,OAAO,IAAI,CAAC,CAAC,KAAK,SAAS,IAAI,cAAc,eAAe,CAAC,yBAAyB,KAAK,MAAM;wBAC/F,IAAI,aAAa;4BACf,CAAC,GAAG,MAAM,YAAY,EAAE,MAAM,UAAU;4BACxC,KAAK,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG;4BAC/B,cAAc;wBAChB;wBACA,IAAI,eAAe;4BACjB,CAAC,GAAG,MAAM,YAAY,EAAE,MAAM,QAAQ,UAAU;4BAChD,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG;4BACpC,gBAAgB;wBAClB;wBACA,KAAK,SAAS,GAAG,CAAC,KAAK,SAAS,IAAI,EAAE,IAAI;wBAC1C,IAAI,WAAW,CAAC,GAAG,MAAM,OAAO,EAAE,MAAM,QAAQ,gBAAgB;wBAChE,IAAI,UAAU;4BACZ,KAAK,IAAI,CAAC,SAAS,IAAI;wBACzB;wBACA,YAAY;oBACd;oBACA,4BAA4B;oBAC5B;gBACF,KAAK,OAAO,MAAM;oBAChB,IAAI,cAAc,SAAS;wBACzB,IAAI,cAAc,CAAC,GAAG,MAAM,OAAO,EAAE,MAAM,QAAQ;wBACnD,KAAK,KAAK,IAAI;wBACd,IAAI,aAAa;4BACf,KAAK,IAAI,CAAC,KAAK,GAAG,cAAc;wBAClC;wBACA;oBACF;gBACF,gBAAgB;gBAChB,KAAK,OAAO,KAAK;oBACf,IAAI,IAAI,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,MAAM,EAAE;wBACjD,KAAK,QAAQ,GAAG;wBAChB,YAAY;oBACd;oBACA,4BAA4B;oBAC5B;gBACF,KAAK,OAAO,UAAU;oBACpB,IAAI,YAAY,OAAO,IAAI,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,MAAM,EAAE;wBACpE,KAAK,QAAQ,GAAG;wBAChB,YAAY;oBACd;oBACA,IAAI,YAAY,KAAK;wBACnB,4BAA4B;wBAC5B;oBACF;oBACA,IAAI,IAAI,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,MAAM,EAAE;wBACjD,KAAK,QAAQ,GAAG;wBAChB,YAAY;oBACd,OAAO,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,EAAE;wBAC7C,KAAK,SAAS,GAAG;oBACnB;oBACA,4BAA4B;oBAC5B;gBACF,KAAK,OAAO,IAAI;oBACd,IAAI,QAAQ,IAAI,CAAC,OAAO,CAAC,UAAU,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,MAAM,IACjH,8DAA8D;oBAC9D,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,SAAS,EAAE;wBACjC,KAAK,SAAS,GAAG;wBACjB,YAAY;oBACd,OAAO,IAAI,CAAC,KAAK,SAAS,IAAI,cAAc,eAAe,CAAC,2BAA2B;wBACrF,IAAI,aAAa;4BACf,CAAC,GAAG,MAAM,YAAY,EAAE,MAAM,UAAU;4BACxC,KAAK,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG;4BAC/B,cAAc;wBAChB;wBACA,IAAI,eAAe;4BACjB,CAAC,GAAG,MAAM,YAAY,EAAE,MAAM,QAAQ,UAAU;4BAChD,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG;4BACpC,gBAAgB;wBAClB;wBACA,KAAK,SAAS,GAAG,CAAC,KAAK,SAAS,IAAI,EAAE,IAAI;wBAC1C,IAAI,YAAY,CAAC,GAAG,MAAM,OAAO,EAAE,MAAM,QAAQ,gBAAgB;wBACjE,IAAI,WAAW;4BACb,KAAK,IAAI,CAAC,SAAS,IAAI;wBACzB;wBACA,YAAY;oBACd,OAAO,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,cAAc,WAAW,CAAC,CAAC,6BAA6B,KAAK,SAAS,GAAG;wBACtH,IAAI,aAAa,CAAC,GAAG,MAAM,KAAK,EAAE;wBAClC,IAAI,eAAe,CAAC,GAAG,MAAM,OAAO,EAAE,MAAM,QAAQ,YAAY;wBAChE,IAAI,WAAW,KAAK,KAAK,IAAI;wBAC7B,KAAK,KAAK,GAAG,WAAW;wBACxB,KAAK,SAAS,GAAG;wBACjB,IAAI,eAAe,WAAW,cAAc;4BAC1C,CAAC,GAAG,MAAM,YAAY,EAAE,MAAM;4BAC9B,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,gBAAgB,QAAQ,IAAI;wBACjD;wBACA,YAAY;oBACd,OAAO;wBACL,IAAI,cAAc,YAAY,OAAO,YAAY;wBACjD,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,EAAE,KAAK,CAAC,KAAK,SAAS,IAAI,yBAAyB,GAAG;4BACtF,KAAK,WAAW,GAAG;4BACnB,IAAI,CAAC,eAAe,YAAY,KAAK;gCACnC,CAAC,GAAG,MAAM,YAAY,EAAE,MAAM;gCAC9B,KAAK,IAAI,CAAC,eAAe,GAAG;4BAC9B;4BACA,YAAY;4BACZ,IAAI,aAAa;gCACf,CAAC,GAAG,MAAM,YAAY,EAAE,MAAM,UAAU;gCACxC,KAAK,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG;gCACjC,cAAc;4BAChB;4BACA,IAAI,eAAe;gCACjB,CAAC,GAAG,MAAM,YAAY,EAAE,MAAM,QAAQ,UAAU;gCAChD,KAAK,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG;gCACtC,gBAAgB;4BAClB;wBACF,OAAO,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI;4BAC1C,YAAY;4BACZ,KAAK,KAAK,IAAI;4BACd,IAAI,KAAK,IAAI,CAAC,KAAK,EAAE;gCACnB,KAAK,IAAI,CAAC,KAAK,IAAI;4BACrB;wBACF;oBACF;oBACA,4BAA4B;oBAC5B;gBACF,KAAK,OAAO,GAAG;oBACb,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,QAAQ,EAAE;wBACrC,OAAO,IAAI,CAAC,KAAK,CAAC,uEAAuE;4BACvF,OAAO,KAAK,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;wBAC1C;oBACF;oBACA,IAAI,iBAAiB,CAAC,GAAG,WAAW,aAAa,EAAE,UACjD,YAAY,eAAe,SAAS,EACpC,YAAY,eAAe,SAAS;oBACtC,KAAK,KAAK,GAAG;oBACb,KAAK,SAAS,GAAG;oBACjB,YAAY;oBACZ,CAAC,GAAG,MAAM,YAAY,EAAE,MAAM;oBAC9B,KAAK,IAAI,CAAC,KAAK,GAAG;oBAClB,4BAA4B;oBAC5B;gBACF,KAAK,OAAO,MAAM;oBAChB,IAAI,CAAC,KAAK,SAAS,EAAE;wBACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,KAAK,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC,EAAE;oBACvE;oBACA,IAAI,KAAK,KAAK,EAAE;wBACd,OAAO,IAAI,CAAC,KAAK,CAAC,0DAA0D;4BAC1E,OAAO,KAAK,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;wBAC1C;oBACF;oBACA,KAAK,QAAQ,GAAG,KAAK,QAAQ,GAAG,KAAK,QAAQ,GAAG,UAAU;oBAC1D,YAAY;oBACZ,4BAA4B;oBAC5B;gBACF,KAAK,OAAO,OAAO;oBACjB,IAAI,WAAW;wBACb,IAAI,6BAA6B,QAAQ,IAAI,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,KAAK,IAAI,cAAc,eAAe;4BACpH,IAAI,cAAc,CAAC,GAAG,MAAM,OAAO,EAAE,MAAM,UAAU,WAAW,YAAY;4BAC5E,IAAI,iBAAiB,CAAC,GAAG,MAAM,OAAO,EAAE,MAAM,QAAQ,UAAU,WAAW,YAAY;4BACvF,CAAC,GAAG,MAAM,YAAY,EAAE,MAAM,QAAQ,UAAU;4BAChD,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,iBAAiB;wBACvD,OAAO;4BACL,IAAI,YAAY,IAAI,CAAC,UAAU,IAAI;4BACnC,IAAI,eAAe,CAAC,GAAG,MAAM,OAAO,EAAE,MAAM,QAAQ,cAAc;4BAClE,CAAC,GAAG,MAAM,YAAY,EAAE,MAAM;4BAC9B,KAAK,IAAI,CAAC,UAAU,GAAG,eAAe;wBACxC;oBACF,OAAO;wBACL,gBAAgB,gBAAgB;oBAClC;oBACA;gBACF;oBACE,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,UAAU,aAAa;wBACzD,OAAO,KAAK,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;oBAC1C;YACJ;YACA;QACF;QACA,aAAa,MAAM;QACnB,aAAa,MAAM;QACnB,IAAI,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC;QACvC,IAAI,CAAC,QAAQ;IACf;IAcA,OAAO,+BAA+B,GAAG,SAAS,gCAAgC,YAAY;QAC5F,IAAI,eAAe,GAAG;YACpB,eAAe,IAAI,CAAC,MAAM,CAAC,MAAM;QACnC;QACA,IAAI,gBAAgB,IAAI,CAAC,QAAQ;QACjC,IAAI,QAAQ,EAAE;QACd,IAAI,QAAQ;QACZ,IAAI,cAAc;QAClB,GAAG;YACD,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;gBAC5D,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;oBACvB,SAAS,IAAI,CAAC,OAAO;gBACvB;YACF,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,OAAO,EAAE;gBACnE,IAAI,SAAS,CAAC;gBACd,IAAI,OAAO;oBACT,OAAO,MAAM,GAAG;oBAChB,QAAQ;gBACV;gBACA,cAAc,IAAI,QAAQ,CAAC,UAAU,CAAC;oBACpC,OAAO,IAAI,CAAC,OAAO;oBACnB,QAAQ,eAAe,IAAI,CAAC,SAAS;oBACrC,aAAa,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;oBACvD,QAAQ;gBACV;gBACA,MAAM,IAAI,CAAC;YACb;QACF,QAAS,EAAE,IAAI,CAAC,QAAQ,GAAG,aAAc;QACzC,IAAI,OAAO;YACT,IAAI,aAAa;gBACf,YAAY,MAAM,CAAC,KAAK,GAAG;YAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;gBAC9B,IAAI,aAAa,IAAI,CAAC,MAAM,CAAC,cAAc;gBAC3C,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE;gBAC9C,MAAM,IAAI,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC;oBAChC,OAAO;oBACP,QAAQ,UAAU,UAAU,CAAC,UAAU,MAAM,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,UAAU,MAAM,CAAC,QAAQ,CAAC,EAAE,SAAS,CAAC,UAAU,MAAM,CAAC,OAAO,CAAC;oBAC5K,aAAa,UAAU,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;oBACnD,QAAQ;wBACN,QAAQ;wBACR,OAAO;oBACT;gBACF;YACF;QACF;QACA,OAAO;IACT;IAMA,OAAO,6BAA6B,GAAG,SAAS,8BAA8B,KAAK,EAAE,aAAa;QAChG,IAAI,SAAS,IAAI;QACjB,IAAI,kBAAkB,KAAK,GAAG;YAC5B,gBAAgB;QAClB;QACA,IAAI,QAAQ;QACZ,IAAI,WAAW;QACf,MAAM,OAAO,CAAC,SAAU,CAAC;YACvB,IAAI,cAAc,OAAO,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE;YACrD,IAAI,iBAAiB,OAAO,UAAU,CAAC,EAAE,cAAc,EAAE;YACzD,SAAS,cAAc,OAAO,UAAU,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,iBAAiB,YAAY,MAAM,KAAK;YACjG,YAAY,cAAc,EAAE,KAAK,GAAG,OAAO,UAAU,CAAC,EAAE,aAAa,EAAE,iBAAiB,eAAe,MAAM,KAAK;QACpH;QACA,IAAI,aAAa,OAAO;YACtB,WAAW;QACb;QACA,IAAI,SAAS;YACX,OAAO;YACP,UAAU;QACZ;QACA,OAAO;IACT;IACA,OAAO,iBAAiB,GAAG,SAAS,kBAAkB,QAAQ;QAC5D,IAAI,aAAa,KAAK,GAAG;YACvB,WAAW,IAAI,CAAC,QAAQ;QAC1B;QACA,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,KAAK;IAC1S;IACA,OAAO,eAAe,GAAG,SAAS;QAChC,IAAI,IAAI,CAAC,iBAAiB,IAAI;YAC5B,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE;YACzD,IAAI,OAAO,CAAC,GAAG,MAAM,KAAK,EAAE,SAAS,WAAW;YAChD,IAAI,OAAO,CAAC;YACZ,IAAI,SAAS,SAAS;gBACpB,KAAK,KAAK,GAAG,MAAM,UAAU;YAC/B;YACA,IAAI,OAAO,IAAI,WAAW,CAAC,UAAU,CAAC;gBACpC,OAAO,MAAM,OAAO;gBACpB,QAAQ,UAAU,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC,UAAU,MAAM,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC,UAAU,MAAM,CAAC,OAAO,CAAC;gBAC9N,aAAa,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;gBACvD,MAAM;YACR;YACA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG;YAChC,OAAO;QACT,OAAO;YACL,IAAI,CAAC,UAAU;QACjB;IACF;IACA,OAAO,UAAU,GAAG,SAAS;QAC3B,IAAI,SAAS,IAAI;QACjB,IAAI,IAAI,CAAC,OAAO,OAAO,KAAK;YAC1B,OAAO,IAAI,CAAC,SAAS;QACvB;QACA,wHAAwH;QACxH,IAAI,kBAAkB,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ;QAClE,IAAI,kBAAkB,KAAK,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,gBAAgB,EAAE;YAClL,IAAI,QAAQ,IAAI,CAAC,+BAA+B,CAAC;YACjD,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI;gBAC5B,IAAI,MAAM;oBACR,IAAI,wBAAwB,IAAI,CAAC,6BAA6B,CAAC,QAC7D,QAAQ,sBAAsB,KAAK,EACnC,WAAW,sBAAsB,QAAQ;oBAC3C,IAAI,aAAa,WAAW;wBAC1B,KAAK,aAAa,IAAI;oBACxB;oBACA,KAAK,MAAM,CAAC,KAAK,IAAI;gBACvB,OAAO;oBACL,MAAM,OAAO,CAAC,SAAU,CAAC;wBACvB,OAAO,OAAO,OAAO,CAAC;oBACxB;gBACF;YACF;YACA;QACF;QACA,IAAI,aAAa,IAAI,CAAC,SAAS;QAC/B,IAAI,iCAAiC;QACrC,IAAI,kBAAkB,IAAI,CAAC,QAAQ,EAAE;YACnC,iCAAiC,IAAI,CAAC,+BAA+B,CAAC;QACxE;QACA,IAAI;QACJ,IAAI,IAAI,CAAC,iBAAiB,IAAI;YAC5B,OAAO,IAAI,CAAC,eAAe;QAC7B,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,UAAU,EAAE;YACtE,OAAO,IAAI,WAAW,CAAC,UAAU,CAAC;gBAChC,OAAO,IAAI,CAAC,OAAO;gBACnB,QAAQ,eAAe,IAAI,CAAC,SAAS;gBACrC,aAAa,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;YACzD;YACA,IAAI,CAAC,QAAQ;QACf,OAAO,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;QACnE,OAAO;QACT,OAAO,IAAI,CAAC,gCAAgC;YAC1C,IAAI,CAAC,UAAU;QACjB;QACA,IAAI,MAAM;YACR,IAAI,gCAAgC;gBAClC,IAAI,yBAAyB,IAAI,CAAC,6BAA6B,CAAC,iCAC9D,SAAS,uBAAuB,KAAK,EACrC,YAAY,uBAAuB,QAAQ;gBAC7C,KAAK,MAAM,CAAC,MAAM,GAAG;gBACrB,KAAK,cAAc,GAAG;YACxB;QACF,OAAO;YACL,wBAAwB;YACxB,IAAI,yBAAyB,IAAI,CAAC,6BAA6B,CAAC,gCAAgC,OAC9F,UAAU,uBAAuB,KAAK,EACtC,aAAa,uBAAuB,QAAQ;YAC9C,IAAI,CAAC,YAAY;gBACf,aAAa;YACf;YACA,IAAI,SAAS,CAAC;YACd,IAAI,OAAO;gBACT,QAAQ,CAAC;YACX;YACA,IAAI,QAAQ,QAAQ,CAAC,QAAQ,WAAW,QAAQ,CAAC,MAAM;gBACrD,OAAO,MAAM,GAAG,QAAQ,KAAK,CAAC,GAAG,QAAQ,MAAM,GAAG;gBAClD,KAAK,MAAM,CAAC,MAAM,GAAG,WAAW,KAAK,CAAC,GAAG,WAAW,MAAM,GAAG;YAC/D,OAAO,IAAI,QAAQ,UAAU,CAAC,QAAQ,WAAW,UAAU,CAAC,MAAM;gBAChE,OAAO,KAAK,GAAG,QAAQ,KAAK,CAAC;gBAC7B,KAAK,MAAM,CAAC,KAAK,GAAG,WAAW,KAAK,CAAC;YACvC,OAAO;gBACL,KAAK,KAAK,GAAG;YACf;YACA,OAAO,IAAI,WAAW,CAAC,UAAU,CAAC;gBAChC,OAAO;gBACP,QAAQ,mBAAmB,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE;gBACrE,aAAa,UAAU,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;gBACnD,QAAQ;gBACR,MAAM;YACR;QACF;QACA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,KAAK,EAAE;YAC5E,KAAK,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO;YACnD,IAAI,CAAC,QAAQ;QACf;QACA,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IACA,OAAO,KAAK,GAAG,SAAS;QACtB,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG;YAC5C,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG;YAC1B,IAAI,CAAC,QAAQ;YACb;QACF;QACA,IAAI,CAAC,OAAO,CAAC,iBAAiB;QAC9B,IAAI,WAAW,IAAI,SAAS,CAAC,UAAU,CAAC;YACtC,QAAQ;gBACN,OAAO,WAAW,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE;YAClD;YACA,aAAa,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;QACzE;QACA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ;IACf;IACA,OAAO,OAAO,GAAG,SAAS;QACxB,IAAI,UAAU,IAAI,CAAC,SAAS;QAC5B,IAAI,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,UAAU,CAAC;YACnC,OAAO,IAAI,CAAC,OAAO;YACnB,QAAQ,eAAe;YACvB,aAAa,OAAO,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;QAClD;QACA,IAAI,CAAC,QAAQ;IACf;IACA,OAAO,KAAK,GAAG,SAAS,MAAM,OAAO,EAAE,IAAI;QACzC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS;IACjC;IACA,OAAO,gBAAgB,GAAG,SAAS;QACjC,OAAO,IAAI,CAAC,KAAK,CAAC,iDAAiD;YACjE,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;QACnD;IACF;IACA,OAAO,kBAAkB,GAAG,SAAS;QACnC,OAAO,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;IACxF;IACA,OAAO,oBAAoB,GAAG,SAAS;QACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,0BAA0B,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;IAC3F;IACA,OAAO,UAAU,GAAG,SAAS;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,IAAI,CAAC,OAAO,KAAK,oDAAoD,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;IACpJ;IACA,OAAO,cAAc,GAAG,SAAS;QAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;IACjF;IACA,OAAO,SAAS,GAAG,SAAS;QAC1B,IAAI,SAAS,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,KAAK;QAC/D,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,IAAI,EAAE;YACzD,IAAI,CAAC,QAAQ;YACb,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,QAAQ,EAAE;YACpE,IAAI,CAAC,QAAQ;YACb,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB;QACA,IAAI,CAAC,cAAc;IACrB;IACA,OAAO,OAAO,GAAG,SAAS;QACxB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,cAAc,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS;YAC7C,IAAI,gBAAgB,KAAK;gBACvB,IAAI,CAAC,QAAQ;gBACb;YACF;QACF;QACA,IAAI,UAAU,IAAI,CAAC,SAAS;QAC5B,IAAI,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,UAAU,CAAC;YACnC,OAAO,IAAI,CAAC,OAAO;YACnB,QAAQ,eAAe;YACvB,aAAa,OAAO,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;QAClD;QACA,IAAI,CAAC,QAAQ;IACf;IACA,OAAO,WAAW,GAAG,SAAS;QAC5B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI;QAC5B,IAAI,aAAa;QACjB,IAAI,CAAC,QAAQ;QACb,IAAI,QAAQ,KAAK,IAAI,KAAK,MAAM,MAAM,EAAE;YACtC,IAAI,WAAW,IAAI,SAAS,CAAC,UAAU,CAAC;gBACtC,QAAQ;oBACN,OAAO,WAAW,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC9C;gBACA,aAAa,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;YACrE;YACA,IAAI,QAAQ,IAAI,CAAC,OAAO;YACxB,KAAK,MAAM,CAAC;YACZ,IAAI,CAAC,OAAO,GAAG;YACf,MAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,WAAY;gBACvD,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,eAAe,EAAE;oBACpE;gBACF;gBACA,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,gBAAgB,EAAE;oBACrE;gBACF;gBACA,IAAI,YAAY;oBACd,IAAI,CAAC,KAAK;gBACZ,OAAO;oBACL,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,GAAG,SAAS,IAAI,CAAC,SAAS;oBACjD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,SAAS,IAAI,CAAC,SAAS;oBACxD,IAAI,CAAC,QAAQ;gBACf;YACF;YACA,IAAI,CAAC,OAAO,GAAG;QACjB,OAAO;YACL,8FAA8F;YAC9F,sCAAsC;YACtC,IAAI,aAAa,IAAI,CAAC,SAAS;YAC/B,IAAI,aAAa;YACjB,IAAI;YACJ,MAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,WAAY;gBACvD,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,eAAe,EAAE;oBACpE;gBACF;gBACA,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,gBAAgB,EAAE;oBACrE;gBACF;gBACA,WAAW,IAAI,CAAC,SAAS;gBACzB,cAAc,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS;gBACvD,IAAI,CAAC,QAAQ;YACf;YACA,IAAI,MAAM;gBACR,KAAK,yBAAyB,CAAC,SAAS,YAAY;YACtD,OAAO;gBACL,IAAI,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC;oBAClC,OAAO;oBACP,QAAQ,UAAU,UAAU,CAAC,UAAU,MAAM,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,UAAU,MAAM,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,UAAU,MAAM,CAAC,OAAO,CAAC;oBAC1K,aAAa,UAAU,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;gBACrD;YACF;QACF;QACA,IAAI,YAAY;YACd,OAAO,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;QACxF;IACF;IACA,OAAO,MAAM,GAAG,SAAS;QACvB,IAAI,SAAS,IAAI;QACjB,IAAI,YAAY;QAChB,IAAI,gBAAgB,IAAI,CAAC,SAAS;QAClC,MAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,KAAK,CAAE;YAC/E,aAAa,IAAI,CAAC,OAAO;YACzB,IAAI,CAAC,QAAQ;QACf;QACA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,OAAO,IAAI,CAAC,QAAQ,CAAC;gBAAC;gBAAgB;aAAiB,EAAE,IAAI,CAAC,QAAQ,GAAG;QAC3E;QACA,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,IAAI,EAAE;YACzD,IAAI,CAAC,SAAS,CAAC,OAAO,SAAU,KAAK,EAAE,MAAM;gBAC3C,aAAa;gBACb,OAAO,OAAO,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC;oBACpC,OAAO;oBACP,QAAQ,mBAAmB,eAAe,OAAO,SAAS;oBAC1D,aAAa,aAAa,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;gBACxD;gBACA,IAAI,SAAS,KAAK,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,eAAe,EAAE;oBACxG,OAAO,KAAK,CAAC,0BAA0B;wBACrC,OAAO,OAAO,SAAS,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;oBACrD;gBACF;YACF;QACF,OAAO;YACL,OAAO,IAAI,CAAC,QAAQ,CAAC;gBAAC;gBAAgB;aAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;QACrG;IACF;IACA,OAAO,KAAK,GAAG,SAAS;QACtB,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1B,6CAA6C;QAC7C,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,eAAe,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,SAAU,IAAI;YAC9L,OAAO,KAAK,IAAI,KAAK;QACvB,IAAI;YACF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC;YACjC,IAAI,CAAC,QAAQ;QACf,OAAO,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,gBAAgB,EAAE;YAClL,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;YACpD,IAAI,CAAC,QAAQ;QACf,OAAO;YACL,IAAI,CAAC,UAAU;QACjB;IACF;IACA,OAAO,MAAM,GAAG,SAAS;QACvB,IAAI,UAAU,IAAI,CAAC,SAAS;QAC5B,IAAI,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC;YAClC,OAAO,IAAI,CAAC,OAAO;YACnB,QAAQ,eAAe;YACvB,aAAa,OAAO,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;QAClD;QACA,IAAI,CAAC,QAAQ;IACf;IACA,OAAO,SAAS,GAAG,SAAS,UAAU,SAAS;QAC7C,IAAI,YAAY,IAAI,CAAC,SAAS;QAC9B,IAAI,aAAa,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK;YAChD,IAAI,CAAC,QAAQ;YACb,OAAO,IAAI,CAAC,SAAS;QACvB;QACA,IAAI,UAAU,IAAI,CAAC,SAAS;QAC5B,IAAI,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC;YACrC,OAAO,IAAI,CAAC,OAAO;YACnB,QAAQ,eAAe;YACvB,aAAa,OAAO,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC;QAClD,IAAI;QACJ,IAAI,CAAC,QAAQ;IACf;IACA,OAAO,SAAS,GAAG,SAAS,UAAU,SAAS,EAAE,aAAa;QAC5D,IAAI,SAAS,IAAI;QACjB,IAAI,YAAY,IAAI,CAAC,SAAS;QAC9B,IAAI,OAAO,IAAI,CAAC,OAAO;QACvB,MAAO,aAAa,CAAC;YAAC,OAAO,MAAM;YAAE,OAAO,KAAK;YAAE,OAAO,MAAM;YAAE,OAAO,IAAI;SAAC,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,EAAG;YACxH,IAAI,CAAC,QAAQ;YACb,IAAI,UAAU,IAAI,CAAC,OAAO;YAC1B,QAAQ;YACR,IAAI,QAAQ,WAAW,CAAC,UAAU,QAAQ,MAAM,GAAG,GAAG;gBACpD,IAAI,OAAO,IAAI,CAAC,SAAS;gBACzB,IAAI,QAAQ,IAAI,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,KAAK,EAAE;oBACxD,QAAQ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;oBACxC,IAAI,CAAC,QAAQ;gBACf;YACF;YACA,YAAY,IAAI,CAAC,SAAS;QAC5B;QACA,IAAI,WAAW,UAAU,MAAM,KAAK,MAAM,CAAC,SAAU,CAAC;YACpD,sCAAsC;YACtC,IAAI,aAAa,IAAI,CAAC,IAAI,EAAE,KAAK;YACjC,8CAA8C;YAC9C,IAAI,qBAAqB,cAAc,IAAI,CAAC;YAC5C,OAAO,CAAC,cAAc,CAAC;QACzB;QACA,IAAI,QAAQ,UAAU,MAAM,KAAK,MAAM,CAAC,SAAU,CAAC;YACjD,OAAO,IAAI,CAAC,IAAI,EAAE,KAAK;QACzB;QACA,4DAA4D;QAC5D,IAAI,iBAAiB,UAAU,MAAM;QACrC,IAAI,eAAe,MAAM,EAAE;YACzB,QAAQ,MAAM,MAAM,CAAC,SAAU,SAAS;gBACtC,OAAO,CAAC,CAAC,eAAe,OAAO,CAAC;YAClC;QACF;QACA,IAAI,UAAU,CAAC,GAAG,cAAc,CAAC,UAAU,EAAE,MAAM;YAAC;SAAE,CAAC,MAAM,CAAC,UAAU;QACxE,QAAQ,OAAO,CAAC,SAAU,GAAG,EAAE,CAAC;YAC9B,IAAI,QAAQ,OAAO,CAAC,IAAI,EAAE,IAAI,KAAK,MAAM;YACzC,IAAI,QAAQ,KAAK,KAAK,CAAC,KAAK;YAC5B,IAAI,MAAM,KAAK,eAAe;gBAC5B,OAAO,cAAc,IAAI,CAAC,QAAQ,OAAO,QAAQ,MAAM;YACzD;YACA,IAAI;YACJ,IAAI,UAAU,OAAO,SAAS;YAC9B,IAAI,cAAc,OAAO,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,EAAE;YAClE,IAAI,SAAS,UAAU,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,GAAG,KAAK,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC;YACxF,IAAI,CAAC,SAAS,OAAO,CAAC,MAAM;gBAC1B,IAAI,gBAAgB;oBAClB,OAAO,MAAM,KAAK,CAAC;oBACnB,QAAQ;oBACR,aAAa;gBACf;gBACA,OAAO,IAAI,UAAU,CAAC,UAAU,CAAC,aAAa,eAAe;YAC/D,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,MAAM;gBAC9B,IAAI,SAAS;oBACX,OAAO,MAAM,KAAK,CAAC;oBACnB,QAAQ;oBACR,aAAa;gBACf;gBACA,OAAO,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,QAAQ;YACjD,OAAO;gBACL,IAAI,UAAU;oBACZ,OAAO;oBACP,QAAQ;oBACR,aAAa;gBACf;gBACA,aAAa,SAAS;gBACtB,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC;YAC7B;YACA,OAAO,OAAO,CAAC,MAAM;YACrB,8CAA8C;YAC9C,YAAY;QACd;QACA,IAAI,CAAC,QAAQ;IACf;IACA,OAAO,IAAI,GAAG,SAAS,KAAK,SAAS;QACnC,IAAI,YAAY,IAAI,CAAC,SAAS;QAC9B,IAAI,aAAa,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK;YAChD,IAAI,CAAC,QAAQ;YACb,OAAO,IAAI,CAAC,SAAS;QACvB;QACA,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB;IACA,OAAO,IAAI,GAAG,SAAS;QACrB,MAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAE;YACzC,IAAI,CAAC,KAAK,CAAC;QACb;QACA,IAAI,CAAC,OAAO,CAAC,iBAAiB;QAC9B,OAAO,IAAI,CAAC,IAAI;IAClB;IACA,OAAO,KAAK,GAAG,SAAS,MAAM,kBAAkB;QAC9C,OAAQ,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC;YAC3C,KAAK,OAAO,KAAK;gBACf,IAAI,CAAC,KAAK;gBACV;YACF,KAAK,OAAO,OAAO;gBACjB,IAAI,CAAC,OAAO;gBACZ;YACF,KAAK,OAAO,eAAe;gBACzB,IAAI,CAAC,WAAW;gBAChB;YACF,KAAK,OAAO,gBAAgB;gBAC1B,IAAI,oBAAoB;oBACtB,IAAI,CAAC,kBAAkB;gBACzB;gBACA;YACF,KAAK,OAAO,UAAU;gBACpB,IAAI,CAAC,SAAS;gBACd;YACF,KAAK,OAAO,MAAM;YAClB,KAAK,OAAO,KAAK;YACjB,KAAK,OAAO,MAAM;YAClB,KAAK,OAAO,IAAI;gBACd,IAAI,CAAC,IAAI;gBACT;YACF,KAAK,OAAO,KAAK;gBACf,IAAI,CAAC,MAAM;gBACX;YACF,KAAK,OAAO,KAAK;gBACf,IAAI,CAAC,KAAK;gBACV;YACF,KAAK,OAAO,QAAQ;gBAClB,IAAI,CAAC,SAAS;gBACd;YACF,KAAK,OAAO,SAAS;gBACnB,IAAI,CAAC,OAAO;gBACZ;YACF,KAAK,OAAO,KAAK;YACjB,KAAK,OAAO,UAAU;gBACpB,IAAI,CAAC,UAAU;gBACf;YACF,KAAK,OAAO,GAAG;gBACb,IAAI,CAAC,MAAM;gBACX;YACF,sCAAsC;YACtC,KAAK,OAAO,WAAW;gBACrB,IAAI,CAAC,oBAAoB;YAC3B,KAAK,OAAO,SAAS;gBACnB,IAAI,CAAC,gBAAgB;YACvB;gBACE,IAAI,CAAC,UAAU;QACnB;IACF;IAKA,OAAO,QAAQ,GAAG,SAAS,SAAS,WAAW,EAAE,KAAK,EAAE,KAAK;QAC3D,IAAI,MAAM,OAAO,CAAC,cAAc;YAC9B,IAAI,OAAO,YAAY,GAAG;YAC1B,cAAc,YAAY,IAAI,CAAC,QAAQ,SAAS;QAClD;QACA,IAAI,KAAK,WAAW,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,OAAO;QAClD,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,KAAK,MAAM,cAAc,KAAK;gBAC5D,OAAO;YACT;QACF;QACA,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,KAAK,MAAM,cAAc,eAAe,QAAQ,eAAe;YAC7F,OAAO;QACT;IACF;IACA,OAAO,aAAa,GAAG,SAAS,cAAc,KAAK;QACjD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,MAAM;IACpC;IACA,OAAO,aAAa,GAAG,SAAS,cAAc,KAAK;QACjD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK;IACnC;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,KAAK,EAAE,QAAQ;QACrD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACtB,OAAO,WAAW,MAAM;QAC1B,OAAO;YACL,OAAO;QACT;IACF;IACA,OAAO,qBAAqB,GAAG,SAAS,sBAAsB,KAAK;QACjE,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC;QAC3B,IAAI,KAAK,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,KAAK,EAAE;YACjD,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,OAAO;YACL,OAAO;QACT;IACF;IACA,OAAO,OAAO,GAAG,SAAS,QAAQ,IAAI,EAAE,SAAS;QAC/C,IAAI,WAAW;YACb,IAAI,OAAO,IAAI,CAAC,YAAY;gBAC1B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;oBACvB,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,IAAI;gBACtC;gBACA,YAAY;YACd;YACA,KAAK,SAAS,GAAG;YACjB,aAAa,MAAM;QACrB;QACA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,KAAK,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;YAChC,IAAI,CAAC,MAAM,GAAG;QAChB;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IAC7B;IACA,OAAO,OAAO,GAAG,SAAS,QAAQ,KAAK;QACrC,IAAI,UAAU,KAAK,GAAG;YACpB,QAAQ,IAAI,CAAC,SAAS;QACxB;QACA,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,SAAS,CAAC,EAAE,KAAK,CAAC,UAAU,MAAM,CAAC,OAAO,CAAC;IAC1F;IACA;;;GAGC,GACD,OAAO,yBAAyB,GAAG,SAAS,0BAA0B,aAAa;QACjF,IAAI,kBAAkB,KAAK,GAAG;YAC5B,gBAAgB,IAAI,CAAC,QAAQ,GAAG;QAClC;QACA,IAAI,iBAAiB;QACrB,MAAO,iBAAiB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAE;YAC1C,IAAI,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;gBAC/E;gBACA;YACF,OAAO;gBACL,OAAO;YACT;QACF;QACA,OAAO,CAAC;IACV;IACA,aAAa,QAAQ;QAAC;YACpB,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACnC;QACF;QAAG;YACD,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE;YACvC;QACF;QAAG;YACD,KAAK;YACL,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE;YACvC;QACF;KAAE;IACF,OAAO;AACT;AACA,OAAO,CAAC,UAAU,GAAG;AACrB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3281, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/processor.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _parser = _interopRequireDefault(require(\"./parser\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nvar Processor = /*#__PURE__*/function () {\n  function Processor(func, options) {\n    this.func = func || function noop() {};\n    this.funcRes = null;\n    this.options = options;\n  }\n  var _proto = Processor.prototype;\n  _proto._shouldUpdateSelector = function _shouldUpdateSelector(rule, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var merged = Object.assign({}, this.options, options);\n    if (merged.updateSelector === false) {\n      return false;\n    } else {\n      return typeof rule !== \"string\";\n    }\n  };\n  _proto._isLossy = function _isLossy(options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var merged = Object.assign({}, this.options, options);\n    if (merged.lossless === false) {\n      return true;\n    } else {\n      return false;\n    }\n  };\n  _proto._root = function _root(rule, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var parser = new _parser[\"default\"](rule, this._parseOptions(options));\n    return parser.root;\n  };\n  _proto._parseOptions = function _parseOptions(options) {\n    return {\n      lossy: this._isLossy(options)\n    };\n  };\n  _proto._run = function _run(rule, options) {\n    var _this = this;\n    if (options === void 0) {\n      options = {};\n    }\n    return new Promise(function (resolve, reject) {\n      try {\n        var root = _this._root(rule, options);\n        Promise.resolve(_this.func(root)).then(function (transform) {\n          var string = undefined;\n          if (_this._shouldUpdateSelector(rule, options)) {\n            string = root.toString();\n            rule.selector = string;\n          }\n          return {\n            transform: transform,\n            root: root,\n            string: string\n          };\n        }).then(resolve, reject);\n      } catch (e) {\n        reject(e);\n        return;\n      }\n    });\n  };\n  _proto._runSync = function _runSync(rule, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var root = this._root(rule, options);\n    var transform = this.func(root);\n    if (transform && typeof transform.then === \"function\") {\n      throw new Error(\"Selector processor returned a promise to a synchronous call.\");\n    }\n    var string = undefined;\n    if (options.updateSelector && typeof rule !== \"string\") {\n      string = root.toString();\n      rule.selector = string;\n    }\n    return {\n      transform: transform,\n      root: root,\n      string: string\n    };\n  }\n\n  /**\n   * Process rule into a selector AST.\n   *\n   * @param rule {postcss.Rule | string} The css selector to be processed\n   * @param options The options for processing\n   * @returns {Promise<parser.Root>} The AST of the selector after processing it.\n   */;\n  _proto.ast = function ast(rule, options) {\n    return this._run(rule, options).then(function (result) {\n      return result.root;\n    });\n  }\n\n  /**\n   * Process rule into a selector AST synchronously.\n   *\n   * @param rule {postcss.Rule | string} The css selector to be processed\n   * @param options The options for processing\n   * @returns {parser.Root} The AST of the selector after processing it.\n   */;\n  _proto.astSync = function astSync(rule, options) {\n    return this._runSync(rule, options).root;\n  }\n\n  /**\n   * Process a selector into a transformed value asynchronously\n   *\n   * @param rule {postcss.Rule | string} The css selector to be processed\n   * @param options The options for processing\n   * @returns {Promise<any>} The value returned by the processor.\n   */;\n  _proto.transform = function transform(rule, options) {\n    return this._run(rule, options).then(function (result) {\n      return result.transform;\n    });\n  }\n\n  /**\n   * Process a selector into a transformed value synchronously.\n   *\n   * @param rule {postcss.Rule | string} The css selector to be processed\n   * @param options The options for processing\n   * @returns {any} The value returned by the processor.\n   */;\n  _proto.transformSync = function transformSync(rule, options) {\n    return this._runSync(rule, options).transform;\n  }\n\n  /**\n   * Process a selector into a new selector string asynchronously.\n   *\n   * @param rule {postcss.Rule | string} The css selector to be processed\n   * @param options The options for processing\n   * @returns {string} the selector after processing.\n   */;\n  _proto.process = function process(rule, options) {\n    return this._run(rule, options).then(function (result) {\n      return result.string || result.root.toString();\n    });\n  }\n\n  /**\n   * Process a selector into a new selector string synchronously.\n   *\n   * @param rule {postcss.Rule | string} The css selector to be processed\n   * @param options The options for processing\n   * @returns {string} the selector after processing.\n   */;\n  _proto.processSync = function processSync(rule, options) {\n    var result = this._runSync(rule, options);\n    return result.string || result.root.toString();\n  };\n  return Processor;\n}();\nexports[\"default\"] = Processor;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,UAAU;AACd,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,IAAI,YAAY,WAAW,GAAE;IAC3B,SAAS,UAAU,IAAI,EAAE,OAAO;QAC9B,IAAI,CAAC,IAAI,GAAG,QAAQ,SAAS,QAAQ;QACrC,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;IACjB;IACA,IAAI,SAAS,UAAU,SAAS;IAChC,OAAO,qBAAqB,GAAG,SAAS,sBAAsB,IAAI,EAAE,OAAO;QACzE,IAAI,YAAY,KAAK,GAAG;YACtB,UAAU,CAAC;QACb;QACA,IAAI,SAAS,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE;QAC7C,IAAI,OAAO,cAAc,KAAK,OAAO;YACnC,OAAO;QACT,OAAO;YACL,OAAO,OAAO,SAAS;QACzB;IACF;IACA,OAAO,QAAQ,GAAG,SAAS,SAAS,OAAO;QACzC,IAAI,YAAY,KAAK,GAAG;YACtB,UAAU,CAAC;QACb;QACA,IAAI,SAAS,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE;QAC7C,IAAI,OAAO,QAAQ,KAAK,OAAO;YAC7B,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IACA,OAAO,KAAK,GAAG,SAAS,MAAM,IAAI,EAAE,OAAO;QACzC,IAAI,YAAY,KAAK,GAAG;YACtB,UAAU,CAAC;QACb;QACA,IAAI,SAAS,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC;QAC7D,OAAO,OAAO,IAAI;IACpB;IACA,OAAO,aAAa,GAAG,SAAS,cAAc,OAAO;QACnD,OAAO;YACL,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB;IACF;IACA,OAAO,IAAI,GAAG,SAAS,KAAK,IAAI,EAAE,OAAO;QACvC,IAAI,QAAQ,IAAI;QAChB,IAAI,YAAY,KAAK,GAAG;YACtB,UAAU,CAAC;QACb;QACA,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;YAC1C,IAAI;gBACF,IAAI,OAAO,MAAM,KAAK,CAAC,MAAM;gBAC7B,QAAQ,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,SAAU,SAAS;oBACxD,IAAI,SAAS;oBACb,IAAI,MAAM,qBAAqB,CAAC,MAAM,UAAU;wBAC9C,SAAS,KAAK,QAAQ;wBACtB,KAAK,QAAQ,GAAG;oBAClB;oBACA,OAAO;wBACL,WAAW;wBACX,MAAM;wBACN,QAAQ;oBACV;gBACF,GAAG,IAAI,CAAC,SAAS;YACnB,EAAE,OAAO,GAAG;gBACV,OAAO;gBACP;YACF;QACF;IACF;IACA,OAAO,QAAQ,GAAG,SAAS,SAAS,IAAI,EAAE,OAAO;QAC/C,IAAI,YAAY,KAAK,GAAG;YACtB,UAAU,CAAC;QACb;QACA,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;QAC5B,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,aAAa,OAAO,UAAU,IAAI,KAAK,YAAY;YACrD,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,SAAS;QACb,IAAI,QAAQ,cAAc,IAAI,OAAO,SAAS,UAAU;YACtD,SAAS,KAAK,QAAQ;YACtB,KAAK,QAAQ,GAAG;QAClB;QACA,OAAO;YACL,WAAW;YACX,MAAM;YACN,QAAQ;QACV;IACF;IASA,OAAO,GAAG,GAAG,SAAS,IAAI,IAAI,EAAE,OAAO;QACrC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,SAAU,MAAM;YACnD,OAAO,OAAO,IAAI;QACpB;IACF;IASA,OAAO,OAAO,GAAG,SAAS,QAAQ,IAAI,EAAE,OAAO;QAC7C,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,SAAS,IAAI;IAC1C;IASA,OAAO,SAAS,GAAG,SAAS,UAAU,IAAI,EAAE,OAAO;QACjD,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,SAAU,MAAM;YACnD,OAAO,OAAO,SAAS;QACzB;IACF;IASA,OAAO,aAAa,GAAG,SAAS,cAAc,IAAI,EAAE,OAAO;QACzD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,SAAS,SAAS;IAC/C;IASA,OAAO,OAAO,GAAG,SAAS,QAAQ,IAAI,EAAE,OAAO;QAC7C,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,SAAU,MAAM;YACnD,OAAO,OAAO,MAAM,IAAI,OAAO,IAAI,CAAC,QAAQ;QAC9C;IACF;IASA,OAAO,WAAW,GAAG,SAAS,YAAY,IAAI,EAAE,OAAO;QACrD,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM;QACjC,OAAO,OAAO,MAAM,IAAI,OAAO,IAAI,CAAC,QAAQ;IAC9C;IACA,OAAO;AACT;AACA,OAAO,CAAC,UAAU,GAAG;AACrB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3411, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/constructors.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.universal = exports.tag = exports.string = exports.selector = exports.root = exports.pseudo = exports.nesting = exports.id = exports.comment = exports.combinator = exports.className = exports.attribute = void 0;\nvar _attribute = _interopRequireDefault(require(\"./attribute\"));\nvar _className = _interopRequireDefault(require(\"./className\"));\nvar _combinator = _interopRequireDefault(require(\"./combinator\"));\nvar _comment = _interopRequireDefault(require(\"./comment\"));\nvar _id = _interopRequireDefault(require(\"./id\"));\nvar _nesting = _interopRequireDefault(require(\"./nesting\"));\nvar _pseudo = _interopRequireDefault(require(\"./pseudo\"));\nvar _root = _interopRequireDefault(require(\"./root\"));\nvar _selector = _interopRequireDefault(require(\"./selector\"));\nvar _string = _interopRequireDefault(require(\"./string\"));\nvar _tag = _interopRequireDefault(require(\"./tag\"));\nvar _universal = _interopRequireDefault(require(\"./universal\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nvar attribute = function attribute(opts) {\n  return new _attribute[\"default\"](opts);\n};\nexports.attribute = attribute;\nvar className = function className(opts) {\n  return new _className[\"default\"](opts);\n};\nexports.className = className;\nvar combinator = function combinator(opts) {\n  return new _combinator[\"default\"](opts);\n};\nexports.combinator = combinator;\nvar comment = function comment(opts) {\n  return new _comment[\"default\"](opts);\n};\nexports.comment = comment;\nvar id = function id(opts) {\n  return new _id[\"default\"](opts);\n};\nexports.id = id;\nvar nesting = function nesting(opts) {\n  return new _nesting[\"default\"](opts);\n};\nexports.nesting = nesting;\nvar pseudo = function pseudo(opts) {\n  return new _pseudo[\"default\"](opts);\n};\nexports.pseudo = pseudo;\nvar root = function root(opts) {\n  return new _root[\"default\"](opts);\n};\nexports.root = root;\nvar selector = function selector(opts) {\n  return new _selector[\"default\"](opts);\n};\nexports.selector = selector;\nvar string = function string(opts) {\n  return new _string[\"default\"](opts);\n};\nexports.string = string;\nvar tag = function tag(opts) {\n  return new _tag[\"default\"](opts);\n};\nexports.tag = tag;\nvar universal = function universal(opts) {\n  return new _universal[\"default\"](opts);\n};\nexports.universal = universal;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,SAAS,GAAG,QAAQ,GAAG,GAAG,QAAQ,MAAM,GAAG,QAAQ,QAAQ,GAAG,QAAQ,IAAI,GAAG,QAAQ,MAAM,GAAG,QAAQ,OAAO,GAAG,QAAQ,EAAE,GAAG,QAAQ,OAAO,GAAG,QAAQ,UAAU,GAAG,QAAQ,SAAS,GAAG,QAAQ,SAAS,GAAG,KAAK;AACzN,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,IAAI,MAAM;AACV,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,IAAI,OAAO;AACX,IAAI,aAAa;AACjB,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,IAAI,YAAY,SAAS,UAAU,IAAI;IACrC,OAAO,IAAI,UAAU,CAAC,UAAU,CAAC;AACnC;AACA,QAAQ,SAAS,GAAG;AACpB,IAAI,YAAY,SAAS,UAAU,IAAI;IACrC,OAAO,IAAI,UAAU,CAAC,UAAU,CAAC;AACnC;AACA,QAAQ,SAAS,GAAG;AACpB,IAAI,aAAa,SAAS,WAAW,IAAI;IACvC,OAAO,IAAI,WAAW,CAAC,UAAU,CAAC;AACpC;AACA,QAAQ,UAAU,GAAG;AACrB,IAAI,UAAU,SAAS,QAAQ,IAAI;IACjC,OAAO,IAAI,QAAQ,CAAC,UAAU,CAAC;AACjC;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI,KAAK,SAAS,GAAG,IAAI;IACvB,OAAO,IAAI,GAAG,CAAC,UAAU,CAAC;AAC5B;AACA,QAAQ,EAAE,GAAG;AACb,IAAI,UAAU,SAAS,QAAQ,IAAI;IACjC,OAAO,IAAI,QAAQ,CAAC,UAAU,CAAC;AACjC;AACA,QAAQ,OAAO,GAAG;AAClB,IAAI,SAAS,SAAS,OAAO,IAAI;IAC/B,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC;AAChC;AACA,QAAQ,MAAM,GAAG;AACjB,IAAI,OAAO,SAAS,KAAK,IAAI;IAC3B,OAAO,IAAI,KAAK,CAAC,UAAU,CAAC;AAC9B;AACA,QAAQ,IAAI,GAAG;AACf,IAAI,WAAW,SAAS,SAAS,IAAI;IACnC,OAAO,IAAI,SAAS,CAAC,UAAU,CAAC;AAClC;AACA,QAAQ,QAAQ,GAAG;AACnB,IAAI,SAAS,SAAS,OAAO,IAAI;IAC/B,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC;AAChC;AACA,QAAQ,MAAM,GAAG;AACjB,IAAI,MAAM,SAAS,IAAI,IAAI;IACzB,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC;AAC7B;AACA,QAAQ,GAAG,GAAG;AACd,IAAI,YAAY,SAAS,UAAU,IAAI;IACrC,OAAO,IAAI,UAAU,CAAC,UAAU,CAAC;AACnC;AACA,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/guards.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.isComment = exports.isCombinator = exports.isClassName = exports.isAttribute = void 0;\nexports.isContainer = isContainer;\nexports.isIdentifier = void 0;\nexports.isNamespace = isNamespace;\nexports.isNesting = void 0;\nexports.isNode = isNode;\nexports.isPseudo = void 0;\nexports.isPseudoClass = isPseudoClass;\nexports.isPseudoElement = isPseudoElement;\nexports.isUniversal = exports.isTag = exports.isString = exports.isSelector = exports.isRoot = void 0;\nvar _types = require(\"./types\");\nvar _IS_TYPE;\nvar IS_TYPE = (_IS_TYPE = {}, _IS_TYPE[_types.ATTRIBUTE] = true, _IS_TYPE[_types.CLASS] = true, _IS_TYPE[_types.COMBINATOR] = true, _IS_TYPE[_types.COMMENT] = true, _IS_TYPE[_types.ID] = true, _IS_TYPE[_types.NESTING] = true, _IS_TYPE[_types.PSEUDO] = true, _IS_TYPE[_types.ROOT] = true, _IS_TYPE[_types.SELECTOR] = true, _IS_TYPE[_types.STRING] = true, _IS_TYPE[_types.TAG] = true, _IS_TYPE[_types.UNIVERSAL] = true, _IS_TYPE);\nfunction isNode(node) {\n  return typeof node === \"object\" && IS_TYPE[node.type];\n}\nfunction isNodeType(type, node) {\n  return isNode(node) && node.type === type;\n}\nvar isAttribute = isNodeType.bind(null, _types.ATTRIBUTE);\nexports.isAttribute = isAttribute;\nvar isClassName = isNodeType.bind(null, _types.CLASS);\nexports.isClassName = isClassName;\nvar isCombinator = isNodeType.bind(null, _types.COMBINATOR);\nexports.isCombinator = isCombinator;\nvar isComment = isNodeType.bind(null, _types.COMMENT);\nexports.isComment = isComment;\nvar isIdentifier = isNodeType.bind(null, _types.ID);\nexports.isIdentifier = isIdentifier;\nvar isNesting = isNodeType.bind(null, _types.NESTING);\nexports.isNesting = isNesting;\nvar isPseudo = isNodeType.bind(null, _types.PSEUDO);\nexports.isPseudo = isPseudo;\nvar isRoot = isNodeType.bind(null, _types.ROOT);\nexports.isRoot = isRoot;\nvar isSelector = isNodeType.bind(null, _types.SELECTOR);\nexports.isSelector = isSelector;\nvar isString = isNodeType.bind(null, _types.STRING);\nexports.isString = isString;\nvar isTag = isNodeType.bind(null, _types.TAG);\nexports.isTag = isTag;\nvar isUniversal = isNodeType.bind(null, _types.UNIVERSAL);\nexports.isUniversal = isUniversal;\nfunction isPseudoElement(node) {\n  return isPseudo(node) && node.value && (node.value.startsWith(\"::\") || node.value.toLowerCase() === \":before\" || node.value.toLowerCase() === \":after\" || node.value.toLowerCase() === \":first-letter\" || node.value.toLowerCase() === \":first-line\");\n}\nfunction isPseudoClass(node) {\n  return isPseudo(node) && !isPseudoElement(node);\n}\nfunction isContainer(node) {\n  return !!(isNode(node) && node.walk);\n}\nfunction isNamespace(node) {\n  return isAttribute(node) || isTag(node);\n}"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,QAAQ,SAAS,GAAG,QAAQ,YAAY,GAAG,QAAQ,WAAW,GAAG,QAAQ,WAAW,GAAG,KAAK;AAC5F,QAAQ,WAAW,GAAG;AACtB,QAAQ,YAAY,GAAG,KAAK;AAC5B,QAAQ,WAAW,GAAG;AACtB,QAAQ,SAAS,GAAG,KAAK;AACzB,QAAQ,MAAM,GAAG;AACjB,QAAQ,QAAQ,GAAG,KAAK;AACxB,QAAQ,aAAa,GAAG;AACxB,QAAQ,eAAe,GAAG;AAC1B,QAAQ,WAAW,GAAG,QAAQ,KAAK,GAAG,QAAQ,QAAQ,GAAG,QAAQ,UAAU,GAAG,QAAQ,MAAM,GAAG,KAAK;AACpG,IAAI;AACJ,IAAI;AACJ,IAAI,UAAU,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,OAAO,SAAS,CAAC,GAAG,MAAM,QAAQ,CAAC,OAAO,KAAK,CAAC,GAAG,MAAM,QAAQ,CAAC,OAAO,UAAU,CAAC,GAAG,MAAM,QAAQ,CAAC,OAAO,OAAO,CAAC,GAAG,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAC,GAAG,MAAM,QAAQ,CAAC,OAAO,OAAO,CAAC,GAAG,MAAM,QAAQ,CAAC,OAAO,MAAM,CAAC,GAAG,MAAM,QAAQ,CAAC,OAAO,IAAI,CAAC,GAAG,MAAM,QAAQ,CAAC,OAAO,QAAQ,CAAC,GAAG,MAAM,QAAQ,CAAC,OAAO,MAAM,CAAC,GAAG,MAAM,QAAQ,CAAC,OAAO,GAAG,CAAC,GAAG,MAAM,QAAQ,CAAC,OAAO,SAAS,CAAC,GAAG,MAAM,QAAQ;AAC1a,SAAS,OAAO,IAAI;IAClB,OAAO,OAAO,SAAS,YAAY,OAAO,CAAC,KAAK,IAAI,CAAC;AACvD;AACA,SAAS,WAAW,IAAI,EAAE,IAAI;IAC5B,OAAO,OAAO,SAAS,KAAK,IAAI,KAAK;AACvC;AACA,IAAI,cAAc,WAAW,IAAI,CAAC,MAAM,OAAO,SAAS;AACxD,QAAQ,WAAW,GAAG;AACtB,IAAI,cAAc,WAAW,IAAI,CAAC,MAAM,OAAO,KAAK;AACpD,QAAQ,WAAW,GAAG;AACtB,IAAI,eAAe,WAAW,IAAI,CAAC,MAAM,OAAO,UAAU;AAC1D,QAAQ,YAAY,GAAG;AACvB,IAAI,YAAY,WAAW,IAAI,CAAC,MAAM,OAAO,OAAO;AACpD,QAAQ,SAAS,GAAG;AACpB,IAAI,eAAe,WAAW,IAAI,CAAC,MAAM,OAAO,EAAE;AAClD,QAAQ,YAAY,GAAG;AACvB,IAAI,YAAY,WAAW,IAAI,CAAC,MAAM,OAAO,OAAO;AACpD,QAAQ,SAAS,GAAG;AACpB,IAAI,WAAW,WAAW,IAAI,CAAC,MAAM,OAAO,MAAM;AAClD,QAAQ,QAAQ,GAAG;AACnB,IAAI,SAAS,WAAW,IAAI,CAAC,MAAM,OAAO,IAAI;AAC9C,QAAQ,MAAM,GAAG;AACjB,IAAI,aAAa,WAAW,IAAI,CAAC,MAAM,OAAO,QAAQ;AACtD,QAAQ,UAAU,GAAG;AACrB,IAAI,WAAW,WAAW,IAAI,CAAC,MAAM,OAAO,MAAM;AAClD,QAAQ,QAAQ,GAAG;AACnB,IAAI,QAAQ,WAAW,IAAI,CAAC,MAAM,OAAO,GAAG;AAC5C,QAAQ,KAAK,GAAG;AAChB,IAAI,cAAc,WAAW,IAAI,CAAC,MAAM,OAAO,SAAS;AACxD,QAAQ,WAAW,GAAG;AACtB,SAAS,gBAAgB,IAAI;IAC3B,OAAO,SAAS,SAAS,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,UAAU,CAAC,SAAS,KAAK,KAAK,CAAC,WAAW,OAAO,aAAa,KAAK,KAAK,CAAC,WAAW,OAAO,YAAY,KAAK,KAAK,CAAC,WAAW,OAAO,mBAAmB,KAAK,KAAK,CAAC,WAAW,OAAO,aAAa;AACtP;AACA,SAAS,cAAc,IAAI;IACzB,OAAO,SAAS,SAAS,CAAC,gBAAgB;AAC5C;AACA,SAAS,YAAY,IAAI;IACvB,OAAO,CAAC,CAAC,CAAC,OAAO,SAAS,KAAK,IAAI;AACrC;AACA,SAAS,YAAY,IAAI;IACvB,OAAO,YAAY,SAAS,MAAM;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/selectors/index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nvar _types = require(\"./types\");\nObject.keys(_types).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (key in exports && exports[key] === _types[key]) return;\n  exports[key] = _types[key];\n});\nvar _constructors = require(\"./constructors\");\nObject.keys(_constructors).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (key in exports && exports[key] === _constructors[key]) return;\n  exports[key] = _constructors[key];\n});\nvar _guards = require(\"./guards\");\nObject.keys(_guards).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (key in exports && exports[key] === _guards[key]) return;\n  exports[key] = _guards[key];\n});"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,IAAI;AACJ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,SAAU,GAAG;IACvC,IAAI,QAAQ,aAAa,QAAQ,cAAc;IAC/C,IAAI,OAAO,WAAW,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,EAAE;IACpD,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B;AACA,IAAI;AACJ,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,SAAU,GAAG;IAC9C,IAAI,QAAQ,aAAa,QAAQ,cAAc;IAC/C,IAAI,OAAO,WAAW,OAAO,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,EAAE;IAC3D,OAAO,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI;AACnC;AACA,IAAI;AACJ,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,SAAU,GAAG;IACxC,IAAI,QAAQ,aAAa,QAAQ,cAAc;IAC/C,IAAI,OAAO,WAAW,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,EAAE;IACrD,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3571, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/factorygame/node_modules/postcss-selector-parser/dist/index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _processor = _interopRequireDefault(require(\"./processor\"));\nvar selectors = _interopRequireWildcard(require(\"./selectors\"));\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nvar parser = function parser(processor) {\n  return new _processor[\"default\"](processor);\n};\nObject.assign(parser, selectors);\ndelete parser.__esModule;\nvar _default = parser;\nexports[\"default\"] = _default;\nmodule.exports = exports.default;"], "names": [], "mappings": "AAAA;AAEA,QAAQ,UAAU,GAAG;AACrB,OAAO,CAAC,UAAU,GAAG,KAAK;AAC1B,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,SAAS,yBAAyB,WAAW;IAAI,IAAI,OAAO,YAAY,YAAY,OAAO;IAAM,IAAI,oBAAoB,IAAI;IAAW,IAAI,mBAAmB,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAS,yBAAyB,WAAW;QAAI,OAAO,cAAc,mBAAmB;IAAmB,CAAC,EAAE;AAAc;AAC9U,SAAS,wBAAwB,GAAG,EAAE,WAAW;IAAI,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE;QAAE,OAAO;IAAK;IAAE,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY;QAAE,OAAO;YAAE,WAAW;QAAI;IAAG;IAAE,IAAI,QAAQ,yBAAyB;IAAc,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM;QAAE,OAAO,MAAM,GAAG,CAAC;IAAM;IAAE,IAAI,SAAS,CAAC;IAAG,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,OAAO,IAAK;QAAE,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YAAE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAAM,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG;gBAAE,OAAO,cAAc,CAAC,QAAQ,KAAK;YAAO,OAAO;gBAAE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;YAAE;QAAE;IAAE;IAAE,MAAM,CAAC,UAAU,GAAG;IAAK,IAAI,OAAO;QAAE,MAAM,GAAG,CAAC,KAAK;IAAS;IAAE,OAAO;AAAQ;AACxyB,SAAS,uBAAuB,GAAG;IAAI,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,WAAW;IAAI;AAAG;AAChG,IAAI,SAAS,SAAS,OAAO,SAAS;IACpC,OAAO,IAAI,UAAU,CAAC,UAAU,CAAC;AACnC;AACA,OAAO,MAAM,CAAC,QAAQ;AACtB,OAAO,OAAO,UAAU;AACxB,IAAI,WAAW;AACf,OAAO,CAAC,UAAU,GAAG;AACrB,OAAO,OAAO,GAAG,QAAQ,OAAO", "ignoreList": [0], "debugId": null}}]}